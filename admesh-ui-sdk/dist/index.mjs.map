{"version": 3, "file": "index.mjs", "sources": ["../node_modules/react/cjs/react-jsx-runtime.production.js", "../node_modules/react/cjs/react-jsx-runtime.development.js", "../node_modules/react/jsx-runtime.js", "../node_modules/classnames/index.js", "../src/components/AdMeshBadge.tsx", "../src/hooks/useAdMeshTracker.ts", "../src/components/AdMeshLinkTracker.tsx", "../src/components/AdMeshProductCard.tsx", "../src/components/AdMeshCompareTable.tsx", "../src/components/AdMeshLayout.tsx", "../src/index.ts"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config)\n      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;\n", "/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsx = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !1,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n    exports.jsxs = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !0,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshBadgeProps, BadgeType } from '../types/index';\n\n// Badge type to variant mapping\nconst badgeTypeVariants: Record<BadgeType, string> = {\n  'Top Match': 'primary',\n  'Free Tier': 'success',\n  'AI Powered': 'secondary',\n  'Popular': 'warning',\n  'New': 'primary',\n  'Trial Available': 'success'\n};\n\n// Badge type to icon mapping (using simple text icons for now)\nconst badgeTypeIcons: Partial<Record<BadgeType, string>> = {\n  'Top Match': '⭐',\n  'Free Tier': '🆓',\n  'AI Powered': '🤖',\n  'Popular': '🔥',\n  'New': '✨',\n  'Trial Available': '🎯'\n};\n\nexport const AdMeshBadge: React.FC<AdMeshBadgeProps> = ({\n  type,\n  variant,\n  size = 'md',\n  className\n}) => {\n  const effectiveVariant = variant || badgeTypeVariants[type] || 'secondary';\n  const icon = badgeTypeIcons[type];\n\n  const badgeClasses = classNames(\n    'admesh-component',\n    'admesh-badge',\n    `admesh-badge--${effectiveVariant}`,\n    `admesh-badge--${size}`,\n    className\n  );\n\n  return (\n    <span className={badgeClasses}>\n      {icon && <span className=\"admesh-badge__icon\">{icon}</span>}\n      <span className=\"admesh-badge__text\">{type}</span>\n    </span>\n  );\n};\n\nAdMeshBadge.displayName = 'AdMeshBadge';\n", "import { useState, useCallback } from 'react';\nimport type { TrackingData, UseAdMeshTrackerReturn } from '../types/index';\n\n// Default tracking endpoint - can be overridden via config\nconst DEFAULT_TRACKING_URL = 'https://api.useadmesh.com/track';\n\ninterface TrackingConfig {\n  apiBaseUrl?: string;\n  enabled?: boolean;\n  debug?: boolean;\n  retryAttempts?: number;\n  retryDelay?: number;\n}\n\n// Global config that can be set by the consuming application\nlet globalConfig: TrackingConfig = {\n  apiBaseUrl: DEFAULT_TRACKING_URL,\n  enabled: true,\n  debug: false,\n  retryAttempts: 3,\n  retryDelay: 1000\n};\n\nexport const setAdMeshTrackerConfig = (config: Partial<TrackingConfig>) => {\n  globalConfig = { ...globalConfig, ...config };\n};\n\nexport const useAdMeshTracker = (config?: Partial<TrackingConfig>): UseAdMeshTrackerReturn => {\n  const [isTracking, setIsTracking] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const mergedConfig = { ...globalConfig, ...config };\n\n  const log = useCallback((message: string, data?: any) => {\n    if (mergedConfig.debug) {\n      console.log(`[AdMesh Tracker] ${message}`, data);\n    }\n  }, [mergedConfig.debug]);\n\n  const sendTrackingEvent = useCallback(async (\n    eventType: 'click' | 'view' | 'conversion',\n    data: TrackingData\n  ): Promise<void> => {\n    if (!mergedConfig.enabled) {\n      log('Tracking disabled, skipping event', { eventType, data });\n      return;\n    }\n\n    if (!data.adId || !data.admeshLink) {\n      const errorMsg = 'Missing required tracking data: adId and admeshLink are required';\n      log(errorMsg, data);\n      setError(errorMsg);\n      return;\n    }\n\n    setIsTracking(true);\n    setError(null);\n\n    const payload = {\n      event_type: eventType,\n      ad_id: data.adId,\n      admesh_link: data.admeshLink,\n      product_id: data.productId,\n      user_id: data.userId,\n      session_id: data.sessionId,\n      revenue: data.revenue,\n      conversion_type: data.conversionType,\n      metadata: data.metadata,\n      timestamp: new Date().toISOString(),\n      user_agent: navigator.userAgent,\n      referrer: document.referrer,\n      page_url: window.location.href\n    };\n\n    log(`Sending ${eventType} event`, payload);\n\n    let lastError: Error | null = null;\n    \n    for (let attempt = 1; attempt <= (mergedConfig.retryAttempts || 3); attempt++) {\n      try {\n        const response = await fetch(`${mergedConfig.apiBaseUrl}/events`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(payload),\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const result = await response.json();\n        log(`${eventType} event tracked successfully`, result);\n        setIsTracking(false);\n        return;\n\n      } catch (err) {\n        lastError = err as Error;\n        log(`Attempt ${attempt} failed for ${eventType} event`, err);\n        \n        if (attempt < (mergedConfig.retryAttempts || 3)) {\n          await new Promise(resolve => \n            setTimeout(resolve, (mergedConfig.retryDelay || 1000) * attempt)\n          );\n        }\n      }\n    }\n\n    // All attempts failed\n    const errorMsg = `Failed to track ${eventType} event after ${mergedConfig.retryAttempts} attempts: ${lastError?.message}`;\n    log(errorMsg, lastError);\n    setError(errorMsg);\n    setIsTracking(false);\n  }, [mergedConfig, log]);\n\n  const trackClick = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('click', data);\n  }, [sendTrackingEvent]);\n\n  const trackView = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('view', data);\n  }, [sendTrackingEvent]);\n\n  const trackConversion = useCallback(async (data: TrackingData): Promise<void> => {\n    if (!data.revenue && !data.conversionType) {\n      log('Warning: Conversion tracking without revenue or conversion type', data);\n    }\n    return sendTrackingEvent('conversion', data);\n  }, [sendTrackingEvent]);\n\n  return {\n    trackClick,\n    trackView,\n    trackConversion,\n    isTracking,\n    error\n  };\n};\n\n// Utility function to build admesh_link with tracking parameters\nexport const buildAdMeshLink = (\n  baseLink: string, \n  adId: string, \n  additionalParams?: Record<string, string>\n): string => {\n  try {\n    const url = new URL(baseLink);\n    url.searchParams.set('ad_id', adId);\n    url.searchParams.set('utm_source', 'admesh');\n    url.searchParams.set('utm_medium', 'recommendation');\n    \n    if (additionalParams) {\n      Object.entries(additionalParams).forEach(([key, value]) => {\n        url.searchParams.set(key, value);\n      });\n    }\n    \n    return url.toString();\n  } catch (error) {\n    console.warn('[AdMesh] Invalid URL provided to buildAdMeshLink:', baseLink);\n    return baseLink;\n  }\n};\n\n// Helper function to extract tracking data from recommendation\nexport const extractTrackingData = (\n  recommendation: { ad_id: string; admesh_link: string; product_id: string },\n  additionalData?: Partial<TrackingData>\n): TrackingData => {\n  return {\n    adId: recommendation.ad_id,\n    admeshLink: recommendation.admesh_link,\n    productId: recommendation.product_id,\n    ...additionalData\n  };\n};\n", "import React, { useCallback, useEffect, useRef } from 'react';\nimport type { AdMeshLinkTrackerProps } from '../types/index';\nimport { useAdMeshTracker, extractTrackingData } from '../hooks/useAdMeshTracker';\n\nexport const AdMeshLinkTracker: React.FC<AdMeshLinkTrackerProps> = ({\n  adId,\n  admeshLink,\n  productId,\n  children,\n  onClick,\n  trackingData,\n  className\n}) => {\n  const { trackClick, trackView } = useAdMeshTracker();\n  const elementRef = useRef<HTMLDivElement>(null);\n  const hasTrackedView = useRef(false);\n\n  // Track view when component becomes visible\n  useEffect(() => {\n    if (!elementRef.current || hasTrackedView.current) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting && !hasTrackedView.current) {\n            hasTrackedView.current = true;\n            trackView({\n              adId,\n              admeshLink,\n              productId,\n              ...trackingData\n            }).catch(console.error);\n          }\n        });\n      },\n      {\n        threshold: 0.5, // Track when 50% of the element is visible\n        rootMargin: '0px'\n      }\n    );\n\n    observer.observe(elementRef.current);\n\n    return () => {\n      observer.disconnect();\n    };\n  }, [adId, admeshLink, productId, trackingData, trackView]);\n\n  const handleClick = useCallback(async (event: React.MouseEvent) => {\n    // Track the click\n    try {\n      await trackClick({\n        adId,\n        admeshLink,\n        productId,\n        ...trackingData\n      });\n    } catch (error) {\n      console.error('Failed to track click:', error);\n    }\n\n    // Call custom onClick handler if provided\n    if (onClick) {\n      onClick();\n    }\n\n    // If the children contain a link, let the browser handle navigation\n    // Otherwise, navigate programmatically\n    const target = event.target as HTMLElement;\n    const link = target.closest('a');\n    \n    if (!link) {\n      // No link found, navigate programmatically\n      window.open(admeshLink, '_blank', 'noopener,noreferrer');\n    }\n    // If there's a link, let the browser handle it naturally\n  }, [adId, admeshLink, productId, trackingData, trackClick, onClick]);\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      onClick={handleClick}\n      style={{ cursor: 'pointer' }}\n    >\n      {children}\n    </div>\n  );\n};\n\nAdMeshLinkTracker.displayName = 'AdMeshLinkTracker';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshProductCardProps, BadgeType } from '../types/index';\nimport { AdMeshBadge } from './AdMeshBadge';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshProductCard: React.FC<AdMeshProductCardProps> = ({\n  recommendation,\n  theme,\n  showMatchScore = true,\n  showBadges = true,\n  maxKeywords = 3,\n  onClick,\n  onTrackView,\n  className\n}) => {\n  // Generate badges based on recommendation data\n  const badges = useMemo((): BadgeType[] => {\n    const generatedBadges: BadgeType[] = [];\n    \n    // Add Top Match badge for high match scores\n    if (recommendation.intent_match_score >= 0.8) {\n      generatedBadges.push('Top Match');\n    }\n    \n    // Add Free Tier badge\n    if (recommendation.has_free_tier) {\n      generatedBadges.push('Free Tier');\n    }\n    \n    // Add Trial Available badge\n    if (recommendation.trial_days && recommendation.trial_days > 0) {\n      generatedBadges.push('Trial Available');\n    }\n    \n    // Add AI Powered badge (check if AI-related keywords exist)\n    const aiKeywords = ['ai', 'artificial intelligence', 'machine learning', 'ml', 'automation'];\n    const hasAIKeywords = recommendation.keywords?.some(keyword => \n      aiKeywords.some(ai => keyword.toLowerCase().includes(ai))\n    ) || recommendation.title.toLowerCase().includes('ai');\n    \n    if (hasAIKeywords) {\n      generatedBadges.push('AI Powered');\n    }\n    \n    return generatedBadges;\n  }, [recommendation]);\n\n  // Format match score as percentage\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  // Limit keywords display\n  const displayKeywords = recommendation.keywords?.slice(0, maxKeywords) || [];\n  const hasMoreKeywords = (recommendation.keywords?.length || 0) > maxKeywords;\n\n  const cardClasses = classNames(\n    'admesh-component',\n    'admesh-card',\n    'admesh-product-card',\n    {\n      [`admesh-product-card--${theme?.mode}`]: theme?.mode,\n    },\n    className\n  );\n\n  const cardStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n    '--admesh-primary-hover': theme.accentColor + 'dd', // Add some transparency for hover\n  } as React.CSSProperties : undefined;\n\n  return (\n    <AdMeshLinkTracker\n      adId={recommendation.ad_id}\n      admeshLink={recommendation.admesh_link}\n      productId={recommendation.product_id}\n      onClick={() => onClick?.(recommendation.ad_id, recommendation.admesh_link)}\n      trackingData={{ \n        title: recommendation.title,\n        matchScore: recommendation.intent_match_score \n      }}\n      className={cardClasses}\n    >\n      <div \n        className=\"admesh-product-card__container\"\n        style={cardStyle}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Header with badges and match score */}\n        <div className=\"admesh-product-card__header\">\n          {showBadges && badges.length > 0 && (\n            <div className=\"admesh-product-card__badges\">\n              {badges.map((badge, index) => (\n                <AdMeshBadge key={`${badge}-${index}`} type={badge} size=\"sm\" />\n              ))}\n            </div>\n          )}\n          \n          {showMatchScore && (\n            <div className=\"admesh-product-card__match-score\">\n              <span className=\"admesh-text-xs admesh-text-muted\">\n                {matchScorePercentage}% match\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Main content */}\n        <div className=\"admesh-product-card__content\">\n          <h3 className=\"admesh-product-card__title admesh-text-lg admesh-font-semibold\">\n            {recommendation.title}\n          </h3>\n          \n          <p className=\"admesh-product-card__reason admesh-text-sm admesh-text-secondary\">\n            {recommendation.reason}\n          </p>\n\n          {/* Keywords */}\n          {displayKeywords.length > 0 && (\n            <div className=\"admesh-product-card__keywords\">\n              {displayKeywords.map((keyword, index) => (\n                <span \n                  key={index}\n                  className=\"admesh-product-card__keyword admesh-badge admesh-badge--secondary admesh-badge--sm\"\n                >\n                  {keyword}\n                </span>\n              ))}\n              {hasMoreKeywords && (\n                <span className=\"admesh-product-card__keyword-more admesh-text-xs admesh-text-muted\">\n                  +{(recommendation.keywords?.length || 0) - maxKeywords} more\n                </span>\n              )}\n            </div>\n          )}\n\n          {/* Additional info */}\n          <div className=\"admesh-product-card__meta\">\n            {recommendation.pricing && (\n              <div className=\"admesh-product-card__pricing admesh-text-sm\">\n                <span className=\"admesh-text-muted\">Pricing: </span>\n                <span className=\"admesh-font-medium\">{recommendation.pricing}</span>\n              </div>\n            )}\n            \n            {recommendation.trial_days && recommendation.trial_days > 0 && (\n              <div className=\"admesh-product-card__trial admesh-text-sm admesh-text-muted\">\n                {recommendation.trial_days}-day free trial\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Footer with CTA */}\n        <div className=\"admesh-product-card__footer\">\n          <button className=\"admesh-button admesh-button--primary admesh-product-card__cta\">\n            Visit Offer\n            <span className=\"admesh-sr-only\">\n              for {recommendation.title}\n            </span>\n          </button>\n        </div>\n      </div>\n    </AdMeshLinkTracker>\n  );\n};\n\nAdMeshProductCard.displayName = 'AdMeshProductCard';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCompareTableProps } from '../types/index';\nimport { AdMeshBadge } from './AdMeshBadge';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshCompareTable: React.FC<AdMeshCompareTableProps> = ({\n  recommendations,\n  theme,\n  maxProducts = 3,\n  showMatchScores = true,\n  showFeatures = true,\n  onProductClick,\n  className\n}) => {\n  // Limit the number of products to compare\n  const productsToCompare = useMemo(() => {\n    return recommendations.slice(0, maxProducts);\n  }, [recommendations, maxProducts]);\n\n  // Extract all unique features across products\n  const allFeatures = useMemo(() => {\n    const featuresSet = new Set<string>();\n    productsToCompare.forEach(product => {\n      product.features?.forEach(feature => featuresSet.add(feature));\n    });\n    return Array.from(featuresSet).slice(0, 8); // Limit to 8 features for readability\n  }, [productsToCompare]);\n\n  const tableClasses = classNames(\n    'admesh-component',\n    'admesh-compare-table',\n    {\n      [`admesh-compare-table--${theme?.mode}`]: theme?.mode,\n    },\n    className\n  );\n\n  const tableStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (productsToCompare.length === 0) {\n    return (\n      <div className={tableClasses}>\n        <div className=\"admesh-compare-table__empty\">\n          <p className=\"admesh-text-muted\">No products to compare</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div \n      className={tableClasses}\n      style={tableStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"admesh-compare-table__container\">\n        <div className=\"admesh-compare-table__header\">\n          <h3 className=\"admesh-compare-table__title admesh-text-xl admesh-font-semibold\">\n            Product Comparison\n          </h3>\n          <p className=\"admesh-compare-table__subtitle admesh-text-sm admesh-text-muted\">\n            Compare {productsToCompare.length} products side by side\n          </p>\n        </div>\n\n        <div className=\"admesh-compare-table__scroll-container\">\n          <table className=\"admesh-compare-table__table\">\n            <thead>\n              <tr>\n                <th className=\"admesh-compare-table__row-header\">\n                  <span className=\"admesh-sr-only\">Feature</span>\n                </th>\n                {productsToCompare.map((product, index) => (\n                  <th key={product.product_id || index} className=\"admesh-compare-table__product-header\">\n                    <AdMeshLinkTracker\n                      adId={product.ad_id}\n                      admeshLink={product.admesh_link}\n                      productId={product.product_id}\n                      onClick={() => onProductClick?.(product.ad_id, product.admesh_link)}\n                      className=\"admesh-compare-table__product-header-content\"\n                    >\n                      <div className=\"admesh-compare-table__product-title\">\n                        <h4 className=\"admesh-text-base admesh-font-semibold admesh-truncate\">\n                          {product.title}\n                        </h4>\n                        {showMatchScores && (\n                          <div className=\"admesh-compare-table__match-score\">\n                            <span className=\"admesh-text-xs admesh-text-muted\">\n                              {Math.round(product.intent_match_score * 100)}% match\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                      \n                      {/* Badges */}\n                      <div className=\"admesh-compare-table__badges\">\n                        {product.has_free_tier && (\n                          <AdMeshBadge type=\"Free Tier\" size=\"sm\" />\n                        )}\n                        {product.trial_days && product.trial_days > 0 && (\n                          <AdMeshBadge type=\"Trial Available\" size=\"sm\" />\n                        )}\n                        {product.intent_match_score >= 0.8 && (\n                          <AdMeshBadge type=\"Top Match\" size=\"sm\" />\n                        )}\n                      </div>\n\n                      <button className=\"admesh-button admesh-button--primary admesh-button--sm admesh-compare-table__cta\">\n                        Visit Offer\n                      </button>\n                    </AdMeshLinkTracker>\n                  </th>\n                ))}\n              </tr>\n            </thead>\n            \n            <tbody>\n              {/* Pricing row */}\n              <tr>\n                <td className=\"admesh-compare-table__row-header admesh-font-medium\">\n                  Pricing\n                </td>\n                {productsToCompare.map((product, index) => (\n                  <td key={product.product_id || index} className=\"admesh-compare-table__cell\">\n                    <span className=\"admesh-text-sm\">\n                      {product.pricing || 'Contact for pricing'}\n                    </span>\n                  </td>\n                ))}\n              </tr>\n\n              {/* Trial period row */}\n              <tr>\n                <td className=\"admesh-compare-table__row-header admesh-font-medium\">\n                  Free Trial\n                </td>\n                {productsToCompare.map((product, index) => (\n                  <td key={product.product_id || index} className=\"admesh-compare-table__cell\">\n                    <span className=\"admesh-text-sm\">\n                      {product.trial_days ? `${product.trial_days} days` : 'No trial'}\n                    </span>\n                  </td>\n                ))}\n              </tr>\n\n              {/* Features rows */}\n              {showFeatures && allFeatures.map((feature, featureIndex) => (\n                <tr key={featureIndex}>\n                  <td className=\"admesh-compare-table__row-header admesh-font-medium\">\n                    {feature}\n                  </td>\n                  {productsToCompare.map((product, productIndex) => (\n                    <td key={product.product_id || productIndex} className=\"admesh-compare-table__cell\">\n                      <span className=\"admesh-text-sm\">\n                        {product.features?.includes(feature) ? (\n                          <span className=\"admesh-compare-table__check\">✓</span>\n                        ) : (\n                          <span className=\"admesh-compare-table__cross\">—</span>\n                        )}\n                      </span>\n                    </td>\n                  ))}\n                </tr>\n              ))}\n\n              {/* Keywords row */}\n              <tr>\n                <td className=\"admesh-compare-table__row-header admesh-font-medium\">\n                  Keywords\n                </td>\n                {productsToCompare.map((product, index) => (\n                  <td key={product.product_id || index} className=\"admesh-compare-table__cell\">\n                    <div className=\"admesh-compare-table__keywords\">\n                      {product.keywords?.slice(0, 3).map((keyword, keywordIndex) => (\n                        <span \n                          key={keywordIndex}\n                          className=\"admesh-badge admesh-badge--secondary admesh-badge--sm\"\n                        >\n                          {keyword}\n                        </span>\n                      ))}\n                      {(product.keywords?.length || 0) > 3 && (\n                        <span className=\"admesh-text-xs admesh-text-muted\">\n                          +{(product.keywords?.length || 0) - 3}\n                        </span>\n                      )}\n                    </div>\n                  </td>\n                ))}\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAdMeshCompareTable.displayName = 'AdMeshCompareTable';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshLayoutProps, IntentType } from '../types/index';\nimport { AdMeshProductCard } from './AdMeshProductCard';\nimport { AdMeshCompareTable } from './AdMeshCompareTable';\n\n// Layout selection logic based on intent type and data characteristics\nconst selectOptimalLayout = (\n  recommendations: any[],\n  intentType?: IntentType,\n  autoLayout?: boolean\n): 'cards' | 'compare' | 'list' => {\n  if (!autoLayout && intentType) {\n    // Use explicit intent type mapping\n    switch (intentType) {\n      case 'compare_products':\n        return 'compare';\n      case 'best_for_use_case':\n      case 'trial_demo':\n      case 'budget_conscious':\n        return 'cards';\n      default:\n        return 'cards';\n    }\n  }\n\n  // Auto-layout logic based on data characteristics\n  const productCount = recommendations.length;\n  \n  // If we have 2-4 products with features, use comparison table\n  if (productCount >= 2 && productCount <= 4) {\n    const hasFeatures = recommendations.some(rec => rec.features && rec.features.length > 0);\n    const hasPricing = recommendations.some(rec => rec.pricing);\n    \n    if (hasFeatures || hasPricing) {\n      return 'compare';\n    }\n  }\n  \n  // Default to cards layout\n  return 'cards';\n};\n\nexport const AdMeshLayout: React.FC<AdMeshLayoutProps> = ({\n  recommendations,\n  intentType,\n  theme,\n  maxDisplayed = 6,\n  showMatchScores = true,\n  showFeatures = true,\n  autoLayout = true,\n  onProductClick,\n  onTrackView,\n  className\n}) => {\n  // Limit recommendations to display\n  const displayRecommendations = useMemo(() => {\n    return recommendations.slice(0, maxDisplayed);\n  }, [recommendations, maxDisplayed]);\n\n  // Determine the optimal layout\n  const layout = useMemo(() => {\n    return selectOptimalLayout(displayRecommendations, intentType, autoLayout);\n  }, [displayRecommendations, intentType, autoLayout]);\n\n  const containerClasses = classNames(\n    'admesh-component',\n    'admesh-layout',\n    `admesh-layout--${layout}`,\n    {\n      [`admesh-layout--${theme?.mode}`]: theme?.mode,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (displayRecommendations.length === 0) {\n    return (\n      <div className={containerClasses}>\n        <div className=\"admesh-layout__empty\">\n          <div className=\"admesh-layout__empty-content\">\n            <h3 className=\"admesh-text-lg admesh-font-semibold admesh-text-muted\">\n              No recommendations available\n            </h3>\n            <p className=\"admesh-text-sm admesh-text-muted\">\n              Try adjusting your search criteria or check back later.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div \n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {layout === 'compare' ? (\n        <AdMeshCompareTable\n          recommendations={displayRecommendations}\n          theme={theme}\n          maxProducts={Math.min(displayRecommendations.length, 4)}\n          showMatchScores={showMatchScores}\n          showFeatures={showFeatures}\n          onProductClick={onProductClick}\n        />\n      ) : (\n        <div className=\"admesh-layout__cards-container\">\n          {/* Header for cards layout */}\n          <div className=\"admesh-layout__header\">\n            <h3 className=\"admesh-layout__title admesh-text-xl admesh-font-semibold\">\n              Recommended Products\n            </h3>\n            <p className=\"admesh-layout__subtitle admesh-text-sm admesh-text-muted\">\n              {displayRecommendations.length} product{displayRecommendations.length !== 1 ? 's' : ''} found\n            </p>\n          </div>\n\n          {/* Cards grid */}\n          <div className=\"admesh-layout__cards-grid\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshProductCard\n                key={recommendation.product_id || recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                showMatchScore={showMatchScores}\n                showBadges={true}\n                maxKeywords={3}\n                onClick={onProductClick}\n                onTrackView={onTrackView}\n              />\n            ))}\n          </div>\n\n          {/* Show more indicator if there are more recommendations */}\n          {recommendations.length > maxDisplayed && (\n            <div className=\"admesh-layout__more-indicator\">\n              <p className=\"admesh-text-sm admesh-text-muted\">\n                Showing {maxDisplayed} of {recommendations.length} recommendations\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nAdMeshLayout.displayName = 'AdMeshLayout';\n", "// AdMesh UI SDK - Main Entry Point\n\n// Export all components\nexport {\n  AdMeshProductCard,\n  AdMeshCompareTable,\n  AdMeshBadge,\n  AdMeshLayout,\n  AdMeshLinkTracker\n} from './components';\n\n// Export hooks\nexport {\n  useAdMeshTracker,\n  setAdMeshTrackerConfig,\n  buildAdMeshLink,\n  extractTrackingData\n} from './hooks/useAdMeshTracker';\n\n// Export types\nexport type {\n  AdMeshRecommendation,\n  AdMeshTheme,\n  IntentType,\n  BadgeType,\n  BadgeVariant,\n  BadgeSize,\n  TrackingData,\n  AdMeshProductCardProps,\n  AdMeshCompareTableProps,\n  AdMeshBadgeProps,\n  AdMeshLayoutProps,\n  AdMeshLinkTrackerProps,\n  UseAdMeshTrackerReturn,\n  AgentRecommendationResponse,\n  AdMeshConfig\n} from './types/index';\n\n// Export styles (consumers can import this separately)\nimport './styles/index.css';\n\n// Version info\nexport const VERSION = '0.1.0';\n\n// Default configuration\nexport const DEFAULT_CONFIG = {\n  trackingEnabled: true,\n  debug: false,\n  theme: {\n    mode: 'light' as const,\n    accentColor: '#2563eb'\n  }\n};\n"], "names": ["REACT_ELEMENT_TYPE", "REACT_FRAGMENT_TYPE", "jsxProd", "type", "config", "<PERSON><PERSON><PERSON>", "key", "propName", "reactJsxRuntime_production", "getComponentNameFromType", "REACT_CLIENT_REFERENCE", "REACT_PROFILER_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_ACTIVITY_TYPE", "REACT_PORTAL_TYPE", "REACT_CONTEXT_TYPE", "REACT_CONSUMER_TYPE", "REACT_FORWARD_REF_TYPE", "innerType", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "testStringCoercion", "value", "checkKeyStringCoercion", "JSCompiler_inline_result", "JSCompiler_temp_const", "JSCompiler_inline_result$jscomp$0", "getTaskName", "name", "get<PERSON>wner", "dispatcher", "ReactSharedInternals", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "getter", "defineKeyPropWarningGetter", "props", "displayName", "warnAboutAccessingKey", "specialPropKeyWarningShown", "elementRefGetterWithDeprecationWarning", "componentName", "didWarnAboutElementRef", "ReactElement", "self", "source", "owner", "debugStack", "debugTask", "jsxDEVImpl", "isStaticChildren", "children", "isArrayImpl", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "k", "didWarnAboutKeySpread", "node", "React", "require$$0", "createTask", "callStackForError", "unknownOwnerDebugStack", "unknownOwnerDebugTask", "reactJsxRuntime_development", "trackActualOwner", "jsxRuntimeModule", "require$$1", "hasOwn", "classNames", "classes", "i", "arg", "appendClass", "parseValue", "newClass", "module", "badgeTypeVariants", "badgeTypeIcons", "AdMeshBadge", "variant", "size", "className", "effectiveVariant", "icon", "badgeClasses", "jsxs", "jsx", "DEFAULT_TRACKING_URL", "globalConfig", "setAdMeshTrackerConfig", "useAdMeshTracker", "isTracking", "setIsTracking", "useState", "error", "setError", "mergedConfig", "log", "useCallback", "message", "data", "sendTrackingEvent", "eventType", "errorMsg", "payload", "lastError", "attempt", "response", "result", "err", "resolve", "trackClick", "trackView", "trackConversion", "buildAdMeshLink", "baseLink", "adId", "additionalParams", "url", "extractTrackingData", "recommendation", "additionalData", "AdMeshLinkTracker", "admeshLink", "productId", "onClick", "trackingData", "elementRef", "useRef", "hasTrackedView", "useEffect", "observer", "entries", "entry", "handleClick", "event", "AdMeshProductCard", "theme", "showMatchScore", "showBadges", "maxKeywords", "onTrackView", "badges", "useMemo", "generatedBadges", "aiKeywords", "_a", "keyword", "ai", "matchScorePercentage", "displayKeywords", "hasMoreKeywords", "_b", "cardClasses", "cardStyle", "badge", "index", "_c", "AdMeshCompareTable", "recommendations", "maxProducts", "showMatchScores", "showFeatures", "onProductClick", "productsToCompare", "allFeatures", "featuresSet", "product", "feature", "tableClasses", "tableStyle", "featureIndex", "productIndex", "keywordIndex", "selectOptimalLayout", "intentType", "autoLayout", "productCount", "hasFeatures", "rec", "hasPricing", "AdMeshLayout", "maxDisplayed", "displayRecommendations", "layout", "containerClasses", "containerStyle", "VERSION", "DEFAULT_CONFIG"], "mappings": ";;;;;;;;;;;;;;;;;;AAWA,MAAIA,IAAqB,OAAO,IAAI,4BAA4B,GAC9DC,IAAsB,OAAO,IAAI,gBAAgB;AACnD,WAASC,EAAQC,GAAMC,GAAQC,GAAU;AACvC,QAAIC,IAAM;AAGV,QAFWD,MAAX,WAAwBC,IAAM,KAAKD,IACxBD,EAAO,QAAlB,WAA0BE,IAAM,KAAKF,EAAO,MACxC,SAASA,GAAQ;AACnB,MAAAC,IAAW,CAAE;AACb,eAASE,KAAYH;AACnB,QAAUG,MAAV,UAAuBF,EAASE,CAAQ,IAAIH,EAAOG,CAAQ;AAAA,IAC9D,MAAM,CAAAF,IAAWD;AAClB,WAAAA,IAASC,EAAS,KACX;AAAA,MACL,UAAUL;AAAA,MACV,MAAMG;AAAA,MACN,KAAKG;AAAA,MACL,KAAgBF,MAAX,SAAoBA,IAAS;AAAA,MAClC,OAAOC;AAAA,IACR;AAAA,EACH;AACA,SAAAG,EAAA,WAAmBP,GACnBO,EAAA,MAAcN,GACdM,EAAA,OAAeN;;;;;;;;;;;;;;wBCtBE,QAAQ,IAAI,aAA7B,gBACG,WAAY;AACX,aAASO,EAAyBN,GAAM;AACtC,UAAYA,KAAR,KAAc,QAAO;AACzB,UAAmB,OAAOA,KAAtB;AACF,eAAOA,EAAK,aAAaO,KACrB,OACAP,EAAK,eAAeA,EAAK,QAAQ;AACvC,UAAiB,OAAOA,KAApB,SAA0B,QAAOA;AACrC,cAAQA,GAAI;AAAA,QACV,KAAKF;AACH,iBAAO;AAAA,QACT,KAAKU;AACH,iBAAO;AAAA,QACT,KAAKC;AACH,iBAAO;AAAA,QACT,KAAKC;AACH,iBAAO;AAAA,QACT,KAAKC;AACH,iBAAO;AAAA,QACT,KAAKC;AACH,iBAAO;AAAA,MACjB;AACM,UAAiB,OAAOZ,KAApB;AACF,gBACgB,OAAOA,EAAK,OAAzB,YACC,QAAQ;AAAA,UACN;AAAA,QACD,GACHA,EAAK,UACf;AAAA,UACU,KAAKa;AACH,mBAAO;AAAA,UACT,KAAKC;AACH,oBAAQd,EAAK,eAAe,aAAa;AAAA,UAC3C,KAAKe;AACH,oBAAQf,EAAK,SAAS,eAAe,aAAa;AAAA,UACpD,KAAKgB;AACH,gBAAIC,IAAYjB,EAAK;AACrB,mBAAAA,IAAOA,EAAK,aACZA,MACIA,IAAOiB,EAAU,eAAeA,EAAU,QAAQ,IACnDjB,IAAcA,MAAP,KAAc,gBAAgBA,IAAO,MAAM,eAC9CA;AAAA,UACT,KAAKkB;AACH,mBACGD,IAAYjB,EAAK,eAAe,MACxBiB,MAAT,OACIA,IACAX,EAAyBN,EAAK,IAAI,KAAK;AAAA,UAE/C,KAAKmB;AACH,YAAAF,IAAYjB,EAAK,UACjBA,IAAOA,EAAK;AACZ,gBAAI;AACF,qBAAOM,EAAyBN,EAAKiB,CAAS,CAAC;AAAA,YAChD,QAAW;AAAA,YAAA;AAAA,QACxB;AACM,aAAO;AAAA,IACb;AACI,aAASG,EAAmBC,GAAO;AACjC,aAAO,KAAKA;AAAA,IAClB;AACI,aAASC,EAAuBD,GAAO;AACrC,UAAI;AACF,QAAAD,EAAmBC,CAAK;AACxB,YAAIE,IAA2B;AAAA,MAChC,QAAW;AACV,QAAAA,IAA2B;AAAA,MACnC;AACM,UAAIA,GAA0B;AAC5B,QAAAA,IAA2B;AAC3B,YAAIC,IAAwBD,EAAyB,OACjDE,IACc,OAAO,UAAtB,cACC,OAAO,eACPJ,EAAM,OAAO,WAAW,KAC1BA,EAAM,YAAY,QAClB;AACF,eAAAG,EAAsB;AAAA,UACpBD;AAAA,UACA;AAAA,UACAE;AAAA,QACD,GACML,EAAmBC,CAAK;AAAA,MACvC;AAAA,IACA;AACI,aAASK,EAAY1B,GAAM;AACzB,UAAIA,MAASF,EAAqB,QAAO;AACzC,UACe,OAAOE,KAApB,YACSA,MAAT,QACAA,EAAK,aAAamB;AAElB,eAAO;AACT,UAAI;AACF,YAAIQ,IAAOrB,EAAyBN,CAAI;AACxC,eAAO2B,IAAO,MAAMA,IAAO,MAAM;AAAA,MAClC,QAAW;AACV,eAAO;AAAA,MACf;AAAA,IACA;AACI,aAASC,IAAW;AAClB,UAAIC,IAAaC,EAAqB;AACtC,aAAgBD,MAAT,OAAsB,OAAOA,EAAW,SAAU;AAAA,IAC/D;AACI,aAASE,IAAe;AACtB,aAAO,MAAM,uBAAuB;AAAA,IAC1C;AACI,aAASC,EAAY/B,GAAQ;AAC3B,UAAIgC,EAAe,KAAKhC,GAAQ,KAAK,GAAG;AACtC,YAAIiC,IAAS,OAAO,yBAAyBjC,GAAQ,KAAK,EAAE;AAC5D,YAAIiC,KAAUA,EAAO,eAAgB,QAAO;AAAA,MACpD;AACM,aAAkBjC,EAAO,QAAlB;AAAA,IACb;AACI,aAASkC,EAA2BC,GAAOC,GAAa;AACtD,eAASC,IAAwB;AAC/B,QAAAC,MACIA,IAA6B,IAC/B,QAAQ;AAAA,UACN;AAAA,UACAF;AAAA,QACZ;AAAA,MACA;AACM,MAAAC,EAAsB,iBAAiB,IACvC,OAAO,eAAeF,GAAO,OAAO;AAAA,QAClC,KAAKE;AAAA,QACL,cAAc;AAAA,MACtB,CAAO;AAAA,IACP;AACI,aAASE,IAAyC;AAChD,UAAIC,IAAgBnC,EAAyB,KAAK,IAAI;AACtD,aAAAoC,EAAuBD,CAAa,MAChCC,EAAuBD,CAAa,IAAI,IAC1C,QAAQ;AAAA,QACN;AAAA,MACV,IACMA,IAAgB,KAAK,MAAM,KACTA,MAAX,SAA2BA,IAAgB;AAAA,IACxD;AACI,aAASE,EACP3C,GACAG,GACAyC,GACAC,GACAC,GACAV,GACAW,GACAC,GACA;AACA,aAAAJ,IAAOR,EAAM,KACbpC,IAAO;AAAA,QACL,UAAUH;AAAA,QACV,MAAMG;AAAA,QACN,KAAKG;AAAA,QACL,OAAOiC;AAAA,QACP,QAAQU;AAAA,MACT,IACoBF,MAAX,SAAkBA,IAAO,UAAnC,OACI,OAAO,eAAe5C,GAAM,OAAO;AAAA,QACjC,YAAY;AAAA,QACZ,KAAKwC;AAAA,MACN,CAAA,IACD,OAAO,eAAexC,GAAM,OAAO,EAAE,YAAY,IAAI,OAAO,MAAM,GACtEA,EAAK,SAAS,CAAE,GAChB,OAAO,eAAeA,EAAK,QAAQ,aAAa;AAAA,QAC9C,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACf,CAAO,GACD,OAAO,eAAeA,GAAM,cAAc;AAAA,QACxC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACf,CAAO,GACD,OAAO,eAAeA,GAAM,eAAe;AAAA,QACzC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO+C;AAAA,MACf,CAAO,GACD,OAAO,eAAe/C,GAAM,cAAc;AAAA,QACxC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAOgD;AAAA,MACf,CAAO,GACD,OAAO,WAAW,OAAO,OAAOhD,EAAK,KAAK,GAAG,OAAO,OAAOA,CAAI,IACxDA;AAAA,IACb;AACI,aAASiD,EACPjD,GACAC,GACAC,GACAgD,GACAL,GACAD,GACAG,GACAC,GACA;AACA,UAAIG,IAAWlD,EAAO;AACtB,UAAekD,MAAX;AACF,YAAID;AACF,cAAIE,GAAYD,CAAQ,GAAG;AACzB,iBACED,IAAmB,GACnBA,IAAmBC,EAAS,QAC5BD;AAEA,cAAAG,EAAkBF,EAASD,CAAgB,CAAC;AAC9C,mBAAO,UAAU,OAAO,OAAOC,CAAQ;AAAA,UACxC;AACC,oBAAQ;AAAA,cACN;AAAA,YACD;AAAA,YACA,CAAAE,EAAkBF,CAAQ;AACjC,UAAIlB,EAAe,KAAKhC,GAAQ,KAAK,GAAG;AACtC,QAAAkD,IAAW7C,EAAyBN,CAAI;AACxC,YAAIsD,IAAO,OAAO,KAAKrD,CAAM,EAAE,OAAO,SAAUsD,IAAG;AACjD,iBAAiBA,OAAV;AAAA,QACjB,CAAS;AACD,QAAAL,IACE,IAAII,EAAK,SACL,oBAAoBA,EAAK,KAAK,SAAS,IAAI,WAC3C,kBACNE,EAAsBL,IAAWD,CAAgB,MAC7CI,IACA,IAAIA,EAAK,SAAS,MAAMA,EAAK,KAAK,SAAS,IAAI,WAAW,MAC5D,QAAQ;AAAA,UACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UACAJ;AAAA,UACAC;AAAA,UACAG;AAAA,UACAH;AAAA,QACD,GACAK,EAAsBL,IAAWD,CAAgB,IAAI;AAAA,MAChE;AAMM,UALAC,IAAW,MACAjD,MAAX,WACGoB,EAAuBpB,CAAQ,GAAIiD,IAAW,KAAKjD,IACtD8B,EAAY/B,CAAM,MACfqB,EAAuBrB,EAAO,GAAG,GAAIkD,IAAW,KAAKlD,EAAO,MAC3D,SAASA,GAAQ;AACnB,QAAAC,IAAW,CAAE;AACb,iBAASE,KAAYH;AACnB,UAAUG,MAAV,UAAuBF,EAASE,CAAQ,IAAIH,EAAOG,CAAQ;AAAA,MAC9D,MAAM,CAAAF,IAAWD;AAClB,aAAAkD,KACEhB;AAAA,QACEjC;AAAA,QACe,OAAOF,KAAtB,aACIA,EAAK,eAAeA,EAAK,QAAQ,YACjCA;AAAA,MACL,GACI2C;AAAA,QACL3C;AAAA,QACAmD;AAAA,QACAP;AAAA,QACAC;AAAA,QACAjB,EAAU;AAAA,QACV1B;AAAA,QACA6C;AAAA,QACAC;AAAA,MACD;AAAA,IACP;AACI,aAASK,EAAkBI,GAAM;AAC/B,MAAa,OAAOA,KAApB,YACWA,MAAT,QACAA,EAAK,aAAa5D,KAClB4D,EAAK,WACJA,EAAK,OAAO,YAAY;AAAA,IACjC;AACI,QAAIC,IAAQC,IACV9D,IAAqB,OAAO,IAAI,4BAA4B,GAC5DgB,IAAoB,OAAO,IAAI,cAAc,GAC7Cf,IAAsB,OAAO,IAAI,gBAAgB,GACjDW,IAAyB,OAAO,IAAI,mBAAmB,GACvDD,IAAsB,OAAO,IAAI,gBAAgB,GAE/CO,IAAsB,OAAO,IAAI,gBAAgB,GACnDD,IAAqB,OAAO,IAAI,eAAe,GAC/CE,IAAyB,OAAO,IAAI,mBAAmB,GACvDN,IAAsB,OAAO,IAAI,gBAAgB,GACjDC,IAA2B,OAAO,IAAI,qBAAqB,GAC3DO,KAAkB,OAAO,IAAI,YAAY,GACzCC,IAAkB,OAAO,IAAI,YAAY,GACzCP,KAAsB,OAAO,IAAI,gBAAgB,GACjDL,KAAyB,OAAO,IAAI,wBAAwB,GAC5DuB,IACE4B,EAAM,iEACRzB,IAAiB,OAAO,UAAU,gBAClCmB,KAAc,MAAM,SACpBQ,IAAa,QAAQ,aACjB,QAAQ,aACR,WAAY;AACV,aAAO;AAAA,IACR;AACP,IAAAF,IAAQ;AAAA,MACN,4BAA4B,SAAUG,GAAmB;AACvD,eAAOA,EAAmB;AAAA,MAClC;AAAA,IACK;AACD,QAAItB,GACAG,IAAyB,CAAE,GAC3BoB,IAAyBJ,EAAM,0BAA0B,EAAE;AAAA,MAC7DA;AAAA,MACA3B;AAAA,IACN,EAAO,GACCgC,IAAwBH,EAAWlC,EAAYK,CAAY,CAAC,GAC5DyB,IAAwB,CAAE;AAC9B,IAAAQ,EAAA,WAAmBlE,GACnBkE,EAAW,MAAG,SAAUhE,GAAMC,GAAQC,GAAU2C,GAAQD,GAAM;AAC5D,UAAIqB,IACF,MAAMnC,EAAqB;AAC7B,aAAOmB;AAAA,QACLjD;AAAA,QACAC;AAAA,QACAC;AAAA,QACA;AAAA,QACA2C;AAAA,QACAD;AAAA,QACAqB,IACI,MAAM,uBAAuB,IAC7BH;AAAA,QACJG,IAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,IAAI+D;AAAA,MACpD;AAAA,IACF,GACDC,EAAY,OAAG,SAAUhE,GAAMC,GAAQC,GAAU2C,GAAQD,GAAM;AAC7D,UAAIqB,IACF,MAAMnC,EAAqB;AAC7B,aAAOmB;AAAA,QACLjD;AAAA,QACAC;AAAA,QACAC;AAAA,QACA;AAAA,QACA2C;AAAA,QACAD;AAAA,QACAqB,IACI,MAAM,uBAAuB,IAC7BH;AAAA,QACJG,IAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,IAAI+D;AAAA,MACpD;AAAA,IACF;AAAA,EACL,EAAM;;;;wBCnWF,QAAQ,IAAI,aAAa,eAC3BG,EAAA,UAAiBP,GAAgD,IAEjEO,EAAA,UAAiBC,GAAiD;;;;;;;;;;;ACEpE,KAAC,WAAY;AAGZ,UAAIC,IAAS,CAAA,EAAG;AAEhB,eAASC,IAAc;AAGtB,iBAFIC,IAAU,IAELC,IAAI,GAAGA,IAAI,UAAU,QAAQA,KAAK;AAC1C,cAAIC,IAAM,UAAUD,CAAC;AACrB,UAAIC,MACHF,IAAUG,EAAYH,GAASI,EAAWF,CAAG,CAAC;AAAA,QAElD;AAEE,eAAOF;AAAA,MACT;AAEC,eAASI,EAAYF,GAAK;AACzB,YAAI,OAAOA,KAAQ,YAAY,OAAOA,KAAQ;AAC7C,iBAAOA;AAGR,YAAI,OAAOA,KAAQ;AAClB,iBAAO;AAGR,YAAI,MAAM,QAAQA,CAAG;AACpB,iBAAOH,EAAW,MAAM,MAAMG,CAAG;AAGlC,YAAIA,EAAI,aAAa,OAAO,UAAU,YAAY,CAACA,EAAI,SAAS,SAAQ,EAAG,SAAS,eAAe;AAClG,iBAAOA,EAAI,SAAU;AAGtB,YAAIF,IAAU;AAEd,iBAASnE,KAAOqE;AACf,UAAIJ,EAAO,KAAKI,GAAKrE,CAAG,KAAKqE,EAAIrE,CAAG,MACnCmE,IAAUG,EAAYH,GAASnE,CAAG;AAIpC,eAAOmE;AAAA,MACT;AAEC,eAASG,EAAapD,GAAOsD,GAAU;AACtC,eAAKA,IAIDtD,IACIA,IAAQ,MAAMsD,IAGftD,IAAQsD,IAPPtD;AAAA,MAQV;AAEC,MAAqCuD,EAAO,WAC3CP,EAAW,UAAUA,GACrBO,YAAiBP,KAOjB,OAAO,aAAaA;AAAA,IAEtB;;;;kCCvEMQ,KAA+C;AAAA,EACnD,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAW;AAAA,EACX,KAAO;AAAA,EACP,mBAAmB;AACrB,GAGMC,KAAqD;AAAA,EACzD,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAW;AAAA,EACX,KAAO;AAAA,EACP,mBAAmB;AACrB,GAEaC,IAA0C,CAAC;AAAA,EACtD,MAAA/E;AAAA,EACA,SAAAgF;AAAA,EACA,MAAAC,IAAO;AAAA,EACP,WAAAC;AACF,MAAM;AACJ,QAAMC,IAAmBH,KAAWH,GAAkB7E,CAAI,KAAK,aACzDoF,IAAON,GAAe9E,CAAI,GAE1BqF,IAAehB;AAAA,IACnB;AAAA,IACA;AAAA,IACA,iBAAiBc,CAAgB;AAAA,IACjC,iBAAiBF,CAAI;AAAA,IACrBC;AAAA,EACF;AAGE,SAAAI,gBAAAA,EAAA,KAAC,QAAK,EAAA,WAAWD,GACd,UAAA;AAAA,IAAAD,KAASG,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAU,sBAAsB,UAAKH,GAAA;AAAA,IACnDG,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAU,sBAAsB,UAAKvF,EAAA,CAAA;AAAA,EAAA,GAC7C;AAEJ;AAEA+E,EAAY,cAAc;AC7C1B,MAAMS,KAAuB;AAW7B,IAAIC,IAA+B;AAAA,EACjC,YAAYD;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,eAAe;AAAA,EACf,YAAY;AACd;AAEa,MAAAE,KAAyB,CAACzF,MAAoC;AACzE,EAAAwF,IAAe,EAAE,GAAGA,GAAc,GAAGxF,EAAO;AAC9C,GAEa0F,KAAmB,CAAC1F,MAA6D;AAC5F,QAAM,CAAC2F,GAAYC,CAAa,IAAIC,GAAS,EAAK,GAC5C,CAACC,GAAOC,CAAQ,IAAIF,GAAwB,IAAI,GAEhDG,IAAe,EAAE,GAAGR,GAAc,GAAGxF,EAAO,GAE5CiG,IAAMC,EAAY,CAACC,GAAiBC,MAAe;AACvD,IAAIJ,EAAa,SACf,QAAQ,IAAI,oBAAoBG,CAAO,IAAIC,CAAI;AAAA,EACjD,GACC,CAACJ,EAAa,KAAK,CAAC,GAEjBK,IAAoBH,EAAY,OACpCI,GACAF,MACkB;AACd,QAAA,CAACJ,EAAa,SAAS;AACzB,MAAAC,EAAI,qCAAqC,EAAE,WAAAK,GAAW,MAAAF,EAAA,CAAM;AAC5D;AAAA,IAAA;AAGF,QAAI,CAACA,EAAK,QAAQ,CAACA,EAAK,YAAY;AAClC,YAAMG,IAAW;AACjB,MAAAN,EAAIM,GAAUH,CAAI,GAClBL,EAASQ,CAAQ;AACjB;AAAA,IAAA;AAGF,IAAAX,EAAc,EAAI,GAClBG,EAAS,IAAI;AAEb,UAAMS,IAAU;AAAA,MACd,YAAYF;AAAA,MACZ,OAAOF,EAAK;AAAA,MACZ,aAAaA,EAAK;AAAA,MAClB,YAAYA,EAAK;AAAA,MACjB,SAASA,EAAK;AAAA,MACd,YAAYA,EAAK;AAAA,MACjB,SAASA,EAAK;AAAA,MACd,iBAAiBA,EAAK;AAAA,MACtB,UAAUA,EAAK;AAAA,MACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,YAAY,UAAU;AAAA,MACtB,UAAU,SAAS;AAAA,MACnB,UAAU,OAAO,SAAS;AAAA,IAC5B;AAEI,IAAAH,EAAA,WAAWK,CAAS,UAAUE,CAAO;AAEzC,QAAIC,IAA0B;AAE9B,aAASC,IAAU,GAAGA,MAAYV,EAAa,iBAAiB,IAAIU;AAC9D,UAAA;AACF,cAAMC,IAAW,MAAM,MAAM,GAAGX,EAAa,UAAU,WAAW;AAAA,UAChE,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,gBAAgB;AAAA,UAClB;AAAA,UACA,MAAM,KAAK,UAAUQ,CAAO;AAAA,QAAA,CAC7B;AAEG,YAAA,CAACG,EAAS;AACN,gBAAA,IAAI,MAAM,QAAQA,EAAS,MAAM,KAAKA,EAAS,UAAU,EAAE;AAG7D,cAAAC,IAAS,MAAMD,EAAS,KAAK;AAC/B,QAAAV,EAAA,GAAGK,CAAS,+BAA+BM,CAAM,GACrDhB,EAAc,EAAK;AACnB;AAAA,eAEOiB,GAAK;AACA,QAAAJ,IAAAI,GACZZ,EAAI,WAAWS,CAAO,eAAeJ,CAAS,UAAUO,CAAG,GAEvDH,KAAWV,EAAa,iBAAiB,MAC3C,MAAM,IAAI;AAAA,UAAQ,OAChB,WAAWc,IAAUd,EAAa,cAAc,OAAQU,CAAO;AAAA,QACjE;AAAA,MACF;AAKE,UAAAH,IAAW,mBAAmBD,CAAS,gBAAgBN,EAAa,aAAa,cAAcS,KAAA,gBAAAA,EAAW,OAAO;AACvH,IAAAR,EAAIM,GAAUE,CAAS,GACvBV,EAASQ,CAAQ,GACjBX,EAAc,EAAK;AAAA,EAAA,GAClB,CAACI,GAAcC,CAAG,CAAC,GAEhBc,IAAab,EAAY,OAAOE,MAC7BC,EAAkB,SAASD,CAAI,GACrC,CAACC,CAAiB,CAAC,GAEhBW,IAAYd,EAAY,OAAOE,MAC5BC,EAAkB,QAAQD,CAAI,GACpC,CAACC,CAAiB,CAAC,GAEhBY,IAAkBf,EAAY,OAAOE,OACrC,CAACA,EAAK,WAAW,CAACA,EAAK,kBACzBH,EAAI,mEAAmEG,CAAI,GAEtEC,EAAkB,cAAcD,CAAI,IAC1C,CAACC,CAAiB,CAAC;AAEf,SAAA;AAAA,IACL,YAAAU;AAAA,IACA,WAAAC;AAAA,IACA,iBAAAC;AAAA,IACA,YAAAtB;AAAA,IACA,OAAAG;AAAA,EACF;AACF,GAGaoB,KAAkB,CAC7BC,GACAC,GACAC,MACW;AACP,MAAA;AACI,UAAAC,IAAM,IAAI,IAAIH,CAAQ;AACxB,WAAAG,EAAA,aAAa,IAAI,SAASF,CAAI,GAC9BE,EAAA,aAAa,IAAI,cAAc,QAAQ,GACvCA,EAAA,aAAa,IAAI,cAAc,gBAAgB,GAE/CD,KACK,OAAA,QAAQA,CAAgB,EAAE,QAAQ,CAAC,CAACnH,GAAKkB,CAAK,MAAM;AACrD,MAAAkG,EAAA,aAAa,IAAIpH,GAAKkB,CAAK;AAAA,IAAA,CAChC,GAGIkG,EAAI,SAAS;AAAA,UACN;AACN,mBAAA,KAAK,qDAAqDH,CAAQ,GACnEA;AAAA,EAAA;AAEX,GAGaI,KAAsB,CACjCC,GACAC,OAEO;AAAA,EACL,MAAMD,EAAe;AAAA,EACrB,YAAYA,EAAe;AAAA,EAC3B,WAAWA,EAAe;AAAA,EAC1B,GAAGC;AACL,IC3KWC,IAAsD,CAAC;AAAA,EAClE,MAAAN;AAAA,EACA,YAAAO;AAAA,EACA,WAAAC;AAAA,EACA,UAAA1E;AAAA,EACA,SAAA2E;AAAA,EACA,cAAAC;AAAA,EACA,WAAA7C;AACF,MAAM;AACJ,QAAM,EAAE,YAAA8B,GAAY,WAAAC,EAAU,IAAItB,GAAiB,GAC7CqC,IAAaC,GAAuB,IAAI,GACxCC,IAAiBD,GAAO,EAAK;AAGnC,EAAAE,GAAU,MAAM;AACd,QAAI,CAACH,EAAW,WAAWE,EAAe,QAAS;AAEnD,UAAME,IAAW,IAAI;AAAA,MACnB,CAACC,MAAY;AACH,QAAAA,EAAA,QAAQ,CAACC,MAAU;AACzB,UAAIA,EAAM,kBAAkB,CAACJ,EAAe,YAC1CA,EAAe,UAAU,IACfjB,EAAA;AAAA,YACR,MAAAI;AAAA,YACA,YAAAO;AAAA,YACA,WAAAC;AAAA,YACA,GAAGE;AAAA,UAAA,CACJ,EAAE,MAAM,QAAQ,KAAK;AAAA,QACxB,CACD;AAAA,MACH;AAAA,MACA;AAAA,QACE,WAAW;AAAA;AAAA,QACX,YAAY;AAAA,MAAA;AAAA,IAEhB;AAES,WAAAK,EAAA,QAAQJ,EAAW,OAAO,GAE5B,MAAM;AACX,MAAAI,EAAS,WAAW;AAAA,IACtB;AAAA,EAAA,GACC,CAACf,GAAMO,GAAYC,GAAWE,GAAcd,CAAS,CAAC;AAEnD,QAAAsB,IAAcpC,EAAY,OAAOqC,MAA4B;AAE7D,QAAA;AACF,YAAMxB,EAAW;AAAA,QACf,MAAAK;AAAA,QACA,YAAAO;AAAA,QACA,WAAAC;AAAA,QACA,GAAGE;AAAA,MAAA,CACJ;AAAA,aACMhC,GAAO;AACN,cAAA,MAAM,0BAA0BA,CAAK;AAAA,IAAA;AAI/C,IAAI+B,KACMA,EAAA,GAKKU,EAAM,OACD,QAAQ,GAAG,KAItB,OAAA,KAAKZ,GAAY,UAAU,qBAAqB;AAAA,EACzD,GAEC,CAACP,GAAMO,GAAYC,GAAWE,GAAcf,GAAYc,CAAO,CAAC;AAGjE,SAAAvC,gBAAAA,EAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,KAAKyC;AAAA,MACL,WAAA9C;AAAA,MACA,SAASqD;AAAA,MACT,OAAO,EAAE,QAAQ,UAAU;AAAA,MAE1B,UAAApF;AAAA,IAAA;AAAA,EACH;AAEJ;AAEAwE,EAAkB,cAAc;ACpFzB,MAAMc,KAAsD,CAAC;AAAA,EAClE,gBAAAhB;AAAA,EACA,OAAAiB;AAAA,EACA,gBAAAC,IAAiB;AAAA,EACjB,YAAAC,IAAa;AAAA,EACb,aAAAC,IAAc;AAAA,EACd,SAAAf;AAAA,EACA,aAAAgB;AAAA,EACA,WAAA5D;AACF,MAAM;;AAEE,QAAA6D,IAASC,EAAQ,MAAmB;;AACxC,UAAMC,IAA+B,CAAC;AAGlC,IAAAxB,EAAe,sBAAsB,OACvCwB,EAAgB,KAAK,WAAW,GAI9BxB,EAAe,iBACjBwB,EAAgB,KAAK,WAAW,GAI9BxB,EAAe,cAAcA,EAAe,aAAa,KAC3DwB,EAAgB,KAAK,iBAAiB;AAIxC,UAAMC,IAAa,CAAC,MAAM,2BAA2B,oBAAoB,MAAM,YAAY;AAK3F,cAJsBC,IAAA1B,EAAe,aAAf,gBAAA0B,EAAyB;AAAA,MAAK,CAAAC,MAClDF,EAAW,KAAK,CAAAG,MAAMD,EAAQ,YAAY,EAAE,SAASC,CAAE,CAAC;AAAA,UACrD5B,EAAe,MAAM,YAAY,EAAE,SAAS,IAAI,MAGnDwB,EAAgB,KAAK,YAAY,GAG5BA;AAAA,EAAA,GACN,CAACxB,CAAc,CAAC,GAGb6B,IAAuB,KAAK,MAAM7B,EAAe,qBAAqB,GAAG,GAGzE8B,MAAkBJ,IAAA1B,EAAe,aAAf,gBAAA0B,EAAyB,MAAM,GAAGN,OAAgB,CAAC,GACrEW,OAAmBC,IAAAhC,EAAe,aAAf,gBAAAgC,EAAyB,WAAU,KAAKZ,GAE3Da,IAAcrF;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,MACE,CAAC,wBAAwBqE,KAAA,gBAAAA,EAAO,IAAI,EAAE,GAAGA,KAAA,gBAAAA,EAAO;AAAA,IAClD;AAAA,IACAxD;AAAA,EACF,GAEMyE,IAAYjB,KAAA,QAAAA,EAAO,cAAc;AAAA,IACrC,oBAAoBA,EAAM;AAAA,IAC1B,0BAA0BA,EAAM,cAAc;AAAA;AAAA,EAAA,IACrB;AAGzB,SAAAnD,gBAAAA,EAAA;AAAA,IAACoC;AAAA,IAAA;AAAA,MACC,MAAMF,EAAe;AAAA,MACrB,YAAYA,EAAe;AAAA,MAC3B,WAAWA,EAAe;AAAA,MAC1B,SAAS,MAAMK,KAAA,gBAAAA,EAAUL,EAAe,OAAOA,EAAe;AAAA,MAC9D,cAAc;AAAA,QACZ,OAAOA,EAAe;AAAA,QACtB,YAAYA,EAAe;AAAA,MAC7B;AAAA,MACA,WAAWiC;AAAA,MAEX,UAAApE,gBAAAA,EAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,WAAU;AAAA,UACV,OAAOqE;AAAA,UACP,qBAAmBjB,KAAA,gBAAAA,EAAO;AAAA,UAG1B,UAAA;AAAA,YAACpD,gBAAAA,EAAAA,KAAA,OAAA,EAAI,WAAU,+BACZ,UAAA;AAAA,cAAcsD,KAAAG,EAAO,SAAS,KAC5BxD,gBAAAA,EAAA,IAAA,OAAA,EAAI,WAAU,+BACZ,UAAAwD,EAAO,IAAI,CAACa,GAAOC,4BACjB9E,GAAsC,EAAA,MAAM6E,GAAO,MAAK,KAAvC,GAAA,GAAGA,CAAK,IAAIC,CAAK,EAA2B,CAC/D,EACH,CAAA;AAAA,cAGDlB,2BACE,OAAI,EAAA,WAAU,oCACb,UAACrD,gBAAAA,EAAAA,KAAA,QAAA,EAAK,WAAU,oCACb,UAAA;AAAA,gBAAAgE;AAAA,gBAAqB;AAAA,cAAA,EAAA,CACxB,EACF,CAAA;AAAA,YAAA,GAEJ;AAAA,YAGAhE,gBAAAA,EAAAA,KAAC,OAAI,EAAA,WAAU,gCACb,UAAA;AAAA,cAAAC,gBAAAA,EAAA,IAAC,MAAG,EAAA,WAAU,kEACX,UAAAkC,EAAe,OAClB;AAAA,cAEClC,gBAAAA,EAAA,IAAA,KAAA,EAAE,WAAU,oEACV,YAAe,QAClB;AAAA,cAGCgE,EAAgB,SAAS,KACvBjE,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAU,iCACZ,UAAA;AAAA,gBAAgBiE,EAAA,IAAI,CAACH,GAASS,MAC7BtE,gBAAAA,EAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBAEC,WAAU;AAAA,oBAET,UAAA6D;AAAA,kBAAA;AAAA,kBAHIS;AAAA,gBAAA,CAKR;AAAA,gBACAL,KACClE,gBAAAA,EAAA,KAAC,QAAK,EAAA,WAAU,sEAAqE,UAAA;AAAA,kBAAA;AAAA,qBAChFwE,IAAArC,EAAe,aAAf,gBAAAqC,EAAyB,WAAU,KAAKjB;AAAA,kBAAY;AAAA,gBAAA,EACzD,CAAA;AAAA,cAAA,GAEJ;AAAA,cAIFvD,gBAAAA,EAAAA,KAAC,OAAI,EAAA,WAAU,6BACZ,UAAA;AAAA,gBAAAmC,EAAe,WACdnC,gBAAAA,OAAC,OAAI,EAAA,WAAU,+CACb,UAAA;AAAA,kBAACC,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAU,qBAAoB,UAAS,aAAA;AAAA,kBAC5CA,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAU,sBAAsB,YAAe,QAAQ,CAAA;AAAA,gBAAA,GAC/D;AAAA,gBAGDkC,EAAe,cAAcA,EAAe,aAAa,KACvDnC,gBAAAA,OAAA,OAAA,EAAI,WAAU,+DACZ,UAAA;AAAA,kBAAemC,EAAA;AAAA,kBAAW;AAAA,gBAAA,EAC7B,CAAA;AAAA,cAAA,EAEJ,CAAA;AAAA,YAAA,GACF;AAAA,kCAGC,OAAI,EAAA,WAAU,+BACb,UAACnC,gBAAAA,EAAA,KAAA,UAAA,EAAO,WAAU,iEAAgE,UAAA;AAAA,cAAA;AAAA,cAEhFA,gBAAAA,EAAAA,KAAC,QAAK,EAAA,WAAU,kBAAiB,UAAA;AAAA,gBAAA;AAAA,gBAC1BmC,EAAe;AAAA,cAAA,EACtB,CAAA;AAAA,YAAA,EAAA,CACF,EACF,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACF;AAAA,EACF;AAEJ;AAEAgB,GAAkB,cAAc;AChKzB,MAAMsB,KAAwD,CAAC;AAAA,EACpE,iBAAAC;AAAA,EACA,OAAAtB;AAAA,EACA,aAAAuB,IAAc;AAAA,EACd,iBAAAC,IAAkB;AAAA,EAClB,cAAAC,IAAe;AAAA,EACf,gBAAAC;AAAA,EACA,WAAAlF;AACF,MAAM;AAEE,QAAAmF,IAAoBrB,EAAQ,MACzBgB,EAAgB,MAAM,GAAGC,CAAW,GAC1C,CAACD,GAAiBC,CAAW,CAAC,GAG3BK,IAActB,EAAQ,MAAM;AAC1B,UAAAuB,wBAAkB,IAAY;AACpC,WAAAF,EAAkB,QAAQ,CAAWG,MAAA;;AACnC,OAAArB,IAAAqB,EAAQ,aAAR,QAAArB,EAAkB,QAAQ,CAAAsB,MAAWF,EAAY,IAAIE,CAAO;AAAA,IAAC,CAC9D,GACM,MAAM,KAAKF,CAAW,EAAE,MAAM,GAAG,CAAC;AAAA,EAAA,GACxC,CAACF,CAAiB,CAAC,GAEhBK,IAAerG;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,MACE,CAAC,yBAAyBqE,KAAA,gBAAAA,EAAO,IAAI,EAAE,GAAGA,KAAA,gBAAAA,EAAO;AAAA,IACnD;AAAA,IACAxD;AAAA,EACF,GAEMyF,IAAajC,KAAA,QAAAA,EAAO,cAAc;AAAA,IACtC,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAEvB,SAAA2B,EAAkB,WAAW,IAE5B9E,gBAAAA,EAAA,IAAA,OAAA,EAAI,WAAWmF,GACd,gCAAC,OAAI,EAAA,WAAU,+BACb,UAAAnF,gBAAAA,EAAA,IAAC,KAAE,EAAA,WAAU,qBAAoB,UAAA,yBAAA,CAAsB,EACzD,CAAA,GACF,IAKFA,gBAAAA,EAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWmF;AAAA,MACX,OAAOC;AAAA,MACP,qBAAmBjC,KAAA,gBAAAA,EAAO;AAAA,MAE1B,UAAApD,gBAAAA,EAAA,KAAC,OAAI,EAAA,WAAU,mCACb,UAAA;AAAA,QAACA,gBAAAA,EAAAA,KAAA,OAAA,EAAI,WAAU,gCACb,UAAA;AAAA,UAACC,gBAAAA,EAAA,IAAA,MAAA,EAAG,WAAU,mEAAkE,UAEhF,sBAAA;AAAA,UACAD,gBAAAA,EAAAA,KAAC,KAAE,EAAA,WAAU,mEAAkE,UAAA;AAAA,YAAA;AAAA,YACpE+E,EAAkB;AAAA,YAAO;AAAA,UAAA,EACpC,CAAA;AAAA,QAAA,GACF;AAAA,8BAEC,OAAI,EAAA,WAAU,0CACb,UAAC/E,gBAAAA,EAAA,KAAA,SAAA,EAAM,WAAU,+BACf,UAAA;AAAA,UAACC,gBAAAA,EAAA,IAAA,SAAA,EACC,iCAAC,MACC,EAAA,UAAA;AAAA,YAACA,gBAAAA,EAAAA,IAAA,MAAA,EAAG,WAAU,oCACZ,UAAAA,gBAAAA,EAAA,IAAC,UAAK,WAAU,kBAAiB,qBAAO,EAC1C,CAAA;AAAA,YACC8E,EAAkB,IAAI,CAACG,GAASX,MAC9BtE,gBAAAA,EAAAA,IAAA,MAAA,EAAqC,WAAU,wCAC9C,UAAAD,gBAAAA,EAAA;AAAA,cAACqC;AAAA,cAAA;AAAA,gBACC,MAAM6C,EAAQ;AAAA,gBACd,YAAYA,EAAQ;AAAA,gBACpB,WAAWA,EAAQ;AAAA,gBACnB,SAAS,MAAMJ,KAAA,gBAAAA,EAAiBI,EAAQ,OAAOA,EAAQ;AAAA,gBACvD,WAAU;AAAA,gBAEV,UAAA;AAAA,kBAAClF,gBAAAA,EAAAA,KAAA,OAAA,EAAI,WAAU,uCACb,UAAA;AAAA,oBAAAC,gBAAAA,EAAA,IAAC,MAAG,EAAA,WAAU,yDACX,UAAAiF,EAAQ,OACX;AAAA,oBACCN,2BACE,OAAI,EAAA,WAAU,qCACb,UAAC5E,gBAAAA,EAAAA,KAAA,QAAA,EAAK,WAAU,oCACb,UAAA;AAAA,sBAAK,KAAA,MAAMkF,EAAQ,qBAAqB,GAAG;AAAA,sBAAE;AAAA,oBAAA,EAAA,CAChD,EACF,CAAA;AAAA,kBAAA,GAEJ;AAAA,kBAGAlF,gBAAAA,EAAAA,KAAC,OAAI,EAAA,WAAU,gCACZ,UAAA;AAAA,oBAAAkF,EAAQ,iBACNjF,gBAAAA,EAAAA,IAAAR,GAAA,EAAY,MAAK,aAAY,MAAK,MAAK;AAAA,oBAEzCyF,EAAQ,cAAcA,EAAQ,aAAa,2BACzCzF,GAAY,EAAA,MAAK,mBAAkB,MAAK,KAAK,CAAA;AAAA,oBAE/CyF,EAAQ,sBAAsB,OAC7BjF,gBAAAA,MAACR,KAAY,MAAK,aAAY,MAAK,KAAK,CAAA;AAAA,kBAAA,GAE5C;AAAA,kBAECQ,gBAAAA,EAAA,IAAA,UAAA,EAAO,WAAU,oFAAmF,UAErG,cAAA,CAAA;AAAA,gBAAA;AAAA,cAAA;AAAA,YAAA,KApCKiF,EAAQ,cAAcX,CAsC/B,CACD;AAAA,UAAA,EAAA,CACH,EACF,CAAA;AAAA,iCAEC,SAEC,EAAA,UAAA;AAAA,YAAAvE,gBAAAA,OAAC,MACC,EAAA,UAAA;AAAA,cAACC,gBAAAA,EAAA,IAAA,MAAA,EAAG,WAAU,uDAAsD,UAEpE,WAAA;AAAA,cACC8E,EAAkB,IAAI,CAACG,GAASX,MAC9BtE,gBAAAA,EAAAA,IAAA,MAAA,EAAqC,WAAU,8BAC9C,UAAAA,gBAAAA,EAAA,IAAC,UAAK,WAAU,kBACb,YAAQ,WAAW,sBAAA,CACtB,KAHOiF,EAAQ,cAAcX,CAI/B,CACD;AAAA,YAAA,GACH;AAAA,mCAGC,MACC,EAAA,UAAA;AAAA,cAACtE,gBAAAA,EAAA,IAAA,MAAA,EAAG,WAAU,uDAAsD,UAEpE,cAAA;AAAA,cACC8E,EAAkB,IAAI,CAACG,GAASX,4BAC9B,MAAqC,EAAA,WAAU,8BAC9C,UAAAtE,gBAAAA,EAAAA,IAAC,QAAK,EAAA,WAAU,kBACb,UAAQiF,EAAA,aAAa,GAAGA,EAAQ,UAAU,UAAU,YACvD,EAHO,GAAAA,EAAQ,cAAcX,CAI/B,CACD;AAAA,YAAA,GACH;AAAA,YAGCM,KAAgBG,EAAY,IAAI,CAACG,GAASG,6BACxC,MACC,EAAA,UAAA;AAAA,cAACrF,gBAAAA,EAAA,IAAA,MAAA,EAAG,WAAU,uDACX,UACHkF,GAAA;AAAA,cACCJ,EAAkB,IAAI,CAACG,GAASK,MAC9BtF;;AAAAA,uCAAAA,EAAAA,IAAA,MAAA,EAA4C,WAAU,8BACrD,gCAAC,QAAK,EAAA,WAAU,kBACb,WAAA4D,IAAAqB,EAAQ,aAAR,QAAArB,EAAkB,SAASsB,KAC1BlF,gBAAAA,EAAAA,IAAC,QAAK,EAAA,WAAU,+BAA8B,UAAA,IAAC,CAAA,IAE9CA,gBAAAA,EAAAA,IAAA,QAAA,EAAK,WAAU,+BAA8B,cAAC,CAAA,EAEnD,CAAA,KAPOiF,EAAQ,cAAcK,CAQ/B;AAAA,eACD;AAAA,YAAA,EAAA,GAdMD,CAeT,CACD;AAAA,mCAGA,MACC,EAAA,UAAA;AAAA,cAACrF,gBAAAA,EAAA,IAAA,MAAA,EAAG,WAAU,uDAAsD,UAEpE,YAAA;AAAA,cACC8E,EAAkB,IAAI,CAACG,GAASX,MAC/BtE;;AAAAA,uCAAAA,EAAAA,IAAC,MAAqC,EAAA,WAAU,8BAC9C,UAAAD,gBAAAA,EAAAA,KAAC,OAAI,EAAA,WAAU,kCACZ,UAAA;AAAA,mBAAQ6D,IAAAqB,EAAA,aAAA,gBAAArB,EAAU,MAAM,GAAG,GAAG,IAAI,CAACC,GAAS0B,MAC3CvF,gBAAAA,EAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBAEC,WAAU;AAAA,sBAET,UAAA6D;AAAA,oBAAA;AAAA,oBAHI0B;AAAA,kBAAA;AAAA,qBAMPrB,IAAAe,EAAQ,aAAR,gBAAAf,EAAkB,WAAU,KAAK,KAChCnE,gBAAAA,EAAA,KAAA,QAAA,EAAK,WAAU,oCAAmC,UAAA;AAAA,oBAAA;AAAA,uBAC9CwE,IAAAU,EAAQ,aAAR,gBAAAV,EAAkB,WAAU,KAAK;AAAA,kBAAA,EACtC,CAAA;AAAA,gBAAA,EAAA,CAEJ,EAfO,GAAAU,EAAQ,cAAcX,CAgB/B;AAAA,eACD;AAAA,YAAA,EACH,CAAA;AAAA,UAAA,EACF,CAAA;AAAA,QAAA,EAAA,CACF,EACF,CAAA;AAAA,MAAA,EACF,CAAA;AAAA,IAAA;AAAA,EACF;AAEJ;AAEAE,GAAmB,cAAc;AClMjC,MAAMgB,KAAsB,CAC1Bf,GACAgB,GACAC,MACiC;AAC7B,MAAA,CAACA,KAAcD;AAEjB,YAAQA,GAAY;AAAA,MAClB,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACI,eAAA;AAAA,MACT;AACS,eAAA;AAAA,IAAA;AAKb,QAAME,IAAelB,EAAgB;AAGjC,MAAAkB,KAAgB,KAAKA,KAAgB,GAAG;AACpC,UAAAC,IAAcnB,EAAgB,KAAK,CAAAoB,MAAOA,EAAI,YAAYA,EAAI,SAAS,SAAS,CAAC,GACjFC,IAAarB,EAAgB,KAAK,CAAAoB,MAAOA,EAAI,OAAO;AAE1D,QAAID,KAAeE;AACV,aAAA;AAAA,EACT;AAIK,SAAA;AACT,GAEaC,KAA4C,CAAC;AAAA,EACxD,iBAAAtB;AAAA,EACA,YAAAgB;AAAA,EACA,OAAAtC;AAAA,EACA,cAAA6C,IAAe;AAAA,EACf,iBAAArB,IAAkB;AAAA,EAClB,cAAAC,IAAe;AAAA,EACf,YAAAc,IAAa;AAAA,EACb,gBAAAb;AAAA,EACA,aAAAtB;AAAA,EACA,WAAA5D;AACF,MAAM;AAEE,QAAAsG,IAAyBxC,EAAQ,MAC9BgB,EAAgB,MAAM,GAAGuB,CAAY,GAC3C,CAACvB,GAAiBuB,CAAY,CAAC,GAG5BE,IAASzC,EAAQ,MACd+B,GAAoBS,GAAwBR,GAAYC,CAAU,GACxE,CAACO,GAAwBR,GAAYC,CAAU,CAAC,GAE7CS,IAAmBrH;AAAA,IACvB;AAAA,IACA;AAAA,IACA,kBAAkBoH,CAAM;AAAA,IACxB;AAAA,MACE,CAAC,kBAAkB/C,KAAA,gBAAAA,EAAO,IAAI,EAAE,GAAGA,KAAA,gBAAAA,EAAO;AAAA,IAC5C;AAAA,IACAxD;AAAA,EACF,GAEMyG,IAAiBjD,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAEvB,SAAA8C,EAAuB,WAAW,IAElCjG,gBAAAA,EAAAA,IAAC,OAAI,EAAA,WAAWmG,GACd,UAAAnG,gBAAAA,EAAAA,IAAC,OAAI,EAAA,WAAU,wBACb,UAAAD,gBAAAA,EAAA,KAAC,OAAI,EAAA,WAAU,gCACb,UAAA;AAAA,IAACC,gBAAAA,EAAA,IAAA,MAAA,EAAG,WAAU,yDAAwD,UAEtE,gCAAA;AAAA,IACCA,gBAAAA,EAAA,IAAA,KAAA,EAAE,WAAU,oCAAmC,UAEhD,0DAAA,CAAA;AAAA,EAAA,EACF,CAAA,EACF,CAAA,GACF,IAKFA,gBAAAA,EAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWmG;AAAA,MACX,OAAOC;AAAA,MACP,qBAAmBjD,KAAA,gBAAAA,EAAO;AAAA,MAEzB,gBAAW,YACVnD,gBAAAA,EAAA;AAAA,QAACwE;AAAA,QAAA;AAAA,UACC,iBAAiByB;AAAA,UACjB,OAAA9C;AAAA,UACA,aAAa,KAAK,IAAI8C,EAAuB,QAAQ,CAAC;AAAA,UACtD,iBAAAtB;AAAA,UACA,cAAAC;AAAA,UACA,gBAAAC;AAAA,QAAA;AAAA,MAGF,IAAA9E,gBAAAA,EAAA,KAAC,OAAI,EAAA,WAAU,kCAEb,UAAA;AAAA,QAACA,gBAAAA,EAAAA,KAAA,OAAA,EAAI,WAAU,yBACb,UAAA;AAAA,UAACC,gBAAAA,EAAA,IAAA,MAAA,EAAG,WAAU,4DAA2D,UAEzE,wBAAA;AAAA,UACAD,gBAAAA,EAAAA,KAAC,KAAE,EAAA,WAAU,4DACV,UAAA;AAAA,YAAuBkG,EAAA;AAAA,YAAO;AAAA,YAASA,EAAuB,WAAW,IAAI,MAAM;AAAA,YAAG;AAAA,UAAA,EACzF,CAAA;AAAA,QAAA,GACF;AAAA,QAGAjG,gBAAAA,EAAAA,IAAC,SAAI,WAAU,6BACZ,YAAuB,IAAI,CAACkC,GAAgBoC,MAC3CtE,gBAAAA,EAAA;AAAA,UAACkD;AAAA,UAAA;AAAA,YAEC,gBAAAhB;AAAA,YACA,OAAAiB;AAAA,YACA,gBAAgBwB;AAAA,YAChB,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAASE;AAAA,YACT,aAAAtB;AAAA,UAAA;AAAA,UAPKrB,EAAe,cAAcA,EAAe,SAASoC;AAAA,QAS7D,CAAA,GACH;AAAA,QAGCG,EAAgB,SAASuB,KACvBhG,gBAAAA,EAAAA,IAAA,OAAA,EAAI,WAAU,iCACb,UAAAD,gBAAAA,EAAA,KAAC,KAAE,EAAA,WAAU,oCAAmC,UAAA;AAAA,UAAA;AAAA,UACrCiG;AAAA,UAAa;AAAA,UAAKvB,EAAgB;AAAA,UAAO;AAAA,QAAA,EAAA,CACpD,EACF,CAAA;AAAA,MAAA,EAEJ,CAAA;AAAA,IAAA;AAAA,EAEJ;AAEJ;AAEAsB,GAAa,cAAc;AC/GpB,MAAMM,KAAU,SAGVC,KAAiB;AAAA,EAC5B,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,OAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa;AAAA,EAAA;AAEjB;", "x_google_ignoreList": [0, 1, 2, 3]}
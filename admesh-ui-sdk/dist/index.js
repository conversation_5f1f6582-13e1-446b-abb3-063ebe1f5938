"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const g=require("react");function me(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}var P={exports:{}},S={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var re;function he(){if(re)return S;re=1;var s=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function o(m,d,a){var l=null;if(a!==void 0&&(l=""+a),d.key!==void 0&&(l=""+d.key),"key"in d){a={};for(var u in d)u!=="key"&&(a[u]=d[u])}else a=d;return d=a.ref,{$$typeof:s,type:m,key:l,ref:d!==void 0?d:null,props:a}}return S.Fragment=n,S.jsx=o,S.jsxs=o,S}var C={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var se;function fe(){return se||(se=1,process.env.NODE_ENV!=="production"&&function(){function s(r){if(r==null)return null;if(typeof r=="function")return r.$$typeof===ie?null:r.displayName||r.name||null;if(typeof r=="string")return r;switch(r){case b:return"Fragment";case x:return"Profiler";case j:return"StrictMode";case $:return"Suspense";case F:return"SuspenseList";case ce:return"Activity"}if(typeof r=="object")switch(typeof r.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),r.$$typeof){case h:return"Portal";case G:return(r.displayName||"Context")+".Provider";case w:return(r._context.displayName||"Context")+".Consumer";case O:var i=r.render;return r=r.displayName,r||(r=i.displayName||i.name||"",r=r!==""?"ForwardRef("+r+")":"ForwardRef"),r;case le:return i=r.displayName||null,i!==null?i:s(r.type)||"Memo";case J:i=r._payload,r=r._init;try{return s(r(i))}catch{}}return null}function n(r){return""+r}function o(r){try{n(r);var i=!1}catch{i=!0}if(i){i=console;var f=i.error,y=typeof Symbol=="function"&&Symbol.toStringTag&&r[Symbol.toStringTag]||r.constructor.name||"Object";return f.call(i,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",y),n(r)}}function m(r){if(r===b)return"<>";if(typeof r=="object"&&r!==null&&r.$$typeof===J)return"<...>";try{var i=s(r);return i?"<"+i+">":"<...>"}catch{return"<...>"}}function d(){var r=L.A;return r===null?null:r.getOwner()}function a(){return Error("react-stack-top-frame")}function l(r){if(X.call(r,"key")){var i=Object.getOwnPropertyDescriptor(r,"key").get;if(i&&i.isReactWarning)return!1}return r.key!==void 0}function u(r,i){function f(){K||(K=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",i))}f.isReactWarning=!0,Object.defineProperty(r,"key",{get:f,configurable:!0})}function k(){var r=s(this.type);return H[r]||(H[r]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),r=this.props.ref,r!==void 0?r:null}function N(r,i,f,y,E,T,U,V){return f=T.ref,r={$$typeof:_,type:r,key:i,props:T,_owner:E},(f!==void 0?f:null)!==null?Object.defineProperty(r,"ref",{enumerable:!1,get:k}):Object.defineProperty(r,"ref",{enumerable:!1,value:null}),r._store={},Object.defineProperty(r._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(r,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(r,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:U}),Object.defineProperty(r,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:V}),Object.freeze&&(Object.freeze(r.props),Object.freeze(r)),r}function p(r,i,f,y,E,T,U,V){var v=i.children;if(v!==void 0)if(y)if(de(v)){for(y=0;y<v.length;y++)t(v[y]);Object.freeze&&Object.freeze(v)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else t(v);if(X.call(i,"key")){v=s(r);var A=Object.keys(i).filter(function(ue){return ue!=="key"});y=0<A.length?"{key: someKey, "+A.join(": ..., ")+": ...}":"{key: someKey}",ee[v+y]||(A=0<A.length?"{"+A.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,y,v,A,v),ee[v+y]=!0)}if(v=null,f!==void 0&&(o(f),v=""+f),l(i)&&(o(i.key),v=""+i.key),"key"in i){f={};for(var D in i)D!=="key"&&(f[D]=i[D])}else f=i;return v&&u(f,typeof r=="function"?r.displayName||r.name||"Unknown":r),N(r,v,T,E,d(),f,U,V)}function t(r){typeof r=="object"&&r!==null&&r.$$typeof===_&&r._store&&(r._store.validated=1)}var c=g,_=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),b=Symbol.for("react.fragment"),j=Symbol.for("react.strict_mode"),x=Symbol.for("react.profiler"),w=Symbol.for("react.consumer"),G=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),$=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),le=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),ce=Symbol.for("react.activity"),ie=Symbol.for("react.client.reference"),L=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=Object.prototype.hasOwnProperty,de=Array.isArray,Y=console.createTask?console.createTask:function(){return null};c={"react-stack-bottom-frame":function(r){return r()}};var K,H={},Z=c["react-stack-bottom-frame"].bind(c,a)(),Q=Y(m(a)),ee={};C.Fragment=b,C.jsx=function(r,i,f,y,E){var T=1e4>L.recentlyCreatedOwnerStacks++;return p(r,i,f,!1,y,E,T?Error("react-stack-top-frame"):Z,T?Y(m(r)):Q)},C.jsxs=function(r,i,f,y,E){var T=1e4>L.recentlyCreatedOwnerStacks++;return p(r,i,f,!0,y,E,T?Error("react-stack-top-frame"):Z,T?Y(m(r)):Q)}}()),C}var ae;function pe(){return ae||(ae=1,process.env.NODE_ENV==="production"?P.exports=he():P.exports=fe()),P.exports}var e=pe(),q={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var te;function xe(){return te||(te=1,function(s){(function(){var n={}.hasOwnProperty;function o(){for(var a="",l=0;l<arguments.length;l++){var u=arguments[l];u&&(a=d(a,m(u)))}return a}function m(a){if(typeof a=="string"||typeof a=="number")return a;if(typeof a!="object")return"";if(Array.isArray(a))return o.apply(null,a);if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]"))return a.toString();var l="";for(var u in a)n.call(a,u)&&a[u]&&(l=d(l,u));return l}function d(a,l){return l?a?a+" "+l:a+l:a}s.exports?(o.default=o,s.exports=o):window.classNames=o})()}(q)),q.exports}var _e=xe();const M=me(_e),be={"Top Match":"primary","Free Tier":"success","AI Powered":"secondary",Popular:"warning",New:"primary","Trial Available":"success"},ge={"Top Match":"★","Free Tier":"◆","AI Powered":"◉",Popular:"▲",New:"●","Trial Available":"◈"},R=({type:s,variant:n,size:o="md",className:m})=>{const d=n||be[s]||"secondary",a=ge[s],l=M("admesh-component","admesh-badge",`admesh-badge--${d}`,`admesh-badge--${o}`,m);return e.jsxs("span",{className:l,children:[a&&e.jsx("span",{className:"admesh-badge__icon",children:a}),e.jsx("span",{className:"admesh-badge__text",children:s})]})};R.displayName="AdMeshBadge";const ye="https://api.useadmesh.com/track";let z={apiBaseUrl:ye,enabled:!0,debug:!1,retryAttempts:3,retryDelay:1e3};const ve=s=>{z={...z,...s}},ne=s=>{const[n,o]=g.useState(!1),[m,d]=g.useState(null),a={...z,...s},l=g.useCallback((t,c)=>{a.debug&&console.log(`[AdMesh Tracker] ${t}`,c)},[a.debug]),u=g.useCallback(async(t,c)=>{if(!a.enabled){l("Tracking disabled, skipping event",{eventType:t,data:c});return}if(!c.adId||!c.admeshLink){const j="Missing required tracking data: adId and admeshLink are required";l(j,c),d(j);return}o(!0),d(null);const _={event_type:t,ad_id:c.adId,admesh_link:c.admeshLink,product_id:c.productId,user_id:c.userId,session_id:c.sessionId,revenue:c.revenue,conversion_type:c.conversionType,metadata:c.metadata,timestamp:new Date().toISOString(),user_agent:navigator.userAgent,referrer:document.referrer,page_url:window.location.href};l(`Sending ${t} event`,_);let h=null;for(let j=1;j<=(a.retryAttempts||3);j++)try{const x=await fetch(`${a.apiBaseUrl}/events`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(_)});if(!x.ok)throw new Error(`HTTP ${x.status}: ${x.statusText}`);const w=await x.json();l(`${t} event tracked successfully`,w),o(!1);return}catch(x){h=x,l(`Attempt ${j} failed for ${t} event`,x),j<(a.retryAttempts||3)&&await new Promise(w=>setTimeout(w,(a.retryDelay||1e3)*j))}const b=`Failed to track ${t} event after ${a.retryAttempts} attempts: ${h==null?void 0:h.message}`;l(b,h),d(b),o(!1)},[a,l]),k=g.useCallback(async t=>u("click",t),[u]),N=g.useCallback(async t=>u("view",t),[u]),p=g.useCallback(async t=>(!t.revenue&&!t.conversionType&&l("Warning: Conversion tracking without revenue or conversion type",t),u("conversion",t)),[u]);return{trackClick:k,trackView:N,trackConversion:p,isTracking:n,error:m}},je=(s,n,o)=>{try{const m=new URL(s);return m.searchParams.set("ad_id",n),m.searchParams.set("utm_source","admesh"),m.searchParams.set("utm_medium","recommendation"),o&&Object.entries(o).forEach(([d,a])=>{m.searchParams.set(d,a)}),m.toString()}catch{return console.warn("[AdMesh] Invalid URL provided to buildAdMeshLink:",s),s}},Ne=(s,n)=>({adId:s.ad_id,admeshLink:s.admesh_link,productId:s.product_id,...n}),I=({adId:s,admeshLink:n,productId:o,children:m,onClick:d,trackingData:a,className:l})=>{const{trackClick:u,trackView:k}=ne(),N=g.useRef(null),p=g.useRef(!1);g.useEffect(()=>{if(!N.current||p.current)return;const c=new IntersectionObserver(_=>{_.forEach(h=>{h.isIntersecting&&!p.current&&(p.current=!0,k({adId:s,admeshLink:n,productId:o,...a}).catch(console.error))})},{threshold:.5,rootMargin:"0px"});return c.observe(N.current),()=>{c.disconnect()}},[s,n,o,a,k]);const t=g.useCallback(async c=>{try{await u({adId:s,admeshLink:n,productId:o,...a})}catch(b){console.error("Failed to track click:",b)}d&&d(),c.target.closest("a")||window.open(n,"_blank","noopener,noreferrer")},[s,n,o,a,u,d]);return e.jsx("div",{ref:N,className:l,onClick:t,style:{cursor:"pointer"},children:m})};I.displayName="AdMeshLinkTracker";const W=({recommendation:s,theme:n,showMatchScore:o=!0,showBadges:m=!0,maxKeywords:d=3,onClick:a,onTrackView:l,className:u})=>{var h,b,j;const k=g.useMemo(()=>{var O;const x=[];s.intent_match_score>=.8&&x.push("Top Match"),s.has_free_tier&&x.push("Free Tier"),s.trial_days&&s.trial_days>0&&x.push("Trial Available");const w=["ai","artificial intelligence","machine learning","ml","automation"];return(((O=s.keywords)==null?void 0:O.some($=>w.some(F=>$.toLowerCase().includes(F))))||s.title.toLowerCase().includes("ai"))&&x.push("AI Powered"),x},[s]),N=Math.round(s.intent_match_score*100),p=((h=s.keywords)==null?void 0:h.slice(0,d))||[],t=(((b=s.keywords)==null?void 0:b.length)||0)>d,c=M("admesh-component","admesh-card","group relative cursor-pointer transition-all duration-300 hover:-translate-y-1 hover:shadow-xl","bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm","overflow-hidden",u),_=n!=null&&n.accentColor?{"--admesh-primary":n.accentColor,"--admesh-primary-hover":n.accentColor+"dd"}:void 0;return e.jsxs(I,{adId:s.ad_id,admeshLink:s.admesh_link,productId:s.product_id,onClick:()=>a==null?void 0:a(s.ad_id,s.admesh_link),trackingData:{title:s.title,matchScore:s.intent_match_score},className:c,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"}),e.jsxs("div",{className:"relative p-8 h-full flex flex-col gap-6 z-10",style:_,"data-admesh-theme":n==null?void 0:n.mode,children:[e.jsxs("div",{className:"flex justify-between items-start gap-4 mb-2",children:[m&&k.length>0&&e.jsx("div",{className:"flex flex-wrap gap-3 flex-1",children:k.map((x,w)=>e.jsx(R,{type:x,size:"sm"},`${x}-${w}`))}),o&&e.jsxs("div",{className:"flex-shrink-0 inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-lg text-sm font-semibold shadow-lg relative overflow-hidden",children:[e.jsx("div",{className:"w-2 h-2 bg-white rounded-full shadow-sm animate-pulse"}),e.jsxs("span",{children:[N,"% match"]}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"})]})]}),e.jsxs("div",{className:"flex-1 flex flex-col gap-5",children:[e.jsx("h3",{className:"text-2xl font-bold leading-tight text-gray-900 dark:text-white bg-gradient-to-r from-gray-900 to-indigo-600 dark:from-white dark:to-indigo-400 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300",children:s.title}),e.jsx("p",{className:"text-base leading-relaxed text-gray-600 dark:text-gray-300 font-normal",children:s.reason}),p.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-2",children:[p.map((x,w)=>e.jsx("span",{className:"admesh-badge admesh-badge--secondary admesh-badge--sm",children:x},w)),t&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 italic",children:["+",(((j=s.keywords)==null?void 0:j.length)||0)-d," more"]})]}),e.jsxs("div",{className:"space-y-2",children:[s.pricing&&e.jsxs("div",{className:"text-sm",children:[e.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Pricing: "}),e.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:s.pricing})]}),s.trial_days&&s.trial_days>0&&e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[s.trial_days,"-day free trial"]})]})]}),e.jsx("div",{className:"mt-auto pt-6 border-t border-gray-200/50 dark:border-gray-700/50",children:e.jsxs("button",{className:"admesh-button admesh-button--primary w-full relative overflow-hidden bg-gradient-to-r from-indigo-500 to-indigo-600 text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300",children:[e.jsx("span",{className:"relative z-10",children:"Visit Offer"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"}),e.jsxs("span",{className:"sr-only",children:["for ",s.title]})]})})]})]})};W.displayName="AdMeshProductCard";const B=({recommendations:s,theme:n,maxProducts:o=3,showMatchScores:m=!0,showFeatures:d=!0,onProductClick:a,className:l})=>{const u=g.useMemo(()=>s.slice(0,o),[s,o]),k=g.useMemo(()=>{const t=new Set;return u.forEach(c=>{var _;(_=c.features)==null||_.forEach(h=>t.add(h))}),Array.from(t).slice(0,8)},[u]),N=M("admesh-component","admesh-compare-table",{[`admesh-compare-table--${n==null?void 0:n.mode}`]:n==null?void 0:n.mode},l),p=n!=null&&n.accentColor?{"--admesh-primary":n.accentColor}:void 0;return u.length===0?e.jsx("div",{className:N,children:e.jsx("div",{className:"admesh-compare-table__empty",children:e.jsx("p",{className:"admesh-text-muted",children:"No products to compare"})})}):e.jsx("div",{className:N,style:p,"data-admesh-theme":n==null?void 0:n.mode,children:e.jsxs("div",{className:"admesh-compare-table__container",children:[e.jsxs("div",{className:"admesh-compare-table__header",children:[e.jsx("h3",{className:"admesh-compare-table__title admesh-text-xl admesh-font-semibold",children:"Product Comparison"}),e.jsxs("p",{className:"admesh-compare-table__subtitle admesh-text-sm admesh-text-muted",children:["Compare ",u.length," products side by side"]})]}),e.jsx("div",{className:"admesh-compare-table__scroll-container",children:e.jsxs("table",{className:"admesh-compare-table__table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"admesh-compare-table__row-header",children:e.jsx("span",{className:"admesh-sr-only",children:"Feature"})}),u.map((t,c)=>e.jsx("th",{className:"admesh-compare-table__product-header",children:e.jsxs(I,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,onClick:()=>a==null?void 0:a(t.ad_id,t.admesh_link),className:"admesh-compare-table__product-header-content",children:[e.jsxs("div",{className:"admesh-compare-table__product-title",children:[e.jsx("h4",{className:"admesh-text-base admesh-font-semibold admesh-truncate",children:t.title}),m&&e.jsx("div",{className:"admesh-compare-table__match-score",children:e.jsxs("span",{className:"admesh-text-xs admesh-text-muted",children:[Math.round(t.intent_match_score*100),"% match"]})})]}),e.jsxs("div",{className:"admesh-compare-table__badges",children:[t.has_free_tier&&e.jsx(R,{type:"Free Tier",size:"sm"}),t.trial_days&&t.trial_days>0&&e.jsx(R,{type:"Trial Available",size:"sm"}),t.intent_match_score>=.8&&e.jsx(R,{type:"Top Match",size:"sm"})]}),e.jsx("button",{className:"admesh-button admesh-button--primary admesh-button--sm admesh-compare-table__cta",children:"Visit Offer"})]})},t.product_id||c))]})}),e.jsxs("tbody",{children:[e.jsxs("tr",{children:[e.jsx("td",{className:"admesh-compare-table__row-header admesh-font-medium",children:"Pricing"}),u.map((t,c)=>e.jsx("td",{className:"admesh-compare-table__cell",children:e.jsx("span",{className:"admesh-text-sm",children:t.pricing||"Contact for pricing"})},t.product_id||c))]}),e.jsxs("tr",{children:[e.jsx("td",{className:"admesh-compare-table__row-header admesh-font-medium",children:"Free Trial"}),u.map((t,c)=>e.jsx("td",{className:"admesh-compare-table__cell",children:e.jsx("span",{className:"admesh-text-sm",children:t.trial_days?`${t.trial_days} days`:"No trial"})},t.product_id||c))]}),d&&k.map((t,c)=>e.jsxs("tr",{children:[e.jsx("td",{className:"admesh-compare-table__row-header admesh-font-medium",children:t}),u.map((_,h)=>{var b;return e.jsx("td",{className:"admesh-compare-table__cell",children:e.jsx("span",{className:"admesh-text-sm",children:(b=_.features)!=null&&b.includes(t)?e.jsx("span",{className:"admesh-compare-table__check",children:"✓"}):e.jsx("span",{className:"admesh-compare-table__cross",children:"—"})})},_.product_id||h)})]},c)),e.jsxs("tr",{children:[e.jsx("td",{className:"admesh-compare-table__row-header admesh-font-medium",children:"Keywords"}),u.map((t,c)=>{var _,h,b;return e.jsx("td",{className:"admesh-compare-table__cell",children:e.jsxs("div",{className:"admesh-compare-table__keywords",children:[(_=t.keywords)==null?void 0:_.slice(0,3).map((j,x)=>e.jsx("span",{className:"admesh-badge admesh-badge--secondary admesh-badge--sm",children:j},x)),(((h=t.keywords)==null?void 0:h.length)||0)>3&&e.jsxs("span",{className:"admesh-text-xs admesh-text-muted",children:["+",(((b=t.keywords)==null?void 0:b.length)||0)-3]})]})},t.product_id||c)})]})]})]})})]})})};B.displayName="AdMeshCompareTable";const ke=(s,n,o)=>{if(!o&&n)switch(n){case"compare_products":return"compare";case"best_for_use_case":case"trial_demo":case"budget_conscious":return"cards";default:return"cards"}const m=s.length;if(m>=2&&m<=4){const d=s.some(l=>l.features&&l.features.length>0),a=s.some(l=>l.pricing);if(d||a)return"compare"}return"cards"},oe=({recommendations:s,intentType:n,theme:o,maxDisplayed:m=6,showMatchScores:d=!0,showFeatures:a=!0,autoLayout:l=!0,onProductClick:u,onTrackView:k,className:N})=>{const p=g.useMemo(()=>s.slice(0,m),[s,m]),t=g.useMemo(()=>ke(p,n,l),[p,n,l]),c=M("admesh-component","admesh-layout",`admesh-layout--${t}`,{[`admesh-layout--${o==null?void 0:o.mode}`]:o==null?void 0:o.mode},N),_=o!=null&&o.accentColor?{"--admesh-primary":o.accentColor}:void 0;return p.length===0?e.jsx("div",{className:c,children:e.jsx("div",{className:"admesh-layout__empty",children:e.jsxs("div",{className:"admesh-layout__empty-content",children:[e.jsx("h3",{className:"admesh-text-lg admesh-font-semibold admesh-text-muted",children:"No recommendations available"}),e.jsx("p",{className:"admesh-text-sm admesh-text-muted",children:"Try adjusting your search criteria or check back later."})]})})}):e.jsx("div",{className:c,style:_,"data-admesh-theme":o==null?void 0:o.mode,children:t==="compare"?e.jsx(B,{recommendations:p,theme:o,maxProducts:Math.min(p.length,4),showMatchScores:d,showFeatures:a,onProductClick:u}):e.jsxs("div",{className:"admesh-layout__cards-container",children:[e.jsxs("div",{className:"admesh-layout__header",children:[e.jsx("h3",{className:"admesh-layout__title admesh-text-xl admesh-font-semibold",children:"Recommended Products"}),e.jsxs("p",{className:"admesh-layout__subtitle admesh-text-sm admesh-text-muted",children:[p.length," product",p.length!==1?"s":""," found"]})]}),e.jsx("div",{className:"admesh-layout__cards-grid",children:p.map((h,b)=>e.jsx(W,{recommendation:h,theme:o,showMatchScore:d,showBadges:!0,maxKeywords:3,onClick:u,onTrackView:k},h.product_id||h.ad_id||b))}),s.length>m&&e.jsx("div",{className:"admesh-layout__more-indicator",children:e.jsxs("p",{className:"admesh-text-sm admesh-text-muted",children:["Showing ",m," of ",s.length," recommendations"]})})]})})};oe.displayName="AdMeshLayout";const we="0.1.0",Te={trackingEnabled:!0,debug:!1,theme:{mode:"light",accentColor:"#2563eb"}};exports.AdMeshBadge=R;exports.AdMeshCompareTable=B;exports.AdMeshLayout=oe;exports.AdMeshLinkTracker=I;exports.AdMeshProductCard=W;exports.DEFAULT_CONFIG=Te;exports.VERSION=we;exports.buildAdMeshLink=je;exports.extractTrackingData=Ne;exports.setAdMeshTrackerConfig=ve;exports.useAdMeshTracker=ne;
//# sourceMappingURL=index.js.map

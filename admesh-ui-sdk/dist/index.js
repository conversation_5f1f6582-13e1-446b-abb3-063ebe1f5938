"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const y=require("react");function me(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var P={exports:{}},S={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var se;function he(){if(se)return S;se=1;var r=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function c(m,i,a){var o=null;if(a!==void 0&&(o=""+a),i.key!==void 0&&(o=""+i.key),"key"in i){a={};for(var u in i)u!=="key"&&(a[u]=i[u])}else a=i;return i=a.ref,{$$typeof:r,type:m,key:o,ref:i!==void 0?i:null,props:a}}return S.Fragment=n,S.jsx=c,S.jsxs=c,S}var C={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var re;function fe(){return re||(re=1,process.env.NODE_ENV!=="production"&&function(){function r(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===le?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case x:return"Fragment";case p:return"Profiler";case v:return"StrictMode";case $:return"Suspense";case F:return"SuspenseList";case de:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case h:return"Portal";case G:return(e.displayName||"Context")+".Provider";case T:return(e._context.displayName||"Context")+".Consumer";case O:var l=e.render;return e=e.displayName,e||(e=l.displayName||l.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case oe:return l=e.displayName||null,l!==null?l:r(e.type)||"Memo";case J:l=e._payload,e=e._init;try{return r(e(l))}catch{}}return null}function n(e){return""+e}function c(e){try{n(e);var l=!1}catch{l=!0}if(l){l=console;var f=l.error,j=typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return f.call(l,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",j),n(e)}}function m(e){if(e===x)return"<>";if(typeof e=="object"&&e!==null&&e.$$typeof===J)return"<...>";try{var l=r(e);return l?"<"+l+">":"<...>"}catch{return"<...>"}}function i(){var e=L.A;return e===null?null:e.getOwner()}function a(){return Error("react-stack-top-frame")}function o(e){if(X.call(e,"key")){var l=Object.getOwnPropertyDescriptor(e,"key").get;if(l&&l.isReactWarning)return!1}return e.key!==void 0}function u(e,l){function f(){K||(K=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",l))}f.isReactWarning=!0,Object.defineProperty(e,"key",{get:f,configurable:!0})}function k(){var e=r(this.type);return H[e]||(H[e]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),e=this.props.ref,e!==void 0?e:null}function N(e,l,f,j,E,w,U,V){return f=w.ref,e={$$typeof:b,type:e,key:l,props:w,_owner:E},(f!==void 0?f:null)!==null?Object.defineProperty(e,"ref",{enumerable:!1,get:k}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:U}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:V}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function _(e,l,f,j,E,w,U,V){var g=l.children;if(g!==void 0)if(j)if(ie(g)){for(j=0;j<g.length;j++)t(g[j]);Object.freeze&&Object.freeze(g)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else t(g);if(X.call(l,"key")){g=r(e);var A=Object.keys(l).filter(function(ue){return ue!=="key"});j=0<A.length?"{key: someKey, "+A.join(": ..., ")+": ...}":"{key: someKey}",ee[g+j]||(A=0<A.length?"{"+A.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,j,g,A,g),ee[g+j]=!0)}if(g=null,f!==void 0&&(c(f),g=""+f),o(l)&&(c(l.key),g=""+l.key),"key"in l){f={};for(var D in l)D!=="key"&&(f[D]=l[D])}else f=l;return g&&u(f,typeof e=="function"?e.displayName||e.name||"Unknown":e),N(e,g,w,E,i(),f,U,V)}function t(e){typeof e=="object"&&e!==null&&e.$$typeof===b&&e._store&&(e._store.validated=1)}var d=y,b=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),T=Symbol.for("react.consumer"),G=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),$=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),oe=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),de=Symbol.for("react.activity"),le=Symbol.for("react.client.reference"),L=d.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=Object.prototype.hasOwnProperty,ie=Array.isArray,Y=console.createTask?console.createTask:function(){return null};d={"react-stack-bottom-frame":function(e){return e()}};var K,H={},Z=d["react-stack-bottom-frame"].bind(d,a)(),Q=Y(m(a)),ee={};C.Fragment=x,C.jsx=function(e,l,f,j,E){var w=1e4>L.recentlyCreatedOwnerStacks++;return _(e,l,f,!1,j,E,w?Error("react-stack-top-frame"):Z,w?Y(m(e)):Q)},C.jsxs=function(e,l,f,j,E){var w=1e4>L.recentlyCreatedOwnerStacks++;return _(e,l,f,!0,j,E,w?Error("react-stack-top-frame"):Z,w?Y(m(e)):Q)}}()),C}var ae;function _e(){return ae||(ae=1,process.env.NODE_ENV==="production"?P.exports=he():P.exports=fe()),P.exports}var s=_e(),q={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var te;function pe(){return te||(te=1,function(r){(function(){var n={}.hasOwnProperty;function c(){for(var a="",o=0;o<arguments.length;o++){var u=arguments[o];u&&(a=i(a,m(u)))}return a}function m(a){if(typeof a=="string"||typeof a=="number")return a;if(typeof a!="object")return"";if(Array.isArray(a))return c.apply(null,a);if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]"))return a.toString();var o="";for(var u in a)n.call(a,u)&&a[u]&&(o=i(o,u));return o}function i(a,o){return o?a?a+" "+o:a+o:a}r.exports?(c.default=c,r.exports=c):window.classNames=c})()}(q)),q.exports}var be=pe();const M=me(be),xe={"Top Match":"primary","Free Tier":"success","AI Powered":"secondary",Popular:"warning",New:"primary","Trial Available":"success"},ye={"Top Match":"★","Free Tier":"◆","AI Powered":"◉",Popular:"▲",New:"●","Trial Available":"◈"},R=({type:r,variant:n,size:c="md",className:m})=>{const i=n||xe[r]||"secondary",a=ye[r],o=M("admesh-component","admesh-badge",`admesh-badge--${i}`,`admesh-badge--${c}`,m);return s.jsxs("span",{className:o,children:[a&&s.jsx("span",{className:"admesh-badge__icon",children:a}),s.jsx("span",{className:"admesh-badge__text",children:r})]})};R.displayName="AdMeshBadge";const je="https://api.useadmesh.com/track";let W={apiBaseUrl:je,enabled:!0,debug:!1,retryAttempts:3,retryDelay:1e3};const ge=r=>{W={...W,...r}},ne=r=>{const[n,c]=y.useState(!1),[m,i]=y.useState(null),a={...W,...r},o=y.useCallback((t,d)=>{a.debug&&console.log(`[AdMesh Tracker] ${t}`,d)},[a.debug]),u=y.useCallback(async(t,d)=>{if(!a.enabled){o("Tracking disabled, skipping event",{eventType:t,data:d});return}if(!d.adId||!d.admeshLink){const v="Missing required tracking data: adId and admeshLink are required";o(v,d),i(v);return}c(!0),i(null);const b={event_type:t,ad_id:d.adId,admesh_link:d.admeshLink,product_id:d.productId,user_id:d.userId,session_id:d.sessionId,revenue:d.revenue,conversion_type:d.conversionType,metadata:d.metadata,timestamp:new Date().toISOString(),user_agent:navigator.userAgent,referrer:document.referrer,page_url:window.location.href};o(`Sending ${t} event`,b);let h=null;for(let v=1;v<=(a.retryAttempts||3);v++)try{const p=await fetch(`${a.apiBaseUrl}/events`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!p.ok)throw new Error(`HTTP ${p.status}: ${p.statusText}`);const T=await p.json();o(`${t} event tracked successfully`,T),c(!1);return}catch(p){h=p,o(`Attempt ${v} failed for ${t} event`,p),v<(a.retryAttempts||3)&&await new Promise(T=>setTimeout(T,(a.retryDelay||1e3)*v))}const x=`Failed to track ${t} event after ${a.retryAttempts} attempts: ${h==null?void 0:h.message}`;o(x,h),i(x),c(!1)},[a,o]),k=y.useCallback(async t=>u("click",t),[u]),N=y.useCallback(async t=>u("view",t),[u]),_=y.useCallback(async t=>(!t.revenue&&!t.conversionType&&o("Warning: Conversion tracking without revenue or conversion type",t),u("conversion",t)),[u]);return{trackClick:k,trackView:N,trackConversion:_,isTracking:n,error:m}},ve=(r,n,c)=>{try{const m=new URL(r);return m.searchParams.set("ad_id",n),m.searchParams.set("utm_source","admesh"),m.searchParams.set("utm_medium","recommendation"),c&&Object.entries(c).forEach(([i,a])=>{m.searchParams.set(i,a)}),m.toString()}catch{return console.warn("[AdMesh] Invalid URL provided to buildAdMeshLink:",r),r}},Ne=(r,n)=>({adId:r.ad_id,admeshLink:r.admesh_link,productId:r.product_id,...n}),I=({adId:r,admeshLink:n,productId:c,children:m,onClick:i,trackingData:a,className:o})=>{const{trackClick:u,trackView:k}=ne(),N=y.useRef(null),_=y.useRef(!1);y.useEffect(()=>{if(!N.current||_.current)return;const d=new IntersectionObserver(b=>{b.forEach(h=>{h.isIntersecting&&!_.current&&(_.current=!0,k({adId:r,admeshLink:n,productId:c,...a}).catch(console.error))})},{threshold:.5,rootMargin:"0px"});return d.observe(N.current),()=>{d.disconnect()}},[r,n,c,a,k]);const t=y.useCallback(async d=>{try{await u({adId:r,admeshLink:n,productId:c,...a})}catch(x){console.error("Failed to track click:",x)}i&&i(),d.target.closest("a")||window.open(n,"_blank","noopener,noreferrer")},[r,n,c,a,u,i]);return s.jsx("div",{ref:N,className:o,onClick:t,style:{cursor:"pointer"},children:m})};I.displayName="AdMeshLinkTracker";const z=({recommendation:r,theme:n,showMatchScore:c=!0,showBadges:m=!0,maxKeywords:i=3,onClick:a,onTrackView:o,className:u})=>{var h,x,v;const k=y.useMemo(()=>{var O;const p=[];r.intent_match_score>=.8&&p.push("Top Match"),r.has_free_tier&&p.push("Free Tier"),r.trial_days&&r.trial_days>0&&p.push("Trial Available");const T=["ai","artificial intelligence","machine learning","ml","automation"];return(((O=r.keywords)==null?void 0:O.some($=>T.some(F=>$.toLowerCase().includes(F))))||r.title.toLowerCase().includes("ai"))&&p.push("AI Powered"),p},[r]),N=Math.round(r.intent_match_score*100),_=((h=r.keywords)==null?void 0:h.slice(0,i))||[],t=(((x=r.keywords)==null?void 0:x.length)||0)>i,d=M("admesh-component","admesh-card","admesh-product-card",{[`admesh-product-card--${n==null?void 0:n.mode}`]:n==null?void 0:n.mode},u),b=n!=null&&n.accentColor?{"--admesh-primary":n.accentColor,"--admesh-primary-hover":n.accentColor+"dd"}:void 0;return s.jsx(I,{adId:r.ad_id,admeshLink:r.admesh_link,productId:r.product_id,onClick:()=>a==null?void 0:a(r.ad_id,r.admesh_link),trackingData:{title:r.title,matchScore:r.intent_match_score},className:d,children:s.jsxs("div",{className:"admesh-product-card__container",style:b,"data-admesh-theme":n==null?void 0:n.mode,children:[s.jsxs("div",{className:"admesh-product-card__header",children:[m&&k.length>0&&s.jsx("div",{className:"admesh-product-card__badges",children:k.map((p,T)=>s.jsx(R,{type:p,size:"sm"},`${p}-${T}`))}),c&&s.jsx("div",{className:"admesh-product-card__match-score",children:s.jsxs("span",{className:"admesh-text-xs admesh-text-muted",children:[N,"% match"]})})]}),s.jsxs("div",{className:"admesh-product-card__content",children:[s.jsx("h3",{className:"admesh-product-card__title admesh-text-lg admesh-font-semibold",children:r.title}),s.jsx("p",{className:"admesh-product-card__reason admesh-text-sm admesh-text-secondary",children:r.reason}),_.length>0&&s.jsxs("div",{className:"admesh-product-card__keywords",children:[_.map((p,T)=>s.jsx("span",{className:"admesh-product-card__keyword admesh-badge admesh-badge--secondary admesh-badge--sm",children:p},T)),t&&s.jsxs("span",{className:"admesh-product-card__keyword-more admesh-text-xs admesh-text-muted",children:["+",(((v=r.keywords)==null?void 0:v.length)||0)-i," more"]})]}),s.jsxs("div",{className:"admesh-product-card__meta",children:[r.pricing&&s.jsxs("div",{className:"admesh-product-card__pricing admesh-text-sm",children:[s.jsx("span",{className:"admesh-text-muted",children:"Pricing: "}),s.jsx("span",{className:"admesh-font-medium",children:r.pricing})]}),r.trial_days&&r.trial_days>0&&s.jsxs("div",{className:"admesh-product-card__trial admesh-text-sm admesh-text-muted",children:[r.trial_days,"-day free trial"]})]})]}),s.jsx("div",{className:"admesh-product-card__footer",children:s.jsxs("button",{className:"admesh-button admesh-button--primary admesh-product-card__cta",children:["Visit Offer",s.jsxs("span",{className:"admesh-sr-only",children:["for ",r.title]})]})})]})})};z.displayName="AdMeshProductCard";const B=({recommendations:r,theme:n,maxProducts:c=3,showMatchScores:m=!0,showFeatures:i=!0,onProductClick:a,className:o})=>{const u=y.useMemo(()=>r.slice(0,c),[r,c]),k=y.useMemo(()=>{const t=new Set;return u.forEach(d=>{var b;(b=d.features)==null||b.forEach(h=>t.add(h))}),Array.from(t).slice(0,8)},[u]),N=M("admesh-component","admesh-compare-table",{[`admesh-compare-table--${n==null?void 0:n.mode}`]:n==null?void 0:n.mode},o),_=n!=null&&n.accentColor?{"--admesh-primary":n.accentColor}:void 0;return u.length===0?s.jsx("div",{className:N,children:s.jsx("div",{className:"admesh-compare-table__empty",children:s.jsx("p",{className:"admesh-text-muted",children:"No products to compare"})})}):s.jsx("div",{className:N,style:_,"data-admesh-theme":n==null?void 0:n.mode,children:s.jsxs("div",{className:"admesh-compare-table__container",children:[s.jsxs("div",{className:"admesh-compare-table__header",children:[s.jsx("h3",{className:"admesh-compare-table__title admesh-text-xl admesh-font-semibold",children:"Product Comparison"}),s.jsxs("p",{className:"admesh-compare-table__subtitle admesh-text-sm admesh-text-muted",children:["Compare ",u.length," products side by side"]})]}),s.jsx("div",{className:"admesh-compare-table__scroll-container",children:s.jsxs("table",{className:"admesh-compare-table__table",children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{className:"admesh-compare-table__row-header",children:s.jsx("span",{className:"admesh-sr-only",children:"Feature"})}),u.map((t,d)=>s.jsx("th",{className:"admesh-compare-table__product-header",children:s.jsxs(I,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,onClick:()=>a==null?void 0:a(t.ad_id,t.admesh_link),className:"admesh-compare-table__product-header-content",children:[s.jsxs("div",{className:"admesh-compare-table__product-title",children:[s.jsx("h4",{className:"admesh-text-base admesh-font-semibold admesh-truncate",children:t.title}),m&&s.jsx("div",{className:"admesh-compare-table__match-score",children:s.jsxs("span",{className:"admesh-text-xs admesh-text-muted",children:[Math.round(t.intent_match_score*100),"% match"]})})]}),s.jsxs("div",{className:"admesh-compare-table__badges",children:[t.has_free_tier&&s.jsx(R,{type:"Free Tier",size:"sm"}),t.trial_days&&t.trial_days>0&&s.jsx(R,{type:"Trial Available",size:"sm"}),t.intent_match_score>=.8&&s.jsx(R,{type:"Top Match",size:"sm"})]}),s.jsx("button",{className:"admesh-button admesh-button--primary admesh-button--sm admesh-compare-table__cta",children:"Visit Offer"})]})},t.product_id||d))]})}),s.jsxs("tbody",{children:[s.jsxs("tr",{children:[s.jsx("td",{className:"admesh-compare-table__row-header admesh-font-medium",children:"Pricing"}),u.map((t,d)=>s.jsx("td",{className:"admesh-compare-table__cell",children:s.jsx("span",{className:"admesh-text-sm",children:t.pricing||"Contact for pricing"})},t.product_id||d))]}),s.jsxs("tr",{children:[s.jsx("td",{className:"admesh-compare-table__row-header admesh-font-medium",children:"Free Trial"}),u.map((t,d)=>s.jsx("td",{className:"admesh-compare-table__cell",children:s.jsx("span",{className:"admesh-text-sm",children:t.trial_days?`${t.trial_days} days`:"No trial"})},t.product_id||d))]}),i&&k.map((t,d)=>s.jsxs("tr",{children:[s.jsx("td",{className:"admesh-compare-table__row-header admesh-font-medium",children:t}),u.map((b,h)=>{var x;return s.jsx("td",{className:"admesh-compare-table__cell",children:s.jsx("span",{className:"admesh-text-sm",children:(x=b.features)!=null&&x.includes(t)?s.jsx("span",{className:"admesh-compare-table__check",children:"✓"}):s.jsx("span",{className:"admesh-compare-table__cross",children:"—"})})},b.product_id||h)})]},d)),s.jsxs("tr",{children:[s.jsx("td",{className:"admesh-compare-table__row-header admesh-font-medium",children:"Keywords"}),u.map((t,d)=>{var b,h,x;return s.jsx("td",{className:"admesh-compare-table__cell",children:s.jsxs("div",{className:"admesh-compare-table__keywords",children:[(b=t.keywords)==null?void 0:b.slice(0,3).map((v,p)=>s.jsx("span",{className:"admesh-badge admesh-badge--secondary admesh-badge--sm",children:v},p)),(((h=t.keywords)==null?void 0:h.length)||0)>3&&s.jsxs("span",{className:"admesh-text-xs admesh-text-muted",children:["+",(((x=t.keywords)==null?void 0:x.length)||0)-3]})]})},t.product_id||d)})]})]})]})})]})})};B.displayName="AdMeshCompareTable";const ke=(r,n,c)=>{if(!c&&n)switch(n){case"compare_products":return"compare";case"best_for_use_case":case"trial_demo":case"budget_conscious":return"cards";default:return"cards"}const m=r.length;if(m>=2&&m<=4){const i=r.some(o=>o.features&&o.features.length>0),a=r.some(o=>o.pricing);if(i||a)return"compare"}return"cards"},ce=({recommendations:r,intentType:n,theme:c,maxDisplayed:m=6,showMatchScores:i=!0,showFeatures:a=!0,autoLayout:o=!0,onProductClick:u,onTrackView:k,className:N})=>{const _=y.useMemo(()=>r.slice(0,m),[r,m]),t=y.useMemo(()=>ke(_,n,o),[_,n,o]),d=M("admesh-component","admesh-layout",`admesh-layout--${t}`,{[`admesh-layout--${c==null?void 0:c.mode}`]:c==null?void 0:c.mode},N),b=c!=null&&c.accentColor?{"--admesh-primary":c.accentColor}:void 0;return _.length===0?s.jsx("div",{className:d,children:s.jsx("div",{className:"admesh-layout__empty",children:s.jsxs("div",{className:"admesh-layout__empty-content",children:[s.jsx("h3",{className:"admesh-text-lg admesh-font-semibold admesh-text-muted",children:"No recommendations available"}),s.jsx("p",{className:"admesh-text-sm admesh-text-muted",children:"Try adjusting your search criteria or check back later."})]})})}):s.jsx("div",{className:d,style:b,"data-admesh-theme":c==null?void 0:c.mode,children:t==="compare"?s.jsx(B,{recommendations:_,theme:c,maxProducts:Math.min(_.length,4),showMatchScores:i,showFeatures:a,onProductClick:u}):s.jsxs("div",{className:"admesh-layout__cards-container",children:[s.jsxs("div",{className:"admesh-layout__header",children:[s.jsx("h3",{className:"admesh-layout__title admesh-text-xl admesh-font-semibold",children:"Recommended Products"}),s.jsxs("p",{className:"admesh-layout__subtitle admesh-text-sm admesh-text-muted",children:[_.length," product",_.length!==1?"s":""," found"]})]}),s.jsx("div",{className:"admesh-layout__cards-grid",children:_.map((h,x)=>s.jsx(z,{recommendation:h,theme:c,showMatchScore:i,showBadges:!0,maxKeywords:3,onClick:u,onTrackView:k},h.product_id||h.ad_id||x))}),r.length>m&&s.jsx("div",{className:"admesh-layout__more-indicator",children:s.jsxs("p",{className:"admesh-text-sm admesh-text-muted",children:["Showing ",m," of ",r.length," recommendations"]})})]})})};ce.displayName="AdMeshLayout";const Te="0.1.0",we={trackingEnabled:!0,debug:!1,theme:{mode:"light",accentColor:"#2563eb"}};exports.AdMeshBadge=R;exports.AdMeshCompareTable=B;exports.AdMeshLayout=ce;exports.AdMeshLinkTracker=I;exports.AdMeshProductCard=z;exports.DEFAULT_CONFIG=we;exports.VERSION=Te;exports.buildAdMeshLink=ve;exports.extractTrackingData=Ne;exports.setAdMeshTrackerConfig=ge;exports.useAdMeshTracker=ne;
//# sourceMappingURL=index.js.map

import he, { useState as ee, use<PERSON><PERSON>back as A, useRef as se, useEffect as fe, use<PERSON>emo as P } from "react";
function _e(r) {
  return r && r.__esModule && Object.prototype.hasOwnProperty.call(r, "default") ? r.default : r;
}
var M = { exports: {} }, R = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var re;
function pe() {
  if (re) return R;
  re = 1;
  var r = Symbol.for("react.transitional.element"), n = Symbol.for("react.fragment");
  function c(m, i, a) {
    var o = null;
    if (a !== void 0 && (o = "" + a), i.key !== void 0 && (o = "" + i.key), "key" in i) {
      a = {};
      for (var u in i)
        u !== "key" && (a[u] = i[u]);
    } else a = i;
    return i = a.ref, {
      $$typeof: r,
      type: m,
      key: o,
      ref: i !== void 0 ? i : null,
      props: a
    };
  }
  return R.Fragment = n, R.jsx = c, R.jsxs = c, R;
}
var S = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ae;
function xe() {
  return ae || (ae = 1, process.env.NODE_ENV !== "production" && function() {
    function r(e) {
      if (e == null) return null;
      if (typeof e == "function")
        return e.$$typeof === ie ? null : e.displayName || e.name || null;
      if (typeof e == "string") return e;
      switch (e) {
        case b:
          return "Fragment";
        case p:
          return "Profiler";
        case v:
          return "StrictMode";
        case $:
          return "Suspense";
        case F:
          return "SuspenseList";
        case le:
          return "Activity";
      }
      if (typeof e == "object")
        switch (typeof e.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), e.$$typeof) {
          case h:
            return "Portal";
          case J:
            return (e.displayName || "Context") + ".Provider";
          case k:
            return (e._context.displayName || "Context") + ".Consumer";
          case C:
            var l = e.render;
            return e = e.displayName, e || (e = l.displayName || l.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
          case de:
            return l = e.displayName || null, l !== null ? l : r(e.type) || "Memo";
          case B:
            l = e._payload, e = e._init;
            try {
              return r(e(l));
            } catch {
            }
        }
      return null;
    }
    function n(e) {
      return "" + e;
    }
    function c(e) {
      try {
        n(e);
        var l = !1;
      } catch {
        l = !0;
      }
      if (l) {
        l = console;
        var f = l.error, y = typeof Symbol == "function" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return f.call(
          l,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          y
        ), n(e);
      }
    }
    function m(e) {
      if (e === b) return "<>";
      if (typeof e == "object" && e !== null && e.$$typeof === B)
        return "<...>";
      try {
        var l = r(e);
        return l ? "<" + l + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function i() {
      var e = L.A;
      return e === null ? null : e.getOwner();
    }
    function a() {
      return Error("react-stack-top-frame");
    }
    function o(e) {
      if (G.call(e, "key")) {
        var l = Object.getOwnPropertyDescriptor(e, "key").get;
        if (l && l.isReactWarning) return !1;
      }
      return e.key !== void 0;
    }
    function u(e, l) {
      function f() {
        X || (X = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          l
        ));
      }
      f.isReactWarning = !0, Object.defineProperty(e, "key", {
        get: f,
        configurable: !0
      });
    }
    function N() {
      var e = r(this.type);
      return K[e] || (K[e] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), e = this.props.ref, e !== void 0 ? e : null;
    }
    function g(e, l, f, y, T, w, U, V) {
      return f = w.ref, e = {
        $$typeof: x,
        type: e,
        key: l,
        props: w,
        _owner: T
      }, (f !== void 0 ? f : null) !== null ? Object.defineProperty(e, "ref", {
        enumerable: !1,
        get: N
      }) : Object.defineProperty(e, "ref", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(e, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(e, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: U
      }), Object.defineProperty(e, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: V
      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;
    }
    function _(e, l, f, y, T, w, U, V) {
      var j = l.children;
      if (j !== void 0)
        if (y)
          if (ue(j)) {
            for (y = 0; y < j.length; y++)
              t(j[y]);
            Object.freeze && Object.freeze(j);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else t(j);
      if (G.call(l, "key")) {
        j = r(e);
        var E = Object.keys(l).filter(function(me) {
          return me !== "key";
        });
        y = 0 < E.length ? "{key: someKey, " + E.join(": ..., ") + ": ...}" : "{key: someKey}", Q[j + y] || (E = 0 < E.length ? "{" + E.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          y,
          j,
          E,
          j
        ), Q[j + y] = !0);
      }
      if (j = null, f !== void 0 && (c(f), j = "" + f), o(l) && (c(l.key), j = "" + l.key), "key" in l) {
        f = {};
        for (var D in l)
          D !== "key" && (f[D] = l[D]);
      } else f = l;
      return j && u(
        f,
        typeof e == "function" ? e.displayName || e.name || "Unknown" : e
      ), g(
        e,
        j,
        w,
        T,
        i(),
        f,
        U,
        V
      );
    }
    function t(e) {
      typeof e == "object" && e !== null && e.$$typeof === x && e._store && (e._store.validated = 1);
    }
    var d = he, x = Symbol.for("react.transitional.element"), h = Symbol.for("react.portal"), b = Symbol.for("react.fragment"), v = Symbol.for("react.strict_mode"), p = Symbol.for("react.profiler"), k = Symbol.for("react.consumer"), J = Symbol.for("react.context"), C = Symbol.for("react.forward_ref"), $ = Symbol.for("react.suspense"), F = Symbol.for("react.suspense_list"), de = Symbol.for("react.memo"), B = Symbol.for("react.lazy"), le = Symbol.for("react.activity"), ie = Symbol.for("react.client.reference"), L = d.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, G = Object.prototype.hasOwnProperty, ue = Array.isArray, Y = console.createTask ? console.createTask : function() {
      return null;
    };
    d = {
      "react-stack-bottom-frame": function(e) {
        return e();
      }
    };
    var X, K = {}, H = d["react-stack-bottom-frame"].bind(
      d,
      a
    )(), Z = Y(m(a)), Q = {};
    S.Fragment = b, S.jsx = function(e, l, f, y, T) {
      var w = 1e4 > L.recentlyCreatedOwnerStacks++;
      return _(
        e,
        l,
        f,
        !1,
        y,
        T,
        w ? Error("react-stack-top-frame") : H,
        w ? Y(m(e)) : Z
      );
    }, S.jsxs = function(e, l, f, y, T) {
      var w = 1e4 > L.recentlyCreatedOwnerStacks++;
      return _(
        e,
        l,
        f,
        !0,
        y,
        T,
        w ? Error("react-stack-top-frame") : H,
        w ? Y(m(e)) : Z
      );
    };
  }()), S;
}
var te;
function be() {
  return te || (te = 1, process.env.NODE_ENV === "production" ? M.exports = pe() : M.exports = xe()), M.exports;
}
var s = be(), q = { exports: {} };
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/
var ne;
function ye() {
  return ne || (ne = 1, function(r) {
    (function() {
      var n = {}.hasOwnProperty;
      function c() {
        for (var a = "", o = 0; o < arguments.length; o++) {
          var u = arguments[o];
          u && (a = i(a, m(u)));
        }
        return a;
      }
      function m(a) {
        if (typeof a == "string" || typeof a == "number")
          return a;
        if (typeof a != "object")
          return "";
        if (Array.isArray(a))
          return c.apply(null, a);
        if (a.toString !== Object.prototype.toString && !a.toString.toString().includes("[native code]"))
          return a.toString();
        var o = "";
        for (var u in a)
          n.call(a, u) && a[u] && (o = i(o, u));
        return o;
      }
      function i(a, o) {
        return o ? a ? a + " " + o : a + o : a;
      }
      r.exports ? (c.default = c, r.exports = c) : window.classNames = c;
    })();
  }(q)), q.exports;
}
var je = ye();
const I = /* @__PURE__ */ _e(je), ve = {
  "Top Match": "primary",
  "Free Tier": "success",
  "AI Powered": "secondary",
  Popular: "warning",
  New: "primary",
  "Trial Available": "success"
}, ge = {
  "Top Match": "★",
  "Free Tier": "◆",
  "AI Powered": "◉",
  Popular: "▲",
  New: "●",
  "Trial Available": "◈"
}, O = ({
  type: r,
  variant: n,
  size: c = "md",
  className: m
}) => {
  const i = n || ve[r] || "secondary", a = ge[r], o = I(
    "admesh-component",
    "admesh-badge",
    `admesh-badge--${i}`,
    `admesh-badge--${c}`,
    m
  );
  return /* @__PURE__ */ s.jsxs("span", { className: o, children: [
    a && /* @__PURE__ */ s.jsx("span", { className: "admesh-badge__icon", children: a }),
    /* @__PURE__ */ s.jsx("span", { className: "admesh-badge__text", children: r })
  ] });
};
O.displayName = "AdMeshBadge";
const Ne = "https://api.useadmesh.com/track";
let W = {
  apiBaseUrl: Ne,
  enabled: !0,
  debug: !1,
  retryAttempts: 3,
  retryDelay: 1e3
};
const Ae = (r) => {
  W = { ...W, ...r };
}, ke = (r) => {
  const [n, c] = ee(!1), [m, i] = ee(null), a = { ...W, ...r }, o = A((t, d) => {
    a.debug && console.log(`[AdMesh Tracker] ${t}`, d);
  }, [a.debug]), u = A(async (t, d) => {
    if (!a.enabled) {
      o("Tracking disabled, skipping event", { eventType: t, data: d });
      return;
    }
    if (!d.adId || !d.admeshLink) {
      const v = "Missing required tracking data: adId and admeshLink are required";
      o(v, d), i(v);
      return;
    }
    c(!0), i(null);
    const x = {
      event_type: t,
      ad_id: d.adId,
      admesh_link: d.admeshLink,
      product_id: d.productId,
      user_id: d.userId,
      session_id: d.sessionId,
      revenue: d.revenue,
      conversion_type: d.conversionType,
      metadata: d.metadata,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      user_agent: navigator.userAgent,
      referrer: document.referrer,
      page_url: window.location.href
    };
    o(`Sending ${t} event`, x);
    let h = null;
    for (let v = 1; v <= (a.retryAttempts || 3); v++)
      try {
        const p = await fetch(`${a.apiBaseUrl}/events`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(x)
        });
        if (!p.ok)
          throw new Error(`HTTP ${p.status}: ${p.statusText}`);
        const k = await p.json();
        o(`${t} event tracked successfully`, k), c(!1);
        return;
      } catch (p) {
        h = p, o(`Attempt ${v} failed for ${t} event`, p), v < (a.retryAttempts || 3) && await new Promise(
          (k) => setTimeout(k, (a.retryDelay || 1e3) * v)
        );
      }
    const b = `Failed to track ${t} event after ${a.retryAttempts} attempts: ${h == null ? void 0 : h.message}`;
    o(b, h), i(b), c(!1);
  }, [a, o]), N = A(async (t) => u("click", t), [u]), g = A(async (t) => u("view", t), [u]), _ = A(async (t) => (!t.revenue && !t.conversionType && o("Warning: Conversion tracking without revenue or conversion type", t), u("conversion", t)), [u]);
  return {
    trackClick: N,
    trackView: g,
    trackConversion: _,
    isTracking: n,
    error: m
  };
}, Re = (r, n, c) => {
  try {
    const m = new URL(r);
    return m.searchParams.set("ad_id", n), m.searchParams.set("utm_source", "admesh"), m.searchParams.set("utm_medium", "recommendation"), c && Object.entries(c).forEach(([i, a]) => {
      m.searchParams.set(i, a);
    }), m.toString();
  } catch {
    return console.warn("[AdMesh] Invalid URL provided to buildAdMeshLink:", r), r;
  }
}, Se = (r, n) => ({
  adId: r.ad_id,
  admeshLink: r.admesh_link,
  productId: r.product_id,
  ...n
}), z = ({
  adId: r,
  admeshLink: n,
  productId: c,
  children: m,
  onClick: i,
  trackingData: a,
  className: o
}) => {
  const { trackClick: u, trackView: N } = ke(), g = se(null), _ = se(!1);
  fe(() => {
    if (!g.current || _.current) return;
    const d = new IntersectionObserver(
      (x) => {
        x.forEach((h) => {
          h.isIntersecting && !_.current && (_.current = !0, N({
            adId: r,
            admeshLink: n,
            productId: c,
            ...a
          }).catch(console.error));
        });
      },
      {
        threshold: 0.5,
        // Track when 50% of the element is visible
        rootMargin: "0px"
      }
    );
    return d.observe(g.current), () => {
      d.disconnect();
    };
  }, [r, n, c, a, N]);
  const t = A(async (d) => {
    try {
      await u({
        adId: r,
        admeshLink: n,
        productId: c,
        ...a
      });
    } catch (b) {
      console.error("Failed to track click:", b);
    }
    i && i(), d.target.closest("a") || window.open(n, "_blank", "noopener,noreferrer");
  }, [r, n, c, a, u, i]);
  return /* @__PURE__ */ s.jsx(
    "div",
    {
      ref: g,
      className: o,
      onClick: t,
      style: { cursor: "pointer" },
      children: m
    }
  );
};
z.displayName = "AdMeshLinkTracker";
const ce = ({
  recommendation: r,
  theme: n,
  showMatchScore: c = !0,
  showBadges: m = !0,
  maxKeywords: i = 3,
  onClick: a,
  onTrackView: o,
  className: u
}) => {
  var h, b, v;
  const N = P(() => {
    var C;
    const p = [];
    r.intent_match_score >= 0.8 && p.push("Top Match"), r.has_free_tier && p.push("Free Tier"), r.trial_days && r.trial_days > 0 && p.push("Trial Available");
    const k = ["ai", "artificial intelligence", "machine learning", "ml", "automation"];
    return (((C = r.keywords) == null ? void 0 : C.some(
      ($) => k.some((F) => $.toLowerCase().includes(F))
    )) || r.title.toLowerCase().includes("ai")) && p.push("AI Powered"), p;
  }, [r]), g = Math.round(r.intent_match_score * 100), _ = ((h = r.keywords) == null ? void 0 : h.slice(0, i)) || [], t = (((b = r.keywords) == null ? void 0 : b.length) || 0) > i, d = I(
    "admesh-component",
    "admesh-card",
    "admesh-product-card",
    {
      [`admesh-product-card--${n == null ? void 0 : n.mode}`]: n == null ? void 0 : n.mode
    },
    u
  ), x = n != null && n.accentColor ? {
    "--admesh-primary": n.accentColor,
    "--admesh-primary-hover": n.accentColor + "dd"
    // Add some transparency for hover
  } : void 0;
  return /* @__PURE__ */ s.jsx(
    z,
    {
      adId: r.ad_id,
      admeshLink: r.admesh_link,
      productId: r.product_id,
      onClick: () => a == null ? void 0 : a(r.ad_id, r.admesh_link),
      trackingData: {
        title: r.title,
        matchScore: r.intent_match_score
      },
      className: d,
      children: /* @__PURE__ */ s.jsxs(
        "div",
        {
          className: "admesh-product-card__container",
          style: x,
          "data-admesh-theme": n == null ? void 0 : n.mode,
          children: [
            /* @__PURE__ */ s.jsxs("div", { className: "admesh-product-card__header", children: [
              m && N.length > 0 && /* @__PURE__ */ s.jsx("div", { className: "admesh-product-card__badges", children: N.map((p, k) => /* @__PURE__ */ s.jsx(O, { type: p, size: "sm" }, `${p}-${k}`)) }),
              c && /* @__PURE__ */ s.jsx("div", { className: "admesh-product-card__match-score", children: /* @__PURE__ */ s.jsxs("span", { className: "admesh-text-xs admesh-text-muted", children: [
                g,
                "% match"
              ] }) })
            ] }),
            /* @__PURE__ */ s.jsxs("div", { className: "admesh-product-card__content", children: [
              /* @__PURE__ */ s.jsx("h3", { className: "admesh-product-card__title admesh-text-lg admesh-font-semibold", children: r.title }),
              /* @__PURE__ */ s.jsx("p", { className: "admesh-product-card__reason admesh-text-sm admesh-text-secondary", children: r.reason }),
              _.length > 0 && /* @__PURE__ */ s.jsxs("div", { className: "admesh-product-card__keywords", children: [
                _.map((p, k) => /* @__PURE__ */ s.jsx(
                  "span",
                  {
                    className: "admesh-product-card__keyword admesh-badge admesh-badge--secondary admesh-badge--sm",
                    children: p
                  },
                  k
                )),
                t && /* @__PURE__ */ s.jsxs("span", { className: "admesh-product-card__keyword-more admesh-text-xs admesh-text-muted", children: [
                  "+",
                  (((v = r.keywords) == null ? void 0 : v.length) || 0) - i,
                  " more"
                ] })
              ] }),
              /* @__PURE__ */ s.jsxs("div", { className: "admesh-product-card__meta", children: [
                r.pricing && /* @__PURE__ */ s.jsxs("div", { className: "admesh-product-card__pricing admesh-text-sm", children: [
                  /* @__PURE__ */ s.jsx("span", { className: "admesh-text-muted", children: "Pricing: " }),
                  /* @__PURE__ */ s.jsx("span", { className: "admesh-font-medium", children: r.pricing })
                ] }),
                r.trial_days && r.trial_days > 0 && /* @__PURE__ */ s.jsxs("div", { className: "admesh-product-card__trial admesh-text-sm admesh-text-muted", children: [
                  r.trial_days,
                  "-day free trial"
                ] })
              ] })
            ] }),
            /* @__PURE__ */ s.jsx("div", { className: "admesh-product-card__footer", children: /* @__PURE__ */ s.jsxs("button", { className: "admesh-button admesh-button--primary admesh-product-card__cta", children: [
              "Visit Offer",
              /* @__PURE__ */ s.jsxs("span", { className: "admesh-sr-only", children: [
                "for ",
                r.title
              ] })
            ] }) })
          ]
        }
      )
    }
  );
};
ce.displayName = "AdMeshProductCard";
const oe = ({
  recommendations: r,
  theme: n,
  maxProducts: c = 3,
  showMatchScores: m = !0,
  showFeatures: i = !0,
  onProductClick: a,
  className: o
}) => {
  const u = P(() => r.slice(0, c), [r, c]), N = P(() => {
    const t = /* @__PURE__ */ new Set();
    return u.forEach((d) => {
      var x;
      (x = d.features) == null || x.forEach((h) => t.add(h));
    }), Array.from(t).slice(0, 8);
  }, [u]), g = I(
    "admesh-component",
    "admesh-compare-table",
    {
      [`admesh-compare-table--${n == null ? void 0 : n.mode}`]: n == null ? void 0 : n.mode
    },
    o
  ), _ = n != null && n.accentColor ? {
    "--admesh-primary": n.accentColor
  } : void 0;
  return u.length === 0 ? /* @__PURE__ */ s.jsx("div", { className: g, children: /* @__PURE__ */ s.jsx("div", { className: "admesh-compare-table__empty", children: /* @__PURE__ */ s.jsx("p", { className: "admesh-text-muted", children: "No products to compare" }) }) }) : /* @__PURE__ */ s.jsx(
    "div",
    {
      className: g,
      style: _,
      "data-admesh-theme": n == null ? void 0 : n.mode,
      children: /* @__PURE__ */ s.jsxs("div", { className: "admesh-compare-table__container", children: [
        /* @__PURE__ */ s.jsxs("div", { className: "admesh-compare-table__header", children: [
          /* @__PURE__ */ s.jsx("h3", { className: "admesh-compare-table__title admesh-text-xl admesh-font-semibold", children: "Product Comparison" }),
          /* @__PURE__ */ s.jsxs("p", { className: "admesh-compare-table__subtitle admesh-text-sm admesh-text-muted", children: [
            "Compare ",
            u.length,
            " products side by side"
          ] })
        ] }),
        /* @__PURE__ */ s.jsx("div", { className: "admesh-compare-table__scroll-container", children: /* @__PURE__ */ s.jsxs("table", { className: "admesh-compare-table__table", children: [
          /* @__PURE__ */ s.jsx("thead", { children: /* @__PURE__ */ s.jsxs("tr", { children: [
            /* @__PURE__ */ s.jsx("th", { className: "admesh-compare-table__row-header", children: /* @__PURE__ */ s.jsx("span", { className: "admesh-sr-only", children: "Feature" }) }),
            u.map((t, d) => /* @__PURE__ */ s.jsx("th", { className: "admesh-compare-table__product-header", children: /* @__PURE__ */ s.jsxs(
              z,
              {
                adId: t.ad_id,
                admeshLink: t.admesh_link,
                productId: t.product_id,
                onClick: () => a == null ? void 0 : a(t.ad_id, t.admesh_link),
                className: "admesh-compare-table__product-header-content",
                children: [
                  /* @__PURE__ */ s.jsxs("div", { className: "admesh-compare-table__product-title", children: [
                    /* @__PURE__ */ s.jsx("h4", { className: "admesh-text-base admesh-font-semibold admesh-truncate", children: t.title }),
                    m && /* @__PURE__ */ s.jsx("div", { className: "admesh-compare-table__match-score", children: /* @__PURE__ */ s.jsxs("span", { className: "admesh-text-xs admesh-text-muted", children: [
                      Math.round(t.intent_match_score * 100),
                      "% match"
                    ] }) })
                  ] }),
                  /* @__PURE__ */ s.jsxs("div", { className: "admesh-compare-table__badges", children: [
                    t.has_free_tier && /* @__PURE__ */ s.jsx(O, { type: "Free Tier", size: "sm" }),
                    t.trial_days && t.trial_days > 0 && /* @__PURE__ */ s.jsx(O, { type: "Trial Available", size: "sm" }),
                    t.intent_match_score >= 0.8 && /* @__PURE__ */ s.jsx(O, { type: "Top Match", size: "sm" })
                  ] }),
                  /* @__PURE__ */ s.jsx("button", { className: "admesh-button admesh-button--primary admesh-button--sm admesh-compare-table__cta", children: "Visit Offer" })
                ]
              }
            ) }, t.product_id || d))
          ] }) }),
          /* @__PURE__ */ s.jsxs("tbody", { children: [
            /* @__PURE__ */ s.jsxs("tr", { children: [
              /* @__PURE__ */ s.jsx("td", { className: "admesh-compare-table__row-header admesh-font-medium", children: "Pricing" }),
              u.map((t, d) => /* @__PURE__ */ s.jsx("td", { className: "admesh-compare-table__cell", children: /* @__PURE__ */ s.jsx("span", { className: "admesh-text-sm", children: t.pricing || "Contact for pricing" }) }, t.product_id || d))
            ] }),
            /* @__PURE__ */ s.jsxs("tr", { children: [
              /* @__PURE__ */ s.jsx("td", { className: "admesh-compare-table__row-header admesh-font-medium", children: "Free Trial" }),
              u.map((t, d) => /* @__PURE__ */ s.jsx("td", { className: "admesh-compare-table__cell", children: /* @__PURE__ */ s.jsx("span", { className: "admesh-text-sm", children: t.trial_days ? `${t.trial_days} days` : "No trial" }) }, t.product_id || d))
            ] }),
            i && N.map((t, d) => /* @__PURE__ */ s.jsxs("tr", { children: [
              /* @__PURE__ */ s.jsx("td", { className: "admesh-compare-table__row-header admesh-font-medium", children: t }),
              u.map((x, h) => {
                var b;
                return /* @__PURE__ */ s.jsx("td", { className: "admesh-compare-table__cell", children: /* @__PURE__ */ s.jsx("span", { className: "admesh-text-sm", children: (b = x.features) != null && b.includes(t) ? /* @__PURE__ */ s.jsx("span", { className: "admesh-compare-table__check", children: "✓" }) : /* @__PURE__ */ s.jsx("span", { className: "admesh-compare-table__cross", children: "—" }) }) }, x.product_id || h);
              })
            ] }, d)),
            /* @__PURE__ */ s.jsxs("tr", { children: [
              /* @__PURE__ */ s.jsx("td", { className: "admesh-compare-table__row-header admesh-font-medium", children: "Keywords" }),
              u.map((t, d) => {
                var x, h, b;
                return /* @__PURE__ */ s.jsx("td", { className: "admesh-compare-table__cell", children: /* @__PURE__ */ s.jsxs("div", { className: "admesh-compare-table__keywords", children: [
                  (x = t.keywords) == null ? void 0 : x.slice(0, 3).map((v, p) => /* @__PURE__ */ s.jsx(
                    "span",
                    {
                      className: "admesh-badge admesh-badge--secondary admesh-badge--sm",
                      children: v
                    },
                    p
                  )),
                  (((h = t.keywords) == null ? void 0 : h.length) || 0) > 3 && /* @__PURE__ */ s.jsxs("span", { className: "admesh-text-xs admesh-text-muted", children: [
                    "+",
                    (((b = t.keywords) == null ? void 0 : b.length) || 0) - 3
                  ] })
                ] }) }, t.product_id || d);
              })
            ] })
          ] })
        ] }) })
      ] })
    }
  );
};
oe.displayName = "AdMeshCompareTable";
const we = (r, n, c) => {
  if (!c && n)
    switch (n) {
      case "compare_products":
        return "compare";
      case "best_for_use_case":
      case "trial_demo":
      case "budget_conscious":
        return "cards";
      default:
        return "cards";
    }
  const m = r.length;
  if (m >= 2 && m <= 4) {
    const i = r.some((o) => o.features && o.features.length > 0), a = r.some((o) => o.pricing);
    if (i || a)
      return "compare";
  }
  return "cards";
}, Te = ({
  recommendations: r,
  intentType: n,
  theme: c,
  maxDisplayed: m = 6,
  showMatchScores: i = !0,
  showFeatures: a = !0,
  autoLayout: o = !0,
  onProductClick: u,
  onTrackView: N,
  className: g
}) => {
  const _ = P(() => r.slice(0, m), [r, m]), t = P(() => we(_, n, o), [_, n, o]), d = I(
    "admesh-component",
    "admesh-layout",
    `admesh-layout--${t}`,
    {
      [`admesh-layout--${c == null ? void 0 : c.mode}`]: c == null ? void 0 : c.mode
    },
    g
  ), x = c != null && c.accentColor ? {
    "--admesh-primary": c.accentColor
  } : void 0;
  return _.length === 0 ? /* @__PURE__ */ s.jsx("div", { className: d, children: /* @__PURE__ */ s.jsx("div", { className: "admesh-layout__empty", children: /* @__PURE__ */ s.jsxs("div", { className: "admesh-layout__empty-content", children: [
    /* @__PURE__ */ s.jsx("h3", { className: "admesh-text-lg admesh-font-semibold admesh-text-muted", children: "No recommendations available" }),
    /* @__PURE__ */ s.jsx("p", { className: "admesh-text-sm admesh-text-muted", children: "Try adjusting your search criteria or check back later." })
  ] }) }) }) : /* @__PURE__ */ s.jsx(
    "div",
    {
      className: d,
      style: x,
      "data-admesh-theme": c == null ? void 0 : c.mode,
      children: t === "compare" ? /* @__PURE__ */ s.jsx(
        oe,
        {
          recommendations: _,
          theme: c,
          maxProducts: Math.min(_.length, 4),
          showMatchScores: i,
          showFeatures: a,
          onProductClick: u
        }
      ) : /* @__PURE__ */ s.jsxs("div", { className: "admesh-layout__cards-container", children: [
        /* @__PURE__ */ s.jsxs("div", { className: "admesh-layout__header", children: [
          /* @__PURE__ */ s.jsx("h3", { className: "admesh-layout__title admesh-text-xl admesh-font-semibold", children: "Recommended Products" }),
          /* @__PURE__ */ s.jsxs("p", { className: "admesh-layout__subtitle admesh-text-sm admesh-text-muted", children: [
            _.length,
            " product",
            _.length !== 1 ? "s" : "",
            " found"
          ] })
        ] }),
        /* @__PURE__ */ s.jsx("div", { className: "admesh-layout__cards-grid", children: _.map((h, b) => /* @__PURE__ */ s.jsx(
          ce,
          {
            recommendation: h,
            theme: c,
            showMatchScore: i,
            showBadges: !0,
            maxKeywords: 3,
            onClick: u,
            onTrackView: N
          },
          h.product_id || h.ad_id || b
        )) }),
        r.length > m && /* @__PURE__ */ s.jsx("div", { className: "admesh-layout__more-indicator", children: /* @__PURE__ */ s.jsxs("p", { className: "admesh-text-sm admesh-text-muted", children: [
          "Showing ",
          m,
          " of ",
          r.length,
          " recommendations"
        ] }) })
      ] })
    }
  );
};
Te.displayName = "AdMeshLayout";
const Oe = "0.1.0", Pe = {
  trackingEnabled: !0,
  debug: !1,
  theme: {
    mode: "light",
    accentColor: "#2563eb"
  }
};
export {
  O as AdMeshBadge,
  oe as AdMeshCompareTable,
  Te as AdMeshLayout,
  z as AdMeshLinkTracker,
  ce as AdMeshProductCard,
  Pe as DEFAULT_CONFIG,
  Oe as VERSION,
  Re as buildAdMeshLink,
  Se as extractTrackingData,
  Ae as setAdMeshTrackerConfig,
  ke as useAdMeshTracker
};
//# sourceMappingURL=index.mjs.map

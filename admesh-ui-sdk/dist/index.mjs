import he, { useState as ee, use<PERSON><PERSON>back as E, use<PERSON><PERSON> as re, useEffect as fe, use<PERSON>emo as C } from "react";
function pe(s) {
  return s && s.__esModule && Object.prototype.hasOwnProperty.call(s, "default") ? s.default : s;
}
var M = { exports: {} }, R = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var se;
function xe() {
  if (se) return R;
  se = 1;
  var s = Symbol.for("react.transitional.element"), n = Symbol.for("react.fragment");
  function l(m, d, a) {
    var o = null;
    if (a !== void 0 && (o = "" + a), d.key !== void 0 && (o = "" + d.key), "key" in d) {
      a = {};
      for (var u in d)
        u !== "key" && (a[u] = d[u]);
    } else a = d;
    return d = a.ref, {
      $$typeof: s,
      type: m,
      key: o,
      ref: d !== void 0 ? d : null,
      props: a
    };
  }
  return R.Fragment = n, R.jsx = l, R.jsxs = l, R;
}
var S = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ae;
function ge() {
  return ae || (ae = 1, process.env.NODE_ENV !== "production" && function() {
    function s(r) {
      if (r == null) return null;
      if (typeof r == "function")
        return r.$$typeof === de ? null : r.displayName || r.name || null;
      if (typeof r == "string") return r;
      switch (r) {
        case b:
          return "Fragment";
        case x:
          return "Profiler";
        case v:
          return "StrictMode";
        case $:
          return "Suspense";
        case F:
          return "SuspenseList";
        case ce:
          return "Activity";
      }
      if (typeof r == "object")
        switch (typeof r.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), r.$$typeof) {
          case h:
            return "Portal";
          case W:
            return (r.displayName || "Context") + ".Provider";
          case w:
            return (r._context.displayName || "Context") + ".Consumer";
          case O:
            var c = r.render;
            return r = r.displayName, r || (r = c.displayName || c.name || "", r = r !== "" ? "ForwardRef(" + r + ")" : "ForwardRef"), r;
          case ie:
            return c = r.displayName || null, c !== null ? c : s(r.type) || "Memo";
          case J:
            c = r._payload, r = r._init;
            try {
              return s(r(c));
            } catch {
            }
        }
      return null;
    }
    function n(r) {
      return "" + r;
    }
    function l(r) {
      try {
        n(r);
        var c = !1;
      } catch {
        c = !0;
      }
      if (c) {
        c = console;
        var f = c.error, _ = typeof Symbol == "function" && Symbol.toStringTag && r[Symbol.toStringTag] || r.constructor.name || "Object";
        return f.call(
          c,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          _
        ), n(r);
      }
    }
    function m(r) {
      if (r === b) return "<>";
      if (typeof r == "object" && r !== null && r.$$typeof === J)
        return "<...>";
      try {
        var c = s(r);
        return c ? "<" + c + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function d() {
      var r = L.A;
      return r === null ? null : r.getOwner();
    }
    function a() {
      return Error("react-stack-top-frame");
    }
    function o(r) {
      if (G.call(r, "key")) {
        var c = Object.getOwnPropertyDescriptor(r, "key").get;
        if (c && c.isReactWarning) return !1;
      }
      return r.key !== void 0;
    }
    function u(r, c) {
      function f() {
        H || (H = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          c
        ));
      }
      f.isReactWarning = !0, Object.defineProperty(r, "key", {
        get: f,
        configurable: !0
      });
    }
    function N() {
      var r = s(this.type);
      return X[r] || (X[r] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), r = this.props.ref, r !== void 0 ? r : null;
    }
    function j(r, c, f, _, T, k, z, U) {
      return f = k.ref, r = {
        $$typeof: g,
        type: r,
        key: c,
        props: k,
        _owner: T
      }, (f !== void 0 ? f : null) !== null ? Object.defineProperty(r, "ref", {
        enumerable: !1,
        get: N
      }) : Object.defineProperty(r, "ref", { enumerable: !1, value: null }), r._store = {}, Object.defineProperty(r._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(r, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(r, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: z
      }), Object.defineProperty(r, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: U
      }), Object.freeze && (Object.freeze(r.props), Object.freeze(r)), r;
    }
    function p(r, c, f, _, T, k, z, U) {
      var y = c.children;
      if (y !== void 0)
        if (_)
          if (ue(y)) {
            for (_ = 0; _ < y.length; _++)
              t(y[_]);
            Object.freeze && Object.freeze(y);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else t(y);
      if (G.call(c, "key")) {
        y = s(r);
        var A = Object.keys(c).filter(function(me) {
          return me !== "key";
        });
        _ = 0 < A.length ? "{key: someKey, " + A.join(": ..., ") + ": ...}" : "{key: someKey}", Q[y + _] || (A = 0 < A.length ? "{" + A.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          _,
          y,
          A,
          y
        ), Q[y + _] = !0);
      }
      if (y = null, f !== void 0 && (l(f), y = "" + f), o(c) && (l(c.key), y = "" + c.key), "key" in c) {
        f = {};
        for (var V in c)
          V !== "key" && (f[V] = c[V]);
      } else f = c;
      return y && u(
        f,
        typeof r == "function" ? r.displayName || r.name || "Unknown" : r
      ), j(
        r,
        y,
        k,
        T,
        d(),
        f,
        z,
        U
      );
    }
    function t(r) {
      typeof r == "object" && r !== null && r.$$typeof === g && r._store && (r._store.validated = 1);
    }
    var i = he, g = Symbol.for("react.transitional.element"), h = Symbol.for("react.portal"), b = Symbol.for("react.fragment"), v = Symbol.for("react.strict_mode"), x = Symbol.for("react.profiler"), w = Symbol.for("react.consumer"), W = Symbol.for("react.context"), O = Symbol.for("react.forward_ref"), $ = Symbol.for("react.suspense"), F = Symbol.for("react.suspense_list"), ie = Symbol.for("react.memo"), J = Symbol.for("react.lazy"), ce = Symbol.for("react.activity"), de = Symbol.for("react.client.reference"), L = i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, G = Object.prototype.hasOwnProperty, ue = Array.isArray, Y = console.createTask ? console.createTask : function() {
      return null;
    };
    i = {
      "react-stack-bottom-frame": function(r) {
        return r();
      }
    };
    var H, X = {}, K = i["react-stack-bottom-frame"].bind(
      i,
      a
    )(), Z = Y(m(a)), Q = {};
    S.Fragment = b, S.jsx = function(r, c, f, _, T) {
      var k = 1e4 > L.recentlyCreatedOwnerStacks++;
      return p(
        r,
        c,
        f,
        !1,
        _,
        T,
        k ? Error("react-stack-top-frame") : K,
        k ? Y(m(r)) : Z
      );
    }, S.jsxs = function(r, c, f, _, T) {
      var k = 1e4 > L.recentlyCreatedOwnerStacks++;
      return p(
        r,
        c,
        f,
        !0,
        _,
        T,
        k ? Error("react-stack-top-frame") : K,
        k ? Y(m(r)) : Z
      );
    };
  }()), S;
}
var te;
function be() {
  return te || (te = 1, process.env.NODE_ENV === "production" ? M.exports = xe() : M.exports = ge()), M.exports;
}
var e = be(), D = { exports: {} };
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/
var ne;
function _e() {
  return ne || (ne = 1, function(s) {
    (function() {
      var n = {}.hasOwnProperty;
      function l() {
        for (var a = "", o = 0; o < arguments.length; o++) {
          var u = arguments[o];
          u && (a = d(a, m(u)));
        }
        return a;
      }
      function m(a) {
        if (typeof a == "string" || typeof a == "number")
          return a;
        if (typeof a != "object")
          return "";
        if (Array.isArray(a))
          return l.apply(null, a);
        if (a.toString !== Object.prototype.toString && !a.toString.toString().includes("[native code]"))
          return a.toString();
        var o = "";
        for (var u in a)
          n.call(a, u) && a[u] && (o = d(o, u));
        return o;
      }
      function d(a, o) {
        return o ? a ? a + " " + o : a + o : a;
      }
      s.exports ? (l.default = l, s.exports = l) : window.classNames = l;
    })();
  }(D)), D.exports;
}
var ye = _e();
const I = /* @__PURE__ */ pe(ye), ve = {
  "Top Match": "primary",
  "Free Tier": "success",
  "AI Powered": "secondary",
  Popular: "warning",
  New: "primary",
  "Trial Available": "success"
}, je = {
  "Top Match": "★",
  "Free Tier": "◆",
  "AI Powered": "◉",
  Popular: "▲",
  New: "●",
  "Trial Available": "◈"
}, P = ({
  type: s,
  variant: n,
  size: l = "md",
  className: m
}) => {
  const d = n || ve[s] || "secondary", a = je[s], o = I(
    "admesh-component",
    "admesh-badge",
    `admesh-badge--${d}`,
    `admesh-badge--${l}`,
    m
  );
  return /* @__PURE__ */ e.jsxs("span", { className: o, children: [
    a && /* @__PURE__ */ e.jsx("span", { className: "admesh-badge__icon", children: a }),
    /* @__PURE__ */ e.jsx("span", { className: "admesh-badge__text", children: s })
  ] });
};
P.displayName = "AdMeshBadge";
const Ne = "https://api.useadmesh.com/track";
let q = {
  apiBaseUrl: Ne,
  enabled: !0,
  debug: !1,
  retryAttempts: 3,
  retryDelay: 1e3
};
const Ee = (s) => {
  q = { ...q, ...s };
}, we = (s) => {
  const [n, l] = ee(!1), [m, d] = ee(null), a = { ...q, ...s }, o = E((t, i) => {
    a.debug && console.log(`[AdMesh Tracker] ${t}`, i);
  }, [a.debug]), u = E(async (t, i) => {
    if (!a.enabled) {
      o("Tracking disabled, skipping event", { eventType: t, data: i });
      return;
    }
    if (!i.adId || !i.admeshLink) {
      const v = "Missing required tracking data: adId and admeshLink are required";
      o(v, i), d(v);
      return;
    }
    l(!0), d(null);
    const g = {
      event_type: t,
      ad_id: i.adId,
      admesh_link: i.admeshLink,
      product_id: i.productId,
      user_id: i.userId,
      session_id: i.sessionId,
      revenue: i.revenue,
      conversion_type: i.conversionType,
      metadata: i.metadata,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      user_agent: navigator.userAgent,
      referrer: document.referrer,
      page_url: window.location.href
    };
    o(`Sending ${t} event`, g);
    let h = null;
    for (let v = 1; v <= (a.retryAttempts || 3); v++)
      try {
        const x = await fetch(`${a.apiBaseUrl}/events`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(g)
        });
        if (!x.ok)
          throw new Error(`HTTP ${x.status}: ${x.statusText}`);
        const w = await x.json();
        o(`${t} event tracked successfully`, w), l(!1);
        return;
      } catch (x) {
        h = x, o(`Attempt ${v} failed for ${t} event`, x), v < (a.retryAttempts || 3) && await new Promise(
          (w) => setTimeout(w, (a.retryDelay || 1e3) * v)
        );
      }
    const b = `Failed to track ${t} event after ${a.retryAttempts} attempts: ${h == null ? void 0 : h.message}`;
    o(b, h), d(b), l(!1);
  }, [a, o]), N = E(async (t) => u("click", t), [u]), j = E(async (t) => u("view", t), [u]), p = E(async (t) => (!t.revenue && !t.conversionType && o("Warning: Conversion tracking without revenue or conversion type", t), u("conversion", t)), [u]);
  return {
    trackClick: N,
    trackView: j,
    trackConversion: p,
    isTracking: n,
    error: m
  };
}, Re = (s, n, l) => {
  try {
    const m = new URL(s);
    return m.searchParams.set("ad_id", n), m.searchParams.set("utm_source", "admesh"), m.searchParams.set("utm_medium", "recommendation"), l && Object.entries(l).forEach(([d, a]) => {
      m.searchParams.set(d, a);
    }), m.toString();
  } catch {
    return console.warn("[AdMesh] Invalid URL provided to buildAdMeshLink:", s), s;
  }
}, Se = (s, n) => ({
  adId: s.ad_id,
  admeshLink: s.admesh_link,
  productId: s.product_id,
  ...n
}), B = ({
  adId: s,
  admeshLink: n,
  productId: l,
  children: m,
  onClick: d,
  trackingData: a,
  className: o
}) => {
  const { trackClick: u, trackView: N } = we(), j = re(null), p = re(!1);
  fe(() => {
    if (!j.current || p.current) return;
    const i = new IntersectionObserver(
      (g) => {
        g.forEach((h) => {
          h.isIntersecting && !p.current && (p.current = !0, N({
            adId: s,
            admeshLink: n,
            productId: l,
            ...a
          }).catch(console.error));
        });
      },
      {
        threshold: 0.5,
        // Track when 50% of the element is visible
        rootMargin: "0px"
      }
    );
    return i.observe(j.current), () => {
      i.disconnect();
    };
  }, [s, n, l, a, N]);
  const t = E(async (i) => {
    try {
      await u({
        adId: s,
        admeshLink: n,
        productId: l,
        ...a
      });
    } catch (b) {
      console.error("Failed to track click:", b);
    }
    d && d(), i.target.closest("a") || window.open(n, "_blank", "noopener,noreferrer");
  }, [s, n, l, a, u, d]);
  return /* @__PURE__ */ e.jsx(
    "div",
    {
      ref: j,
      className: o,
      onClick: t,
      style: { cursor: "pointer" },
      children: m
    }
  );
};
B.displayName = "AdMeshLinkTracker";
const le = ({
  recommendation: s,
  theme: n,
  showMatchScore: l = !0,
  showBadges: m = !0,
  maxKeywords: d = 3,
  onClick: a,
  onTrackView: o,
  className: u
}) => {
  var h, b, v;
  const N = C(() => {
    var O;
    const x = [];
    s.intent_match_score >= 0.8 && x.push("Top Match"), s.has_free_tier && x.push("Free Tier"), s.trial_days && s.trial_days > 0 && x.push("Trial Available");
    const w = ["ai", "artificial intelligence", "machine learning", "ml", "automation"];
    return (((O = s.keywords) == null ? void 0 : O.some(
      ($) => w.some((F) => $.toLowerCase().includes(F))
    )) || s.title.toLowerCase().includes("ai")) && x.push("AI Powered"), x;
  }, [s]), j = Math.round(s.intent_match_score * 100), p = ((h = s.keywords) == null ? void 0 : h.slice(0, d)) || [], t = (((b = s.keywords) == null ? void 0 : b.length) || 0) > d, i = I(
    "admesh-component",
    "admesh-card",
    "group relative cursor-pointer transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",
    "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm",
    "overflow-hidden",
    u
  ), g = n != null && n.accentColor ? {
    "--admesh-primary": n.accentColor,
    "--admesh-primary-hover": n.accentColor + "dd"
    // Add some transparency for hover
  } : void 0;
  return /* @__PURE__ */ e.jsxs(
    B,
    {
      adId: s.ad_id,
      admeshLink: s.admesh_link,
      productId: s.product_id,
      onClick: () => a == null ? void 0 : a(s.ad_id, s.admesh_link),
      trackingData: {
        title: s.title,
        matchScore: s.intent_match_score
      },
      className: i,
      children: [
        /* @__PURE__ */ e.jsx("div", { className: "absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" }),
        /* @__PURE__ */ e.jsxs(
          "div",
          {
            className: "relative p-8 h-full flex flex-col gap-6 z-10",
            style: g,
            "data-admesh-theme": n == null ? void 0 : n.mode,
            children: [
              /* @__PURE__ */ e.jsxs("div", { className: "flex justify-between items-start gap-4 mb-2", children: [
                m && N.length > 0 && /* @__PURE__ */ e.jsx("div", { className: "flex flex-wrap gap-3 flex-1", children: N.map((x, w) => /* @__PURE__ */ e.jsx(P, { type: x, size: "sm" }, `${x}-${w}`)) }),
                l && /* @__PURE__ */ e.jsxs("div", { className: "flex-shrink-0 inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-lg text-sm font-semibold shadow-lg relative overflow-hidden", children: [
                  /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-white rounded-full shadow-sm animate-pulse" }),
                  /* @__PURE__ */ e.jsxs("span", { children: [
                    j,
                    "% match"
                  ] }),
                  /* @__PURE__ */ e.jsx("div", { className: "absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700" })
                ] })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "flex-1 flex flex-col gap-5", children: [
                /* @__PURE__ */ e.jsx("h3", { className: "text-2xl font-bold leading-tight text-gray-900 dark:text-white bg-gradient-to-r from-gray-900 to-indigo-600 dark:from-white dark:to-indigo-400 bg-clip-text text-transparent group-hover:translate-x-1 transition-transform duration-300", children: s.title }),
                /* @__PURE__ */ e.jsx("p", { className: "text-base leading-relaxed text-gray-600 dark:text-gray-300 font-normal", children: s.reason }),
                p.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-2", children: [
                  p.map((x, w) => /* @__PURE__ */ e.jsx(
                    "span",
                    {
                      className: "admesh-badge admesh-badge--secondary admesh-badge--sm",
                      children: x
                    },
                    w
                  )),
                  t && /* @__PURE__ */ e.jsxs("span", { className: "text-xs text-gray-500 dark:text-gray-400 italic", children: [
                    "+",
                    (((v = s.keywords) == null ? void 0 : v.length) || 0) - d,
                    " more"
                  ] })
                ] }),
                /* @__PURE__ */ e.jsxs("div", { className: "space-y-2", children: [
                  s.pricing && /* @__PURE__ */ e.jsxs("div", { className: "text-sm", children: [
                    /* @__PURE__ */ e.jsx("span", { className: "text-gray-500 dark:text-gray-400", children: "Pricing: " }),
                    /* @__PURE__ */ e.jsx("span", { className: "font-medium text-gray-900 dark:text-white", children: s.pricing })
                  ] }),
                  s.trial_days && s.trial_days > 0 && /* @__PURE__ */ e.jsxs("div", { className: "text-sm text-gray-500 dark:text-gray-400", children: [
                    s.trial_days,
                    "-day free trial"
                  ] })
                ] })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "mt-auto pt-6 border-t border-gray-200/50 dark:border-gray-700/50", children: [
                /* @__PURE__ */ e.jsxs("button", { className: "admesh-button admesh-button--primary w-full relative overflow-hidden bg-gradient-to-r from-indigo-500 to-indigo-600 text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300", children: [
                  /* @__PURE__ */ e.jsx("span", { className: "relative z-10", children: "Visit Offer" }),
                  /* @__PURE__ */ e.jsx("div", { className: "absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700" }),
                  /* @__PURE__ */ e.jsxs("span", { className: "sr-only", children: [
                    "for ",
                    s.title
                  ] })
                ] }),
                /* @__PURE__ */ e.jsx("div", { className: "flex items-center justify-center mt-4 text-xs text-gray-400 dark:text-gray-500", children: /* @__PURE__ */ e.jsxs("span", { className: "flex items-center gap-1.5", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "w-3 h-3 text-indigo-500", fill: "currentColor", viewBox: "0 0 20 20", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("span", { className: "font-medium", children: "Powered by" }),
                  /* @__PURE__ */ e.jsx("span", { className: "font-semibold text-indigo-600 dark:text-indigo-400", children: "AdMesh" })
                ] }) })
              ] })
            ]
          }
        )
      ]
    }
  );
};
le.displayName = "AdMeshProductCard";
const oe = ({
  recommendations: s,
  theme: n,
  maxProducts: l = 3,
  showMatchScores: m = !0,
  showFeatures: d = !0,
  onProductClick: a,
  className: o
}) => {
  const u = C(() => s.slice(0, l), [s, l]), N = C(() => {
    const t = /* @__PURE__ */ new Set();
    return u.forEach((i) => {
      var g;
      (g = i.features) == null || g.forEach((h) => t.add(h));
    }), Array.from(t).slice(0, 8);
  }, [u]), j = I(
    "admesh-component",
    "admesh-compare-table",
    {
      [`admesh-compare-table--${n == null ? void 0 : n.mode}`]: n == null ? void 0 : n.mode
    },
    o
  ), p = n != null && n.accentColor ? {
    "--admesh-primary": n.accentColor
  } : void 0;
  return u.length === 0 ? /* @__PURE__ */ e.jsx("div", { className: j, children: /* @__PURE__ */ e.jsx("div", { className: "admesh-compare-table__empty", children: /* @__PURE__ */ e.jsx("p", { className: "admesh-text-muted", children: "No products to compare" }) }) }) : /* @__PURE__ */ e.jsx(
    "div",
    {
      className: j,
      style: p,
      "data-admesh-theme": n == null ? void 0 : n.mode,
      children: /* @__PURE__ */ e.jsxs("div", { className: "admesh-compare-table__container", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "admesh-compare-table__header", children: [
          /* @__PURE__ */ e.jsx("h3", { className: "admesh-compare-table__title admesh-text-xl admesh-font-semibold", children: "Product Comparison" }),
          /* @__PURE__ */ e.jsxs("p", { className: "admesh-compare-table__subtitle admesh-text-sm admesh-text-muted", children: [
            "Compare ",
            u.length,
            " products side by side"
          ] })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "admesh-compare-table__scroll-container", children: /* @__PURE__ */ e.jsxs("table", { className: "admesh-compare-table__table", children: [
          /* @__PURE__ */ e.jsx("thead", { children: /* @__PURE__ */ e.jsxs("tr", { children: [
            /* @__PURE__ */ e.jsx("th", { className: "admesh-compare-table__row-header", children: /* @__PURE__ */ e.jsx("span", { className: "admesh-sr-only", children: "Feature" }) }),
            u.map((t, i) => /* @__PURE__ */ e.jsx("th", { className: "admesh-compare-table__product-header", children: /* @__PURE__ */ e.jsxs(
              B,
              {
                adId: t.ad_id,
                admeshLink: t.admesh_link,
                productId: t.product_id,
                onClick: () => a == null ? void 0 : a(t.ad_id, t.admesh_link),
                className: "admesh-compare-table__product-header-content",
                children: [
                  /* @__PURE__ */ e.jsxs("div", { className: "admesh-compare-table__product-title", children: [
                    /* @__PURE__ */ e.jsx("h4", { className: "admesh-text-base admesh-font-semibold admesh-truncate", children: t.title }),
                    m && /* @__PURE__ */ e.jsx("div", { className: "admesh-compare-table__match-score", children: /* @__PURE__ */ e.jsxs("span", { className: "admesh-text-xs admesh-text-muted", children: [
                      Math.round(t.intent_match_score * 100),
                      "% match"
                    ] }) })
                  ] }),
                  /* @__PURE__ */ e.jsxs("div", { className: "admesh-compare-table__badges", children: [
                    t.has_free_tier && /* @__PURE__ */ e.jsx(P, { type: "Free Tier", size: "sm" }),
                    t.trial_days && t.trial_days > 0 && /* @__PURE__ */ e.jsx(P, { type: "Trial Available", size: "sm" }),
                    t.intent_match_score >= 0.8 && /* @__PURE__ */ e.jsx(P, { type: "Top Match", size: "sm" })
                  ] }),
                  /* @__PURE__ */ e.jsx("button", { className: "admesh-button admesh-button--primary admesh-button--sm admesh-compare-table__cta", children: "Visit Offer" })
                ]
              }
            ) }, t.product_id || i))
          ] }) }),
          /* @__PURE__ */ e.jsxs("tbody", { children: [
            /* @__PURE__ */ e.jsxs("tr", { children: [
              /* @__PURE__ */ e.jsx("td", { className: "admesh-compare-table__row-header admesh-font-medium", children: "Pricing" }),
              u.map((t, i) => /* @__PURE__ */ e.jsx("td", { className: "admesh-compare-table__cell", children: /* @__PURE__ */ e.jsx("span", { className: "admesh-text-sm", children: t.pricing || "Contact for pricing" }) }, t.product_id || i))
            ] }),
            /* @__PURE__ */ e.jsxs("tr", { children: [
              /* @__PURE__ */ e.jsx("td", { className: "admesh-compare-table__row-header admesh-font-medium", children: "Free Trial" }),
              u.map((t, i) => /* @__PURE__ */ e.jsx("td", { className: "admesh-compare-table__cell", children: /* @__PURE__ */ e.jsx("span", { className: "admesh-text-sm", children: t.trial_days ? `${t.trial_days} days` : "No trial" }) }, t.product_id || i))
            ] }),
            d && N.map((t, i) => /* @__PURE__ */ e.jsxs("tr", { children: [
              /* @__PURE__ */ e.jsx("td", { className: "admesh-compare-table__row-header admesh-font-medium", children: t }),
              u.map((g, h) => {
                var b;
                return /* @__PURE__ */ e.jsx("td", { className: "admesh-compare-table__cell", children: /* @__PURE__ */ e.jsx("span", { className: "admesh-text-sm", children: (b = g.features) != null && b.includes(t) ? /* @__PURE__ */ e.jsx("span", { className: "admesh-compare-table__check", children: "✓" }) : /* @__PURE__ */ e.jsx("span", { className: "admesh-compare-table__cross", children: "—" }) }) }, g.product_id || h);
              })
            ] }, i)),
            /* @__PURE__ */ e.jsxs("tr", { children: [
              /* @__PURE__ */ e.jsx("td", { className: "admesh-compare-table__row-header admesh-font-medium", children: "Keywords" }),
              u.map((t, i) => {
                var g, h, b;
                return /* @__PURE__ */ e.jsx("td", { className: "admesh-compare-table__cell", children: /* @__PURE__ */ e.jsxs("div", { className: "admesh-compare-table__keywords", children: [
                  (g = t.keywords) == null ? void 0 : g.slice(0, 3).map((v, x) => /* @__PURE__ */ e.jsx(
                    "span",
                    {
                      className: "admesh-badge admesh-badge--secondary admesh-badge--sm",
                      children: v
                    },
                    x
                  )),
                  (((h = t.keywords) == null ? void 0 : h.length) || 0) > 3 && /* @__PURE__ */ e.jsxs("span", { className: "admesh-text-xs admesh-text-muted", children: [
                    "+",
                    (((b = t.keywords) == null ? void 0 : b.length) || 0) - 3
                  ] })
                ] }) }, t.product_id || i);
              })
            ] })
          ] })
        ] }) }),
        /* @__PURE__ */ e.jsx("div", { className: "flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50", children: /* @__PURE__ */ e.jsxs("span", { className: "flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500", children: [
          /* @__PURE__ */ e.jsx("svg", { className: "w-3 h-3 text-indigo-500", fill: "currentColor", viewBox: "0 0 20 20", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z", clipRule: "evenodd" }) }),
          /* @__PURE__ */ e.jsx("span", { className: "font-medium", children: "Powered by" }),
          /* @__PURE__ */ e.jsx("span", { className: "font-semibold text-indigo-600 dark:text-indigo-400", children: "AdMesh" })
        ] }) })
      ] })
    }
  );
};
oe.displayName = "AdMeshCompareTable";
const ke = (s, n, l) => {
  if (!l && n)
    switch (n) {
      case "compare_products":
        return "compare";
      case "best_for_use_case":
      case "trial_demo":
      case "budget_conscious":
        return "cards";
      default:
        return "cards";
    }
  const m = s.length;
  if (m >= 2 && m <= 4) {
    const d = s.some((o) => o.features && o.features.length > 0), a = s.some((o) => o.pricing);
    if (d || a)
      return "compare";
  }
  return "cards";
}, Te = ({
  recommendations: s,
  intentType: n,
  theme: l,
  maxDisplayed: m = 6,
  showMatchScores: d = !0,
  showFeatures: a = !0,
  autoLayout: o = !0,
  onProductClick: u,
  onTrackView: N,
  className: j
}) => {
  const p = C(() => s.slice(0, m), [s, m]), t = C(() => ke(p, n, o), [p, n, o]), i = I(
    "admesh-component",
    "admesh-layout",
    `admesh-layout--${t}`,
    {
      [`admesh-layout--${l == null ? void 0 : l.mode}`]: l == null ? void 0 : l.mode
    },
    j
  ), g = l != null && l.accentColor ? {
    "--admesh-primary": l.accentColor
  } : void 0;
  return p.length === 0 ? /* @__PURE__ */ e.jsx("div", { className: i, children: /* @__PURE__ */ e.jsx("div", { className: "admesh-layout__empty", children: /* @__PURE__ */ e.jsxs("div", { className: "admesh-layout__empty-content", children: [
    /* @__PURE__ */ e.jsx("h3", { className: "admesh-text-lg admesh-font-semibold admesh-text-muted", children: "No recommendations available" }),
    /* @__PURE__ */ e.jsx("p", { className: "admesh-text-sm admesh-text-muted", children: "Try adjusting your search criteria or check back later." }),
    /* @__PURE__ */ e.jsx("div", { className: "flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50", children: /* @__PURE__ */ e.jsxs("span", { className: "flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500", children: [
      /* @__PURE__ */ e.jsx("svg", { className: "w-3 h-3 text-indigo-500", fill: "currentColor", viewBox: "0 0 20 20", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z", clipRule: "evenodd" }) }),
      /* @__PURE__ */ e.jsx("span", { className: "font-medium", children: "Powered by" }),
      /* @__PURE__ */ e.jsx("span", { className: "font-semibold text-indigo-600 dark:text-indigo-400", children: "AdMesh" })
    ] }) })
  ] }) }) }) : /* @__PURE__ */ e.jsx(
    "div",
    {
      className: i,
      style: g,
      "data-admesh-theme": l == null ? void 0 : l.mode,
      children: t === "compare" ? /* @__PURE__ */ e.jsx(
        oe,
        {
          recommendations: p,
          theme: l,
          maxProducts: Math.min(p.length, 4),
          showMatchScores: d,
          showFeatures: a,
          onProductClick: u
        }
      ) : /* @__PURE__ */ e.jsxs("div", { className: "admesh-layout__cards-container", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "admesh-layout__header", children: [
          /* @__PURE__ */ e.jsx("h3", { className: "admesh-layout__title admesh-text-xl admesh-font-semibold", children: "Recommended Products" }),
          /* @__PURE__ */ e.jsxs("p", { className: "admesh-layout__subtitle admesh-text-sm admesh-text-muted", children: [
            p.length,
            " product",
            p.length !== 1 ? "s" : "",
            " found"
          ] })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "admesh-layout__cards-grid", children: p.map((h, b) => /* @__PURE__ */ e.jsx(
          le,
          {
            recommendation: h,
            theme: l,
            showMatchScore: d,
            showBadges: !0,
            maxKeywords: 3,
            onClick: u,
            onTrackView: N
          },
          h.product_id || h.ad_id || b
        )) }),
        s.length > m && /* @__PURE__ */ e.jsx("div", { className: "admesh-layout__more-indicator", children: /* @__PURE__ */ e.jsxs("p", { className: "admesh-text-sm admesh-text-muted", children: [
          "Showing ",
          m,
          " of ",
          s.length,
          " recommendations"
        ] }) }),
        /* @__PURE__ */ e.jsx("div", { className: "flex items-center justify-center mt-8 pt-6 border-t border-gray-200/50 dark:border-gray-700/50", children: /* @__PURE__ */ e.jsxs("span", { className: "flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500", children: [
          /* @__PURE__ */ e.jsx("svg", { className: "w-3 h-3 text-indigo-500", fill: "currentColor", viewBox: "0 0 20 20", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z", clipRule: "evenodd" }) }),
          /* @__PURE__ */ e.jsx("span", { className: "font-medium", children: "Powered by" }),
          /* @__PURE__ */ e.jsx("span", { className: "font-semibold text-indigo-600 dark:text-indigo-400", children: "AdMesh" })
        ] }) })
      ] })
    }
  );
};
Te.displayName = "AdMeshLayout";
const Pe = "0.1.0", Ce = {
  trackingEnabled: !0,
  debug: !1,
  theme: {
    mode: "light",
    accentColor: "#2563eb"
  }
};
export {
  P as AdMeshBadge,
  oe as AdMeshCompareTable,
  Te as AdMeshLayout,
  B as AdMeshLinkTracker,
  le as AdMeshProductCard,
  Ce as DEFAULT_CONFIG,
  Pe as VERSION,
  Re as buildAdMeshLink,
  Se as extractTrackingData,
  Ee as setAdMeshTrackerConfig,
  we as useAdMeshTracker
};
//# sourceMappingURL=index.mjs.map

{"version": 3, "file": "index.js", "sources": ["../node_modules/react/cjs/react-jsx-runtime.production.js", "../node_modules/react/cjs/react-jsx-runtime.development.js", "../node_modules/react/jsx-runtime.js", "../node_modules/classnames/index.js", "../src/components/AdMeshBadge.tsx", "../src/hooks/useAdMeshTracker.ts", "../src/components/AdMeshLinkTracker.tsx", "../src/components/AdMeshProductCard.tsx", "../src/components/AdMeshCompareTable.tsx", "../src/components/AdMeshLayout.tsx", "../src/index.ts"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config)\n      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;\n", "/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsx = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !1,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n    exports.jsxs = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !0,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshBadgeProps, BadgeType } from '../types/index';\n\n// Badge type to variant mapping\nconst badgeTypeVariants: Record<BadgeType, string> = {\n  'Top Match': 'primary',\n  'Free Tier': 'success',\n  'AI Powered': 'secondary',\n  'Popular': 'warning',\n  'New': 'primary',\n  'Trial Available': 'success'\n};\n\n// Badge type to icon mapping (using modern Unicode icons)\nconst badgeTypeIcons: Partial<Record<BadgeType, string>> = {\n  'Top Match': '★',\n  'Free Tier': '◆',\n  'AI Powered': '◉',\n  'Popular': '▲',\n  'New': '●',\n  'Trial Available': '◈'\n};\n\nexport const AdMeshBadge: React.FC<AdMeshBadgeProps> = ({\n  type,\n  variant,\n  size = 'md',\n  className\n}) => {\n  const effectiveVariant = variant || badgeTypeVariants[type] || 'secondary';\n  const icon = badgeTypeIcons[type];\n\n  const badgeClasses = classNames(\n    'admesh-component',\n    'admesh-badge',\n    `admesh-badge--${effectiveVariant}`,\n    `admesh-badge--${size}`,\n    className\n  );\n\n  return (\n    <span className={badgeClasses}>\n      {icon && <span className=\"admesh-badge__icon\">{icon}</span>}\n      <span className=\"admesh-badge__text\">{type}</span>\n    </span>\n  );\n};\n\nAdMeshBadge.displayName = 'AdMeshBadge';\n", "import { useState, useCallback } from 'react';\nimport type { TrackingData, UseAdMeshTrackerReturn } from '../types/index';\n\n// Default tracking endpoint - can be overridden via config\nconst DEFAULT_TRACKING_URL = 'https://api.useadmesh.com/track';\n\ninterface TrackingConfig {\n  apiBaseUrl?: string;\n  enabled?: boolean;\n  debug?: boolean;\n  retryAttempts?: number;\n  retryDelay?: number;\n}\n\n// Global config that can be set by the consuming application\nlet globalConfig: TrackingConfig = {\n  apiBaseUrl: DEFAULT_TRACKING_URL,\n  enabled: true,\n  debug: false,\n  retryAttempts: 3,\n  retryDelay: 1000\n};\n\nexport const setAdMeshTrackerConfig = (config: Partial<TrackingConfig>) => {\n  globalConfig = { ...globalConfig, ...config };\n};\n\nexport const useAdMeshTracker = (config?: Partial<TrackingConfig>): UseAdMeshTrackerReturn => {\n  const [isTracking, setIsTracking] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const mergedConfig = { ...globalConfig, ...config };\n\n  const log = useCallback((message: string, data?: any) => {\n    if (mergedConfig.debug) {\n      console.log(`[AdMesh Tracker] ${message}`, data);\n    }\n  }, [mergedConfig.debug]);\n\n  const sendTrackingEvent = useCallback(async (\n    eventType: 'click' | 'view' | 'conversion',\n    data: TrackingData\n  ): Promise<void> => {\n    if (!mergedConfig.enabled) {\n      log('Tracking disabled, skipping event', { eventType, data });\n      return;\n    }\n\n    if (!data.adId || !data.admeshLink) {\n      const errorMsg = 'Missing required tracking data: adId and admeshLink are required';\n      log(errorMsg, data);\n      setError(errorMsg);\n      return;\n    }\n\n    setIsTracking(true);\n    setError(null);\n\n    const payload = {\n      event_type: eventType,\n      ad_id: data.adId,\n      admesh_link: data.admeshLink,\n      product_id: data.productId,\n      user_id: data.userId,\n      session_id: data.sessionId,\n      revenue: data.revenue,\n      conversion_type: data.conversionType,\n      metadata: data.metadata,\n      timestamp: new Date().toISOString(),\n      user_agent: navigator.userAgent,\n      referrer: document.referrer,\n      page_url: window.location.href\n    };\n\n    log(`Sending ${eventType} event`, payload);\n\n    let lastError: Error | null = null;\n    \n    for (let attempt = 1; attempt <= (mergedConfig.retryAttempts || 3); attempt++) {\n      try {\n        const response = await fetch(`${mergedConfig.apiBaseUrl}/events`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(payload),\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const result = await response.json();\n        log(`${eventType} event tracked successfully`, result);\n        setIsTracking(false);\n        return;\n\n      } catch (err) {\n        lastError = err as Error;\n        log(`Attempt ${attempt} failed for ${eventType} event`, err);\n        \n        if (attempt < (mergedConfig.retryAttempts || 3)) {\n          await new Promise(resolve => \n            setTimeout(resolve, (mergedConfig.retryDelay || 1000) * attempt)\n          );\n        }\n      }\n    }\n\n    // All attempts failed\n    const errorMsg = `Failed to track ${eventType} event after ${mergedConfig.retryAttempts} attempts: ${lastError?.message}`;\n    log(errorMsg, lastError);\n    setError(errorMsg);\n    setIsTracking(false);\n  }, [mergedConfig, log]);\n\n  const trackClick = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('click', data);\n  }, [sendTrackingEvent]);\n\n  const trackView = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('view', data);\n  }, [sendTrackingEvent]);\n\n  const trackConversion = useCallback(async (data: TrackingData): Promise<void> => {\n    if (!data.revenue && !data.conversionType) {\n      log('Warning: Conversion tracking without revenue or conversion type', data);\n    }\n    return sendTrackingEvent('conversion', data);\n  }, [sendTrackingEvent]);\n\n  return {\n    trackClick,\n    trackView,\n    trackConversion,\n    isTracking,\n    error\n  };\n};\n\n// Utility function to build admesh_link with tracking parameters\nexport const buildAdMeshLink = (\n  baseLink: string, \n  adId: string, \n  additionalParams?: Record<string, string>\n): string => {\n  try {\n    const url = new URL(baseLink);\n    url.searchParams.set('ad_id', adId);\n    url.searchParams.set('utm_source', 'admesh');\n    url.searchParams.set('utm_medium', 'recommendation');\n    \n    if (additionalParams) {\n      Object.entries(additionalParams).forEach(([key, value]) => {\n        url.searchParams.set(key, value);\n      });\n    }\n    \n    return url.toString();\n  } catch (error) {\n    console.warn('[AdMesh] Invalid URL provided to buildAdMeshLink:', baseLink);\n    return baseLink;\n  }\n};\n\n// Helper function to extract tracking data from recommendation\nexport const extractTrackingData = (\n  recommendation: { ad_id: string; admesh_link: string; product_id: string },\n  additionalData?: Partial<TrackingData>\n): TrackingData => {\n  return {\n    adId: recommendation.ad_id,\n    admeshLink: recommendation.admesh_link,\n    productId: recommendation.product_id,\n    ...additionalData\n  };\n};\n", "import React, { useCallback, useEffect, useRef } from 'react';\nimport type { AdMeshLinkTrackerProps } from '../types/index';\nimport { useAdMeshTracker, extractTrackingData } from '../hooks/useAdMeshTracker';\n\nexport const AdMeshLinkTracker: React.FC<AdMeshLinkTrackerProps> = ({\n  adId,\n  admeshLink,\n  productId,\n  children,\n  onClick,\n  trackingData,\n  className\n}) => {\n  const { trackClick, trackView } = useAdMeshTracker();\n  const elementRef = useRef<HTMLDivElement>(null);\n  const hasTrackedView = useRef(false);\n\n  // Track view when component becomes visible\n  useEffect(() => {\n    if (!elementRef.current || hasTrackedView.current) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting && !hasTrackedView.current) {\n            hasTrackedView.current = true;\n            trackView({\n              adId,\n              admeshLink,\n              productId,\n              ...trackingData\n            }).catch(console.error);\n          }\n        });\n      },\n      {\n        threshold: 0.5, // Track when 50% of the element is visible\n        rootMargin: '0px'\n      }\n    );\n\n    observer.observe(elementRef.current);\n\n    return () => {\n      observer.disconnect();\n    };\n  }, [adId, admeshLink, productId, trackingData, trackView]);\n\n  const handleClick = useCallback(async (event: React.MouseEvent) => {\n    // Track the click\n    try {\n      await trackClick({\n        adId,\n        admeshLink,\n        productId,\n        ...trackingData\n      });\n    } catch (error) {\n      console.error('Failed to track click:', error);\n    }\n\n    // Call custom onClick handler if provided\n    if (onClick) {\n      onClick();\n    }\n\n    // If the children contain a link, let the browser handle navigation\n    // Otherwise, navigate programmatically\n    const target = event.target as HTMLElement;\n    const link = target.closest('a');\n    \n    if (!link) {\n      // No link found, navigate programmatically\n      window.open(admeshLink, '_blank', 'noopener,noreferrer');\n    }\n    // If there's a link, let the browser handle it naturally\n  }, [adId, admeshLink, productId, trackingData, trackClick, onClick]);\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      onClick={handleClick}\n      style={{ cursor: 'pointer' }}\n    >\n      {children}\n    </div>\n  );\n};\n\nAdMeshLinkTracker.displayName = 'AdMeshLinkTracker';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshProductCardProps, BadgeType } from '../types/index';\nimport { AdMeshBadge } from './AdMeshBadge';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshProductCard: React.FC<AdMeshProductCardProps> = ({\n  recommendation,\n  theme,\n  showMatchScore = true,\n  showBadges = true,\n  maxKeywords = 3,\n  onClick,\n  onTrackView,\n  className\n}) => {\n  // Generate badges based on recommendation data\n  const badges = useMemo((): BadgeType[] => {\n    const generatedBadges: BadgeType[] = [];\n    \n    // Add Top Match badge for high match scores\n    if (recommendation.intent_match_score >= 0.8) {\n      generatedBadges.push('Top Match');\n    }\n    \n    // Add Free Tier badge\n    if (recommendation.has_free_tier) {\n      generatedBadges.push('Free Tier');\n    }\n    \n    // Add Trial Available badge\n    if (recommendation.trial_days && recommendation.trial_days > 0) {\n      generatedBadges.push('Trial Available');\n    }\n    \n    // Add AI Powered badge (check if AI-related keywords exist)\n    const aiKeywords = ['ai', 'artificial intelligence', 'machine learning', 'ml', 'automation'];\n    const hasAIKeywords = recommendation.keywords?.some(keyword => \n      aiKeywords.some(ai => keyword.toLowerCase().includes(ai))\n    ) || recommendation.title.toLowerCase().includes('ai');\n    \n    if (hasAIKeywords) {\n      generatedBadges.push('AI Powered');\n    }\n    \n    return generatedBadges;\n  }, [recommendation]);\n\n  // Format match score as percentage\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  // Limit keywords display\n  const displayKeywords = recommendation.keywords?.slice(0, maxKeywords) || [];\n  const hasMoreKeywords = (recommendation.keywords?.length || 0) > maxKeywords;\n\n  const cardClasses = classNames(\n    'admesh-component',\n    'admesh-card',\n    'admesh-product-card',\n    {\n      [`admesh-product-card--${theme?.mode}`]: theme?.mode,\n    },\n    className\n  );\n\n  const cardStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n    '--admesh-primary-hover': theme.accentColor + 'dd', // Add some transparency for hover\n  } as React.CSSProperties : undefined;\n\n  return (\n    <AdMeshLinkTracker\n      adId={recommendation.ad_id}\n      admeshLink={recommendation.admesh_link}\n      productId={recommendation.product_id}\n      onClick={() => onClick?.(recommendation.ad_id, recommendation.admesh_link)}\n      trackingData={{ \n        title: recommendation.title,\n        matchScore: recommendation.intent_match_score \n      }}\n      className={cardClasses}\n    >\n      <div \n        className=\"admesh-product-card__container\"\n        style={cardStyle}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Header with badges and match score */}\n        <div className=\"admesh-product-card__header\">\n          {showBadges && badges.length > 0 && (\n            <div className=\"admesh-product-card__badges\">\n              {badges.map((badge, index) => (\n                <AdMeshBadge key={`${badge}-${index}`} type={badge} size=\"sm\" />\n              ))}\n            </div>\n          )}\n          \n          {showMatchScore && (\n            <div className=\"admesh-product-card__match-score\">\n              <span className=\"admesh-text-xs admesh-text-muted\">\n                {matchScorePercentage}% match\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Main content */}\n        <div className=\"admesh-product-card__content\">\n          <h3 className=\"admesh-product-card__title admesh-text-lg admesh-font-semibold\">\n            {recommendation.title}\n          </h3>\n          \n          <p className=\"admesh-product-card__reason admesh-text-sm admesh-text-secondary\">\n            {recommendation.reason}\n          </p>\n\n          {/* Keywords */}\n          {displayKeywords.length > 0 && (\n            <div className=\"admesh-product-card__keywords\">\n              {displayKeywords.map((keyword, index) => (\n                <span \n                  key={index}\n                  className=\"admesh-product-card__keyword admesh-badge admesh-badge--secondary admesh-badge--sm\"\n                >\n                  {keyword}\n                </span>\n              ))}\n              {hasMoreKeywords && (\n                <span className=\"admesh-product-card__keyword-more admesh-text-xs admesh-text-muted\">\n                  +{(recommendation.keywords?.length || 0) - maxKeywords} more\n                </span>\n              )}\n            </div>\n          )}\n\n          {/* Additional info */}\n          <div className=\"admesh-product-card__meta\">\n            {recommendation.pricing && (\n              <div className=\"admesh-product-card__pricing admesh-text-sm\">\n                <span className=\"admesh-text-muted\">Pricing: </span>\n                <span className=\"admesh-font-medium\">{recommendation.pricing}</span>\n              </div>\n            )}\n            \n            {recommendation.trial_days && recommendation.trial_days > 0 && (\n              <div className=\"admesh-product-card__trial admesh-text-sm admesh-text-muted\">\n                {recommendation.trial_days}-day free trial\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Footer with CTA */}\n        <div className=\"admesh-product-card__footer\">\n          <button className=\"admesh-button admesh-button--primary admesh-product-card__cta\">\n            Visit Offer\n            <span className=\"admesh-sr-only\">\n              for {recommendation.title}\n            </span>\n          </button>\n        </div>\n      </div>\n    </AdMeshLinkTracker>\n  );\n};\n\nAdMeshProductCard.displayName = 'AdMeshProductCard';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCompareTableProps } from '../types/index';\nimport { AdMeshBadge } from './AdMeshBadge';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshCompareTable: React.FC<AdMeshCompareTableProps> = ({\n  recommendations,\n  theme,\n  maxProducts = 3,\n  showMatchScores = true,\n  showFeatures = true,\n  onProductClick,\n  className\n}) => {\n  // Limit the number of products to compare\n  const productsToCompare = useMemo(() => {\n    return recommendations.slice(0, maxProducts);\n  }, [recommendations, maxProducts]);\n\n  // Extract all unique features across products\n  const allFeatures = useMemo(() => {\n    const featuresSet = new Set<string>();\n    productsToCompare.forEach(product => {\n      product.features?.forEach(feature => featuresSet.add(feature));\n    });\n    return Array.from(featuresSet).slice(0, 8); // Limit to 8 features for readability\n  }, [productsToCompare]);\n\n  const tableClasses = classNames(\n    'admesh-component',\n    'admesh-compare-table',\n    {\n      [`admesh-compare-table--${theme?.mode}`]: theme?.mode,\n    },\n    className\n  );\n\n  const tableStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (productsToCompare.length === 0) {\n    return (\n      <div className={tableClasses}>\n        <div className=\"admesh-compare-table__empty\">\n          <p className=\"admesh-text-muted\">No products to compare</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div \n      className={tableClasses}\n      style={tableStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"admesh-compare-table__container\">\n        <div className=\"admesh-compare-table__header\">\n          <h3 className=\"admesh-compare-table__title admesh-text-xl admesh-font-semibold\">\n            Product Comparison\n          </h3>\n          <p className=\"admesh-compare-table__subtitle admesh-text-sm admesh-text-muted\">\n            Compare {productsToCompare.length} products side by side\n          </p>\n        </div>\n\n        <div className=\"admesh-compare-table__scroll-container\">\n          <table className=\"admesh-compare-table__table\">\n            <thead>\n              <tr>\n                <th className=\"admesh-compare-table__row-header\">\n                  <span className=\"admesh-sr-only\">Feature</span>\n                </th>\n                {productsToCompare.map((product, index) => (\n                  <th key={product.product_id || index} className=\"admesh-compare-table__product-header\">\n                    <AdMeshLinkTracker\n                      adId={product.ad_id}\n                      admeshLink={product.admesh_link}\n                      productId={product.product_id}\n                      onClick={() => onProductClick?.(product.ad_id, product.admesh_link)}\n                      className=\"admesh-compare-table__product-header-content\"\n                    >\n                      <div className=\"admesh-compare-table__product-title\">\n                        <h4 className=\"admesh-text-base admesh-font-semibold admesh-truncate\">\n                          {product.title}\n                        </h4>\n                        {showMatchScores && (\n                          <div className=\"admesh-compare-table__match-score\">\n                            <span className=\"admesh-text-xs admesh-text-muted\">\n                              {Math.round(product.intent_match_score * 100)}% match\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                      \n                      {/* Badges */}\n                      <div className=\"admesh-compare-table__badges\">\n                        {product.has_free_tier && (\n                          <AdMeshBadge type=\"Free Tier\" size=\"sm\" />\n                        )}\n                        {product.trial_days && product.trial_days > 0 && (\n                          <AdMeshBadge type=\"Trial Available\" size=\"sm\" />\n                        )}\n                        {product.intent_match_score >= 0.8 && (\n                          <AdMeshBadge type=\"Top Match\" size=\"sm\" />\n                        )}\n                      </div>\n\n                      <button className=\"admesh-button admesh-button--primary admesh-button--sm admesh-compare-table__cta\">\n                        Visit Offer\n                      </button>\n                    </AdMeshLinkTracker>\n                  </th>\n                ))}\n              </tr>\n            </thead>\n            \n            <tbody>\n              {/* Pricing row */}\n              <tr>\n                <td className=\"admesh-compare-table__row-header admesh-font-medium\">\n                  Pricing\n                </td>\n                {productsToCompare.map((product, index) => (\n                  <td key={product.product_id || index} className=\"admesh-compare-table__cell\">\n                    <span className=\"admesh-text-sm\">\n                      {product.pricing || 'Contact for pricing'}\n                    </span>\n                  </td>\n                ))}\n              </tr>\n\n              {/* Trial period row */}\n              <tr>\n                <td className=\"admesh-compare-table__row-header admesh-font-medium\">\n                  Free Trial\n                </td>\n                {productsToCompare.map((product, index) => (\n                  <td key={product.product_id || index} className=\"admesh-compare-table__cell\">\n                    <span className=\"admesh-text-sm\">\n                      {product.trial_days ? `${product.trial_days} days` : 'No trial'}\n                    </span>\n                  </td>\n                ))}\n              </tr>\n\n              {/* Features rows */}\n              {showFeatures && allFeatures.map((feature, featureIndex) => (\n                <tr key={featureIndex}>\n                  <td className=\"admesh-compare-table__row-header admesh-font-medium\">\n                    {feature}\n                  </td>\n                  {productsToCompare.map((product, productIndex) => (\n                    <td key={product.product_id || productIndex} className=\"admesh-compare-table__cell\">\n                      <span className=\"admesh-text-sm\">\n                        {product.features?.includes(feature) ? (\n                          <span className=\"admesh-compare-table__check\">✓</span>\n                        ) : (\n                          <span className=\"admesh-compare-table__cross\">—</span>\n                        )}\n                      </span>\n                    </td>\n                  ))}\n                </tr>\n              ))}\n\n              {/* Keywords row */}\n              <tr>\n                <td className=\"admesh-compare-table__row-header admesh-font-medium\">\n                  Keywords\n                </td>\n                {productsToCompare.map((product, index) => (\n                  <td key={product.product_id || index} className=\"admesh-compare-table__cell\">\n                    <div className=\"admesh-compare-table__keywords\">\n                      {product.keywords?.slice(0, 3).map((keyword, keywordIndex) => (\n                        <span \n                          key={keywordIndex}\n                          className=\"admesh-badge admesh-badge--secondary admesh-badge--sm\"\n                        >\n                          {keyword}\n                        </span>\n                      ))}\n                      {(product.keywords?.length || 0) > 3 && (\n                        <span className=\"admesh-text-xs admesh-text-muted\">\n                          +{(product.keywords?.length || 0) - 3}\n                        </span>\n                      )}\n                    </div>\n                  </td>\n                ))}\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAdMeshCompareTable.displayName = 'AdMeshCompareTable';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshLayoutProps, IntentType } from '../types/index';\nimport { AdMeshProductCard } from './AdMeshProductCard';\nimport { AdMeshCompareTable } from './AdMeshCompareTable';\n\n// Layout selection logic based on intent type and data characteristics\nconst selectOptimalLayout = (\n  recommendations: any[],\n  intentType?: IntentType,\n  autoLayout?: boolean\n): 'cards' | 'compare' | 'list' => {\n  if (!autoLayout && intentType) {\n    // Use explicit intent type mapping\n    switch (intentType) {\n      case 'compare_products':\n        return 'compare';\n      case 'best_for_use_case':\n      case 'trial_demo':\n      case 'budget_conscious':\n        return 'cards';\n      default:\n        return 'cards';\n    }\n  }\n\n  // Auto-layout logic based on data characteristics\n  const productCount = recommendations.length;\n  \n  // If we have 2-4 products with features, use comparison table\n  if (productCount >= 2 && productCount <= 4) {\n    const hasFeatures = recommendations.some(rec => rec.features && rec.features.length > 0);\n    const hasPricing = recommendations.some(rec => rec.pricing);\n    \n    if (hasFeatures || hasPricing) {\n      return 'compare';\n    }\n  }\n  \n  // Default to cards layout\n  return 'cards';\n};\n\nexport const AdMeshLayout: React.FC<AdMeshLayoutProps> = ({\n  recommendations,\n  intentType,\n  theme,\n  maxDisplayed = 6,\n  showMatchScores = true,\n  showFeatures = true,\n  autoLayout = true,\n  onProductClick,\n  onTrackView,\n  className\n}) => {\n  // Limit recommendations to display\n  const displayRecommendations = useMemo(() => {\n    return recommendations.slice(0, maxDisplayed);\n  }, [recommendations, maxDisplayed]);\n\n  // Determine the optimal layout\n  const layout = useMemo(() => {\n    return selectOptimalLayout(displayRecommendations, intentType, autoLayout);\n  }, [displayRecommendations, intentType, autoLayout]);\n\n  const containerClasses = classNames(\n    'admesh-component',\n    'admesh-layout',\n    `admesh-layout--${layout}`,\n    {\n      [`admesh-layout--${theme?.mode}`]: theme?.mode,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (displayRecommendations.length === 0) {\n    return (\n      <div className={containerClasses}>\n        <div className=\"admesh-layout__empty\">\n          <div className=\"admesh-layout__empty-content\">\n            <h3 className=\"admesh-text-lg admesh-font-semibold admesh-text-muted\">\n              No recommendations available\n            </h3>\n            <p className=\"admesh-text-sm admesh-text-muted\">\n              Try adjusting your search criteria or check back later.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div \n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {layout === 'compare' ? (\n        <AdMeshCompareTable\n          recommendations={displayRecommendations}\n          theme={theme}\n          maxProducts={Math.min(displayRecommendations.length, 4)}\n          showMatchScores={showMatchScores}\n          showFeatures={showFeatures}\n          onProductClick={onProductClick}\n        />\n      ) : (\n        <div className=\"admesh-layout__cards-container\">\n          {/* Header for cards layout */}\n          <div className=\"admesh-layout__header\">\n            <h3 className=\"admesh-layout__title admesh-text-xl admesh-font-semibold\">\n              Recommended Products\n            </h3>\n            <p className=\"admesh-layout__subtitle admesh-text-sm admesh-text-muted\">\n              {displayRecommendations.length} product{displayRecommendations.length !== 1 ? 's' : ''} found\n            </p>\n          </div>\n\n          {/* Cards grid */}\n          <div className=\"admesh-layout__cards-grid\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshProductCard\n                key={recommendation.product_id || recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                showMatchScore={showMatchScores}\n                showBadges={true}\n                maxKeywords={3}\n                onClick={onProductClick}\n                onTrackView={onTrackView}\n              />\n            ))}\n          </div>\n\n          {/* Show more indicator if there are more recommendations */}\n          {recommendations.length > maxDisplayed && (\n            <div className=\"admesh-layout__more-indicator\">\n              <p className=\"admesh-text-sm admesh-text-muted\">\n                Showing {maxDisplayed} of {recommendations.length} recommendations\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nAdMeshLayout.displayName = 'AdMeshLayout';\n", "// AdMesh UI SDK - Main Entry Point\n\n// Export all components\nexport {\n  AdMeshProductCard,\n  AdMeshCompareTable,\n  AdMeshBadge,\n  AdMeshLayout,\n  AdMeshLinkTracker\n} from './components';\n\n// Export hooks\nexport {\n  useAdMeshTracker,\n  setAdMeshTrackerConfig,\n  buildAdMeshLink,\n  extractTrackingData\n} from './hooks/useAdMeshTracker';\n\n// Export types\nexport type {\n  AdMeshRecommendation,\n  AdMeshTheme,\n  IntentType,\n  BadgeType,\n  BadgeVariant,\n  BadgeSize,\n  TrackingData,\n  AdMeshProductCardProps,\n  AdMeshCompareTableProps,\n  AdMeshBadgeProps,\n  AdMeshLayoutProps,\n  AdMeshLinkTrackerProps,\n  UseAdMeshTrackerReturn,\n  AgentRecommendationResponse,\n  AdMeshConfig\n} from './types/index';\n\n// Export styles (consumers can import this separately)\nimport './styles/index.css';\n\n// Version info\nexport const VERSION = '0.1.0';\n\n// Default configuration\nexport const DEFAULT_CONFIG = {\n  trackingEnabled: true,\n  debug: false,\n  theme: {\n    mode: 'light' as const,\n    accentColor: '#2563eb'\n  }\n};\n"], "names": ["REACT_ELEMENT_TYPE", "REACT_FRAGMENT_TYPE", "jsxProd", "type", "config", "<PERSON><PERSON><PERSON>", "key", "propName", "reactJsxRuntime_production", "getComponentNameFromType", "REACT_CLIENT_REFERENCE", "REACT_PROFILER_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_ACTIVITY_TYPE", "REACT_PORTAL_TYPE", "REACT_CONTEXT_TYPE", "REACT_CONSUMER_TYPE", "REACT_FORWARD_REF_TYPE", "innerType", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "testStringCoercion", "value", "checkKeyStringCoercion", "JSCompiler_inline_result", "JSCompiler_temp_const", "JSCompiler_inline_result$jscomp$0", "getTaskName", "name", "get<PERSON>wner", "dispatcher", "ReactSharedInternals", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "getter", "defineKeyPropWarningGetter", "props", "displayName", "warnAboutAccessingKey", "specialPropKeyWarningShown", "elementRefGetterWithDeprecationWarning", "componentName", "didWarnAboutElementRef", "ReactElement", "self", "source", "owner", "debugStack", "debugTask", "jsxDEVImpl", "isStaticChildren", "children", "isArrayImpl", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "k", "didWarnAboutKeySpread", "node", "React", "require$$0", "createTask", "callStackForError", "unknownOwnerDebugStack", "unknownOwnerDebugTask", "reactJsxRuntime_development", "trackActualOwner", "jsxRuntimeModule", "require$$1", "hasOwn", "classNames", "classes", "i", "arg", "appendClass", "parseValue", "newClass", "module", "badgeTypeVariants", "badgeTypeIcons", "AdMeshBadge", "variant", "size", "className", "effectiveVariant", "icon", "badgeClasses", "jsxs", "jsx", "DEFAULT_TRACKING_URL", "globalConfig", "setAdMeshTrackerConfig", "useAdMeshTracker", "isTracking", "setIsTracking", "useState", "error", "setError", "mergedConfig", "log", "useCallback", "message", "data", "sendTrackingEvent", "eventType", "errorMsg", "payload", "lastError", "attempt", "response", "result", "err", "resolve", "trackClick", "trackView", "trackConversion", "buildAdMeshLink", "baseLink", "adId", "additionalParams", "url", "extractTrackingData", "recommendation", "additionalData", "AdMeshLinkTracker", "admeshLink", "productId", "onClick", "trackingData", "elementRef", "useRef", "hasTrackedView", "useEffect", "observer", "entries", "entry", "handleClick", "event", "AdMeshProductCard", "theme", "showMatchScore", "showBadges", "maxKeywords", "onTrackView", "badges", "useMemo", "generatedBadges", "aiKeywords", "_a", "keyword", "ai", "matchScorePercentage", "displayKeywords", "hasMoreKeywords", "_b", "cardClasses", "cardStyle", "badge", "index", "_c", "AdMeshCompareTable", "recommendations", "maxProducts", "showMatchScores", "showFeatures", "onProductClick", "productsToCompare", "allFeatures", "featuresSet", "product", "feature", "tableClasses", "tableStyle", "featureIndex", "productIndex", "keywordIndex", "selectOptimalLayout", "intentType", "autoLayout", "productCount", "hasFeatures", "rec", "hasPricing", "AdMeshLayout", "maxDisplayed", "displayRecommendations", "layout", "containerClasses", "containerStyle", "VERSION", "DEFAULT_CONFIG"], "mappings": ";;;;;;;;4CAWA,IAAIA,EAAqB,OAAO,IAAI,4BAA4B,EAC9DC,EAAsB,OAAO,IAAI,gBAAgB,EACnD,SAASC,EAAQC,EAAMC,EAAQC,EAAU,CACvC,IAAIC,EAAM,KAGV,GAFWD,IAAX,SAAwBC,EAAM,GAAKD,GACxBD,EAAO,MAAlB,SAA0BE,EAAM,GAAKF,EAAO,KACxC,QAASA,EAAQ,CACnBC,EAAW,CAAE,EACb,QAASE,KAAYH,EACTG,IAAV,QAAuBF,EAASE,CAAQ,EAAIH,EAAOG,CAAQ,EAC9D,MAAMF,EAAWD,EAClB,OAAAA,EAASC,EAAS,IACX,CACL,SAAUL,EACV,KAAMG,EACN,IAAKG,EACL,IAAgBF,IAAX,OAAoBA,EAAS,KAClC,MAAOC,CACR,CACH,CACA,OAAAG,EAAA,SAAmBP,EACnBO,EAAA,IAAcN,EACdM,EAAA,KAAeN;;;;;;;;yCCtBE,QAAQ,IAAI,WAA7B,cACG,UAAY,CACX,SAASO,EAAyBN,EAAM,CACtC,GAAYA,GAAR,KAAc,OAAO,KACzB,GAAmB,OAAOA,GAAtB,WACF,OAAOA,EAAK,WAAaO,GACrB,KACAP,EAAK,aAAeA,EAAK,MAAQ,KACvC,GAAiB,OAAOA,GAApB,SAA0B,OAAOA,EACrC,OAAQA,EAAI,CACV,KAAKF,EACH,MAAO,WACT,KAAKU,EACH,MAAO,WACT,KAAKC,EACH,MAAO,aACT,KAAKC,EACH,MAAO,WACT,KAAKC,EACH,MAAO,eACT,KAAKC,GACH,MAAO,UACjB,CACM,GAAiB,OAAOZ,GAApB,SACF,OACgB,OAAOA,EAAK,KAAzB,UACC,QAAQ,MACN,mHACD,EACHA,EAAK,SACf,CACU,KAAKa,EACH,MAAO,SACT,KAAKC,EACH,OAAQd,EAAK,aAAe,WAAa,YAC3C,KAAKe,EACH,OAAQf,EAAK,SAAS,aAAe,WAAa,YACpD,KAAKgB,EACH,IAAIC,EAAYjB,EAAK,OACrB,OAAAA,EAAOA,EAAK,YACZA,IACIA,EAAOiB,EAAU,aAAeA,EAAU,MAAQ,GACnDjB,EAAcA,IAAP,GAAc,cAAgBA,EAAO,IAAM,cAC9CA,EACT,KAAKkB,GACH,OACGD,EAAYjB,EAAK,aAAe,KACxBiB,IAAT,KACIA,EACAX,EAAyBN,EAAK,IAAI,GAAK,OAE/C,KAAKmB,EACHF,EAAYjB,EAAK,SACjBA,EAAOA,EAAK,MACZ,GAAI,CACF,OAAOM,EAAyBN,EAAKiB,CAAS,CAAC,CAChD,MAAW,CAAA,CACxB,CACM,OAAO,IACb,CACI,SAASG,EAAmBC,EAAO,CACjC,MAAO,GAAKA,CAClB,CACI,SAASC,EAAuBD,EAAO,CACrC,GAAI,CACFD,EAAmBC,CAAK,EACxB,IAAIE,EAA2B,EAChC,MAAW,CACVA,EAA2B,EACnC,CACM,GAAIA,EAA0B,CAC5BA,EAA2B,QAC3B,IAAIC,EAAwBD,EAAyB,MACjDE,EACc,OAAO,QAAtB,YACC,OAAO,aACPJ,EAAM,OAAO,WAAW,GAC1BA,EAAM,YAAY,MAClB,SACF,OAAAG,EAAsB,KACpBD,EACA,2GACAE,CACD,EACML,EAAmBC,CAAK,CACvC,CACA,CACI,SAASK,EAAY1B,EAAM,CACzB,GAAIA,IAASF,EAAqB,MAAO,KACzC,GACe,OAAOE,GAApB,UACSA,IAAT,MACAA,EAAK,WAAamB,EAElB,MAAO,QACT,GAAI,CACF,IAAIQ,EAAOrB,EAAyBN,CAAI,EACxC,OAAO2B,EAAO,IAAMA,EAAO,IAAM,OAClC,MAAW,CACV,MAAO,OACf,CACA,CACI,SAASC,GAAW,CAClB,IAAIC,EAAaC,EAAqB,EACtC,OAAgBD,IAAT,KAAsB,KAAOA,EAAW,SAAU,CAC/D,CACI,SAASE,GAAe,CACtB,OAAO,MAAM,uBAAuB,CAC1C,CACI,SAASC,EAAY/B,EAAQ,CAC3B,GAAIgC,EAAe,KAAKhC,EAAQ,KAAK,EAAG,CACtC,IAAIiC,EAAS,OAAO,yBAAyBjC,EAAQ,KAAK,EAAE,IAC5D,GAAIiC,GAAUA,EAAO,eAAgB,MAAO,EACpD,CACM,OAAkBjC,EAAO,MAAlB,MACb,CACI,SAASkC,EAA2BC,EAAOC,EAAa,CACtD,SAASC,GAAwB,CAC/BC,IACIA,EAA6B,GAC/B,QAAQ,MACN,0OACAF,CACZ,EACA,CACMC,EAAsB,eAAiB,GACvC,OAAO,eAAeF,EAAO,MAAO,CAClC,IAAKE,EACL,aAAc,EACtB,CAAO,CACP,CACI,SAASE,GAAyC,CAChD,IAAIC,EAAgBnC,EAAyB,KAAK,IAAI,EACtD,OAAAoC,EAAuBD,CAAa,IAChCC,EAAuBD,CAAa,EAAI,GAC1C,QAAQ,MACN,6IACV,GACMA,EAAgB,KAAK,MAAM,IACTA,IAAX,OAA2BA,EAAgB,IACxD,CACI,SAASE,EACP3C,EACAG,EACAyC,EACAC,EACAC,EACAV,EACAW,EACAC,EACA,CACA,OAAAJ,EAAOR,EAAM,IACbpC,EAAO,CACL,SAAUH,EACV,KAAMG,EACN,IAAKG,EACL,MAAOiC,EACP,OAAQU,CACT,GACoBF,IAAX,OAAkBA,EAAO,QAAnC,KACI,OAAO,eAAe5C,EAAM,MAAO,CACjC,WAAY,GACZ,IAAKwC,CACN,CAAA,EACD,OAAO,eAAexC,EAAM,MAAO,CAAE,WAAY,GAAI,MAAO,KAAM,EACtEA,EAAK,OAAS,CAAE,EAChB,OAAO,eAAeA,EAAK,OAAQ,YAAa,CAC9C,aAAc,GACd,WAAY,GACZ,SAAU,GACV,MAAO,CACf,CAAO,EACD,OAAO,eAAeA,EAAM,aAAc,CACxC,aAAc,GACd,WAAY,GACZ,SAAU,GACV,MAAO,IACf,CAAO,EACD,OAAO,eAAeA,EAAM,cAAe,CACzC,aAAc,GACd,WAAY,GACZ,SAAU,GACV,MAAO+C,CACf,CAAO,EACD,OAAO,eAAe/C,EAAM,aAAc,CACxC,aAAc,GACd,WAAY,GACZ,SAAU,GACV,MAAOgD,CACf,CAAO,EACD,OAAO,SAAW,OAAO,OAAOhD,EAAK,KAAK,EAAG,OAAO,OAAOA,CAAI,GACxDA,CACb,CACI,SAASiD,EACPjD,EACAC,EACAC,EACAgD,EACAL,EACAD,EACAG,EACAC,EACA,CACA,IAAIG,EAAWlD,EAAO,SACtB,GAAekD,IAAX,OACF,GAAID,EACF,GAAIE,GAAYD,CAAQ,EAAG,CACzB,IACED,EAAmB,EACnBA,EAAmBC,EAAS,OAC5BD,IAEAG,EAAkBF,EAASD,CAAgB,CAAC,EAC9C,OAAO,QAAU,OAAO,OAAOC,CAAQ,CACxC,MACC,QAAQ,MACN,sJACD,OACAE,EAAkBF,CAAQ,EACjC,GAAIlB,EAAe,KAAKhC,EAAQ,KAAK,EAAG,CACtCkD,EAAW7C,EAAyBN,CAAI,EACxC,IAAIsD,EAAO,OAAO,KAAKrD,CAAM,EAAE,OAAO,SAAUsD,GAAG,CACjD,OAAiBA,KAAV,KACjB,CAAS,EACDL,EACE,EAAII,EAAK,OACL,kBAAoBA,EAAK,KAAK,SAAS,EAAI,SAC3C,iBACNE,GAAsBL,EAAWD,CAAgB,IAC7CI,EACA,EAAIA,EAAK,OAAS,IAAMA,EAAK,KAAK,SAAS,EAAI,SAAW,KAC5D,QAAQ,MACN;AAAA;AAAA;AAAA;AAAA;AAAA,mCACAJ,EACAC,EACAG,EACAH,CACD,EACAK,GAAsBL,EAAWD,CAAgB,EAAI,GAChE,CAMM,GALAC,EAAW,KACAjD,IAAX,SACGoB,EAAuBpB,CAAQ,EAAIiD,EAAW,GAAKjD,GACtD8B,EAAY/B,CAAM,IACfqB,EAAuBrB,EAAO,GAAG,EAAIkD,EAAW,GAAKlD,EAAO,KAC3D,QAASA,EAAQ,CACnBC,EAAW,CAAE,EACb,QAASE,KAAYH,EACTG,IAAV,QAAuBF,EAASE,CAAQ,EAAIH,EAAOG,CAAQ,EAC9D,MAAMF,EAAWD,EAClB,OAAAkD,GACEhB,EACEjC,EACe,OAAOF,GAAtB,WACIA,EAAK,aAAeA,EAAK,MAAQ,UACjCA,CACL,EACI2C,EACL3C,EACAmD,EACAP,EACAC,EACAjB,EAAU,EACV1B,EACA6C,EACAC,CACD,CACP,CACI,SAASK,EAAkBI,EAAM,CAClB,OAAOA,GAApB,UACWA,IAAT,MACAA,EAAK,WAAa5D,GAClB4D,EAAK,SACJA,EAAK,OAAO,UAAY,EACjC,CACI,IAAIC,EAAQC,EACV9D,EAAqB,OAAO,IAAI,4BAA4B,EAC5DgB,EAAoB,OAAO,IAAI,cAAc,EAC7Cf,EAAsB,OAAO,IAAI,gBAAgB,EACjDW,EAAyB,OAAO,IAAI,mBAAmB,EACvDD,EAAsB,OAAO,IAAI,gBAAgB,EAE/CO,EAAsB,OAAO,IAAI,gBAAgB,EACnDD,EAAqB,OAAO,IAAI,eAAe,EAC/CE,EAAyB,OAAO,IAAI,mBAAmB,EACvDN,EAAsB,OAAO,IAAI,gBAAgB,EACjDC,EAA2B,OAAO,IAAI,qBAAqB,EAC3DO,GAAkB,OAAO,IAAI,YAAY,EACzCC,EAAkB,OAAO,IAAI,YAAY,EACzCP,GAAsB,OAAO,IAAI,gBAAgB,EACjDL,GAAyB,OAAO,IAAI,wBAAwB,EAC5DuB,EACE4B,EAAM,gEACRzB,EAAiB,OAAO,UAAU,eAClCmB,GAAc,MAAM,QACpBQ,EAAa,QAAQ,WACjB,QAAQ,WACR,UAAY,CACV,OAAO,IACR,EACPF,EAAQ,CACN,2BAA4B,SAAUG,EAAmB,CACvD,OAAOA,EAAmB,CAClC,CACK,EACD,IAAItB,EACAG,EAAyB,CAAE,EAC3BoB,EAAyBJ,EAAM,0BAA0B,EAAE,KAC7DA,EACA3B,CACN,EAAO,EACCgC,EAAwBH,EAAWlC,EAAYK,CAAY,CAAC,EAC5DyB,GAAwB,CAAE,EAC9BQ,EAAA,SAAmBlE,EACnBkE,EAAW,IAAG,SAAUhE,EAAMC,EAAQC,EAAU2C,EAAQD,EAAM,CAC5D,IAAIqB,EACF,IAAMnC,EAAqB,6BAC7B,OAAOmB,EACLjD,EACAC,EACAC,EACA,GACA2C,EACAD,EACAqB,EACI,MAAM,uBAAuB,EAC7BH,EACJG,EAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,EAAI+D,CACpD,CACF,EACDC,EAAY,KAAG,SAAUhE,EAAMC,EAAQC,EAAU2C,EAAQD,EAAM,CAC7D,IAAIqB,EACF,IAAMnC,EAAqB,6BAC7B,OAAOmB,EACLjD,EACAC,EACAC,EACA,GACA2C,EACAD,EACAqB,EACI,MAAM,uBAAuB,EAC7BH,EACJG,EAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,EAAI+D,CACpD,CACF,CACL,EAAM,2CCnWF,QAAQ,IAAI,WAAa,aAC3BG,EAAA,QAAiBP,GAAgD,EAEjEO,EAAA,QAAiBC,GAAiD;;;;qDCEnE,UAAY,CAGZ,IAAIC,EAAS,CAAA,EAAG,eAEhB,SAASC,GAAc,CAGtB,QAFIC,EAAU,GAELC,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CAC1C,IAAIC,EAAM,UAAUD,CAAC,EACjBC,IACHF,EAAUG,EAAYH,EAASI,EAAWF,CAAG,CAAC,EAElD,CAEE,OAAOF,CACT,CAEC,SAASI,EAAYF,EAAK,CACzB,GAAI,OAAOA,GAAQ,UAAY,OAAOA,GAAQ,SAC7C,OAAOA,EAGR,GAAI,OAAOA,GAAQ,SAClB,MAAO,GAGR,GAAI,MAAM,QAAQA,CAAG,EACpB,OAAOH,EAAW,MAAM,KAAMG,CAAG,EAGlC,GAAIA,EAAI,WAAa,OAAO,UAAU,UAAY,CAACA,EAAI,SAAS,SAAQ,EAAG,SAAS,eAAe,EAClG,OAAOA,EAAI,SAAU,EAGtB,IAAIF,EAAU,GAEd,QAASnE,KAAOqE,EACXJ,EAAO,KAAKI,EAAKrE,CAAG,GAAKqE,EAAIrE,CAAG,IACnCmE,EAAUG,EAAYH,EAASnE,CAAG,GAIpC,OAAOmE,CACT,CAEC,SAASG,EAAapD,EAAOsD,EAAU,CACtC,OAAKA,EAIDtD,EACIA,EAAQ,IAAMsD,EAGftD,EAAQsD,EAPPtD,CAQV,CAEsCuD,EAAO,SAC3CP,EAAW,QAAUA,EACrBO,UAAiBP,GAOjB,OAAO,WAAaA,CAEtB,+CCvEMQ,GAA+C,CACnD,YAAa,UACb,YAAa,UACb,aAAc,YACd,QAAW,UACX,IAAO,UACP,kBAAmB,SACrB,EAGMC,GAAqD,CACzD,YAAa,IACb,YAAa,IACb,aAAc,IACd,QAAW,IACX,IAAO,IACP,kBAAmB,GACrB,EAEaC,EAA0C,CAAC,CACtD,KAAA/E,EACA,QAAAgF,EACA,KAAAC,EAAO,KACP,UAAAC,CACF,IAAM,CACJ,MAAMC,EAAmBH,GAAWH,GAAkB7E,CAAI,GAAK,YACzDoF,EAAON,GAAe9E,CAAI,EAE1BqF,EAAehB,EACnB,mBACA,eACA,iBAAiBc,CAAgB,GACjC,iBAAiBF,CAAI,GACrBC,CACF,EAGE,OAAAI,EAAA,KAAC,OAAK,CAAA,UAAWD,EACd,SAAA,CAAAD,GAASG,EAAA,IAAA,OAAA,CAAK,UAAU,qBAAsB,SAAKH,EAAA,EACnDG,EAAA,IAAA,OAAA,CAAK,UAAU,qBAAsB,SAAKvF,CAAA,CAAA,CAAA,EAC7C,CAEJ,EAEA+E,EAAY,YAAc,cC7C1B,MAAMS,GAAuB,kCAW7B,IAAIC,EAA+B,CACjC,WAAYD,GACZ,QAAS,GACT,MAAO,GACP,cAAe,EACf,WAAY,GACd,EAEa,MAAAE,GAA0BzF,GAAoC,CACzEwF,EAAe,CAAE,GAAGA,EAAc,GAAGxF,CAAO,CAC9C,EAEa0F,GAAoB1F,GAA6D,CAC5F,KAAM,CAAC2F,EAAYC,CAAa,EAAIC,EAAAA,SAAS,EAAK,EAC5C,CAACC,EAAOC,CAAQ,EAAIF,EAAAA,SAAwB,IAAI,EAEhDG,EAAe,CAAE,GAAGR,EAAc,GAAGxF,CAAO,EAE5CiG,EAAMC,EAAAA,YAAY,CAACC,EAAiBC,IAAe,CACnDJ,EAAa,OACf,QAAQ,IAAI,oBAAoBG,CAAO,GAAIC,CAAI,CACjD,EACC,CAACJ,EAAa,KAAK,CAAC,EAEjBK,EAAoBH,EAAAA,YAAY,MACpCI,EACAF,IACkB,CACd,GAAA,CAACJ,EAAa,QAAS,CACzBC,EAAI,oCAAqC,CAAE,UAAAK,EAAW,KAAAF,CAAA,CAAM,EAC5D,MAAA,CAGF,GAAI,CAACA,EAAK,MAAQ,CAACA,EAAK,WAAY,CAClC,MAAMG,EAAW,mEACjBN,EAAIM,EAAUH,CAAI,EAClBL,EAASQ,CAAQ,EACjB,MAAA,CAGFX,EAAc,EAAI,EAClBG,EAAS,IAAI,EAEb,MAAMS,EAAU,CACd,WAAYF,EACZ,MAAOF,EAAK,KACZ,YAAaA,EAAK,WAClB,WAAYA,EAAK,UACjB,QAASA,EAAK,OACd,WAAYA,EAAK,UACjB,QAASA,EAAK,QACd,gBAAiBA,EAAK,eACtB,SAAUA,EAAK,SACf,UAAW,IAAI,KAAK,EAAE,YAAY,EAClC,WAAY,UAAU,UACtB,SAAU,SAAS,SACnB,SAAU,OAAO,SAAS,IAC5B,EAEIH,EAAA,WAAWK,CAAS,SAAUE,CAAO,EAEzC,IAAIC,EAA0B,KAE9B,QAASC,EAAU,EAAGA,IAAYV,EAAa,eAAiB,GAAIU,IAC9D,GAAA,CACF,MAAMC,EAAW,MAAM,MAAM,GAAGX,EAAa,UAAU,UAAW,CAChE,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAUQ,CAAO,CAAA,CAC7B,EAEG,GAAA,CAACG,EAAS,GACN,MAAA,IAAI,MAAM,QAAQA,EAAS,MAAM,KAAKA,EAAS,UAAU,EAAE,EAG7D,MAAAC,EAAS,MAAMD,EAAS,KAAK,EAC/BV,EAAA,GAAGK,CAAS,8BAA+BM,CAAM,EACrDhB,EAAc,EAAK,EACnB,aAEOiB,EAAK,CACAJ,EAAAI,EACZZ,EAAI,WAAWS,CAAO,eAAeJ,CAAS,SAAUO,CAAG,EAEvDH,GAAWV,EAAa,eAAiB,IAC3C,MAAM,IAAI,WACR,WAAWc,GAAUd,EAAa,YAAc,KAAQU,CAAO,CACjE,CACF,CAKE,MAAAH,EAAW,mBAAmBD,CAAS,gBAAgBN,EAAa,aAAa,cAAcS,GAAA,YAAAA,EAAW,OAAO,GACvHR,EAAIM,EAAUE,CAAS,EACvBV,EAASQ,CAAQ,EACjBX,EAAc,EAAK,CAAA,EAClB,CAACI,EAAcC,CAAG,CAAC,EAEhBc,EAAab,cAAY,MAAOE,GAC7BC,EAAkB,QAASD,CAAI,EACrC,CAACC,CAAiB,CAAC,EAEhBW,EAAYd,cAAY,MAAOE,GAC5BC,EAAkB,OAAQD,CAAI,EACpC,CAACC,CAAiB,CAAC,EAEhBY,EAAkBf,cAAY,MAAOE,IACrC,CAACA,EAAK,SAAW,CAACA,EAAK,gBACzBH,EAAI,kEAAmEG,CAAI,EAEtEC,EAAkB,aAAcD,CAAI,GAC1C,CAACC,CAAiB,CAAC,EAEf,MAAA,CACL,WAAAU,EACA,UAAAC,EACA,gBAAAC,EACA,WAAAtB,EACA,MAAAG,CACF,CACF,EAGaoB,GAAkB,CAC7BC,EACAC,EACAC,IACW,CACP,GAAA,CACI,MAAAC,EAAM,IAAI,IAAIH,CAAQ,EACxB,OAAAG,EAAA,aAAa,IAAI,QAASF,CAAI,EAC9BE,EAAA,aAAa,IAAI,aAAc,QAAQ,EACvCA,EAAA,aAAa,IAAI,aAAc,gBAAgB,EAE/CD,GACK,OAAA,QAAQA,CAAgB,EAAE,QAAQ,CAAC,CAACnH,EAAKkB,CAAK,IAAM,CACrDkG,EAAA,aAAa,IAAIpH,EAAKkB,CAAK,CAAA,CAChC,EAGIkG,EAAI,SAAS,OACN,CACN,eAAA,KAAK,oDAAqDH,CAAQ,EACnEA,CAAA,CAEX,EAGaI,GAAsB,CACjCC,EACAC,KAEO,CACL,KAAMD,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,GAAGC,CACL,GC3KWC,EAAsD,CAAC,CAClE,KAAAN,EACA,WAAAO,EACA,UAAAC,EACA,SAAA1E,EACA,QAAA2E,EACA,aAAAC,EACA,UAAA7C,CACF,IAAM,CACJ,KAAM,CAAE,WAAA8B,EAAY,UAAAC,CAAU,EAAItB,GAAiB,EAC7CqC,EAAaC,SAAuB,IAAI,EACxCC,EAAiBD,SAAO,EAAK,EAGnCE,EAAAA,UAAU,IAAM,CACd,GAAI,CAACH,EAAW,SAAWE,EAAe,QAAS,OAEnD,MAAME,EAAW,IAAI,qBAClBC,GAAY,CACHA,EAAA,QAASC,GAAU,CACrBA,EAAM,gBAAkB,CAACJ,EAAe,UAC1CA,EAAe,QAAU,GACfjB,EAAA,CACR,KAAAI,EACA,WAAAO,EACA,UAAAC,EACA,GAAGE,CAAA,CACJ,EAAE,MAAM,QAAQ,KAAK,EACxB,CACD,CACH,EACA,CACE,UAAW,GACX,WAAY,KAAA,CAEhB,EAES,OAAAK,EAAA,QAAQJ,EAAW,OAAO,EAE5B,IAAM,CACXI,EAAS,WAAW,CACtB,CAAA,EACC,CAACf,EAAMO,EAAYC,EAAWE,EAAcd,CAAS,CAAC,EAEnD,MAAAsB,EAAcpC,cAAY,MAAOqC,GAA4B,CAE7D,GAAA,CACF,MAAMxB,EAAW,CACf,KAAAK,EACA,WAAAO,EACA,UAAAC,EACA,GAAGE,CAAA,CACJ,QACMhC,EAAO,CACN,QAAA,MAAM,yBAA0BA,CAAK,CAAA,CAI3C+B,GACMA,EAAA,EAKKU,EAAM,OACD,QAAQ,GAAG,GAItB,OAAA,KAAKZ,EAAY,SAAU,qBAAqB,CACzD,EAEC,CAACP,EAAMO,EAAYC,EAAWE,EAAcf,EAAYc,CAAO,CAAC,EAGjE,OAAAvC,EAAA,IAAC,MAAA,CACC,IAAKyC,EACL,UAAA9C,EACA,QAASqD,EACT,MAAO,CAAE,OAAQ,SAAU,EAE1B,SAAApF,CAAA,CACH,CAEJ,EAEAwE,EAAkB,YAAc,oBCpFzB,MAAMc,EAAsD,CAAC,CAClE,eAAAhB,EACA,MAAAiB,EACA,eAAAC,EAAiB,GACjB,WAAAC,EAAa,GACb,YAAAC,EAAc,EACd,QAAAf,EACA,YAAAgB,EACA,UAAA5D,CACF,IAAM,WAEE,MAAA6D,EAASC,EAAAA,QAAQ,IAAmB,OACxC,MAAMC,EAA+B,CAAC,EAGlCxB,EAAe,oBAAsB,IACvCwB,EAAgB,KAAK,WAAW,EAI9BxB,EAAe,eACjBwB,EAAgB,KAAK,WAAW,EAI9BxB,EAAe,YAAcA,EAAe,WAAa,GAC3DwB,EAAgB,KAAK,iBAAiB,EAIxC,MAAMC,EAAa,CAAC,KAAM,0BAA2B,mBAAoB,KAAM,YAAY,EAK3F,SAJsBC,EAAA1B,EAAe,WAAf,YAAA0B,EAAyB,KAAKC,GAClDF,EAAW,KAAKG,GAAMD,EAAQ,YAAY,EAAE,SAASC,CAAE,CAAC,KACrD5B,EAAe,MAAM,YAAY,EAAE,SAAS,IAAI,IAGnDwB,EAAgB,KAAK,YAAY,EAG5BA,CAAA,EACN,CAACxB,CAAc,CAAC,EAGb6B,EAAuB,KAAK,MAAM7B,EAAe,mBAAqB,GAAG,EAGzE8B,IAAkBJ,EAAA1B,EAAe,WAAf,YAAA0B,EAAyB,MAAM,EAAGN,KAAgB,CAAC,EACrEW,KAAmBC,EAAAhC,EAAe,WAAf,YAAAgC,EAAyB,SAAU,GAAKZ,EAE3Da,EAAcrF,EAClB,mBACA,cACA,sBACA,CACE,CAAC,wBAAwBqE,GAAA,YAAAA,EAAO,IAAI,EAAE,EAAGA,GAAA,YAAAA,EAAO,IAClD,EACAxD,CACF,EAEMyE,EAAYjB,GAAA,MAAAA,EAAO,YAAc,CACrC,mBAAoBA,EAAM,YAC1B,yBAA0BA,EAAM,YAAc,IAAA,EACrB,OAGzB,OAAAnD,EAAA,IAACoC,EAAA,CACC,KAAMF,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,QAAS,IAAMK,GAAA,YAAAA,EAAUL,EAAe,MAAOA,EAAe,aAC9D,aAAc,CACZ,MAAOA,EAAe,MACtB,WAAYA,EAAe,kBAC7B,EACA,UAAWiC,EAEX,SAAApE,EAAA,KAAC,MAAA,CACC,UAAU,iCACV,MAAOqE,EACP,oBAAmBjB,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAACpD,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACZ,SAAA,CAAcsD,GAAAG,EAAO,OAAS,GAC5BxD,EAAA,IAAA,MAAA,CAAI,UAAU,8BACZ,SAAAwD,EAAO,IAAI,CAACa,EAAOC,UACjB9E,EAAsC,CAAA,KAAM6E,EAAO,KAAK,IAAvC,EAAA,GAAGA,CAAK,IAAIC,CAAK,EAA2B,CAC/D,CACH,CAAA,EAGDlB,SACE,MAAI,CAAA,UAAU,mCACb,SAACrD,EAAAA,KAAA,OAAA,CAAK,UAAU,mCACb,SAAA,CAAAgE,EAAqB,SAAA,CAAA,CACxB,CACF,CAAA,CAAA,EAEJ,EAGAhE,EAAAA,KAAC,MAAI,CAAA,UAAU,+BACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,iEACX,SAAAkC,EAAe,MAClB,EAEClC,EAAA,IAAA,IAAA,CAAE,UAAU,mEACV,WAAe,OAClB,EAGCgE,EAAgB,OAAS,GACvBjE,EAAA,KAAA,MAAA,CAAI,UAAU,gCACZ,SAAA,CAAgBiE,EAAA,IAAI,CAACH,EAASS,IAC7BtE,EAAA,IAAC,OAAA,CAEC,UAAU,qFAET,SAAA6D,CAAA,EAHIS,CAAA,CAKR,EACAL,GACClE,EAAA,KAAC,OAAK,CAAA,UAAU,qEAAqE,SAAA,CAAA,OAChFwE,EAAArC,EAAe,WAAf,YAAAqC,EAAyB,SAAU,GAAKjB,EAAY,OAAA,CACzD,CAAA,CAAA,EAEJ,EAIFvD,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACZ,SAAA,CAAAmC,EAAe,SACdnC,OAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,oBAAoB,SAAS,YAAA,EAC5CA,EAAA,IAAA,OAAA,CAAK,UAAU,qBAAsB,WAAe,OAAQ,CAAA,CAAA,EAC/D,EAGDkC,EAAe,YAAcA,EAAe,WAAa,GACvDnC,OAAA,MAAA,CAAI,UAAU,8DACZ,SAAA,CAAemC,EAAA,WAAW,iBAAA,CAC7B,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,8BACb,SAACnC,EAAA,KAAA,SAAA,CAAO,UAAU,gEAAgE,SAAA,CAAA,cAEhFA,EAAAA,KAAC,OAAK,CAAA,UAAU,iBAAiB,SAAA,CAAA,OAC1BmC,EAAe,KAAA,CACtB,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CAAA,CACF,CACF,CAEJ,EAEAgB,EAAkB,YAAc,oBChKzB,MAAMsB,EAAwD,CAAC,CACpE,gBAAAC,EACA,MAAAtB,EACA,YAAAuB,EAAc,EACd,gBAAAC,EAAkB,GAClB,aAAAC,EAAe,GACf,eAAAC,EACA,UAAAlF,CACF,IAAM,CAEE,MAAAmF,EAAoBrB,EAAAA,QAAQ,IACzBgB,EAAgB,MAAM,EAAGC,CAAW,EAC1C,CAACD,EAAiBC,CAAW,CAAC,EAG3BK,EAActB,EAAAA,QAAQ,IAAM,CAC1B,MAAAuB,MAAkB,IACxB,OAAAF,EAAkB,QAAmBG,GAAA,QACnCrB,EAAAqB,EAAQ,WAAR,MAAArB,EAAkB,QAAQsB,GAAWF,EAAY,IAAIE,CAAO,EAAC,CAC9D,EACM,MAAM,KAAKF,CAAW,EAAE,MAAM,EAAG,CAAC,CAAA,EACxC,CAACF,CAAiB,CAAC,EAEhBK,EAAerG,EACnB,mBACA,uBACA,CACE,CAAC,yBAAyBqE,GAAA,YAAAA,EAAO,IAAI,EAAE,EAAGA,GAAA,YAAAA,EAAO,IACnD,EACAxD,CACF,EAEMyF,EAAajC,GAAA,MAAAA,EAAO,YAAc,CACtC,mBAAoBA,EAAM,WAAA,EACD,OAEvB,OAAA2B,EAAkB,SAAW,EAE5B9E,EAAA,IAAA,MAAA,CAAI,UAAWmF,EACd,eAAC,MAAI,CAAA,UAAU,8BACb,SAAAnF,EAAA,IAAC,IAAE,CAAA,UAAU,oBAAoB,SAAA,wBAAA,CAAsB,CACzD,CAAA,EACF,EAKFA,EAAA,IAAC,MAAA,CACC,UAAWmF,EACX,MAAOC,EACP,oBAAmBjC,GAAA,YAAAA,EAAO,KAE1B,SAAApD,EAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,kEAAkE,SAEhF,qBAAA,EACAD,EAAAA,KAAC,IAAE,CAAA,UAAU,kEAAkE,SAAA,CAAA,WACpE+E,EAAkB,OAAO,wBAAA,CACpC,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,yCACb,SAAC/E,EAAA,KAAA,QAAA,CAAM,UAAU,8BACf,SAAA,CAACC,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,mCACZ,SAAAA,EAAA,IAAC,QAAK,UAAU,iBAAiB,mBAAO,CAC1C,CAAA,EACC8E,EAAkB,IAAI,CAACG,EAASX,IAC9BtE,EAAAA,IAAA,KAAA,CAAqC,UAAU,uCAC9C,SAAAD,EAAA,KAACqC,EAAA,CACC,KAAM6C,EAAQ,MACd,WAAYA,EAAQ,YACpB,UAAWA,EAAQ,WACnB,QAAS,IAAMJ,GAAA,YAAAA,EAAiBI,EAAQ,MAAOA,EAAQ,aACvD,UAAU,+CAEV,SAAA,CAAClF,EAAAA,KAAA,MAAA,CAAI,UAAU,sCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,wDACX,SAAAiF,EAAQ,MACX,EACCN,SACE,MAAI,CAAA,UAAU,oCACb,SAAC5E,EAAAA,KAAA,OAAA,CAAK,UAAU,mCACb,SAAA,CAAK,KAAA,MAAMkF,EAAQ,mBAAqB,GAAG,EAAE,SAAA,CAAA,CAChD,CACF,CAAA,CAAA,EAEJ,EAGAlF,EAAAA,KAAC,MAAI,CAAA,UAAU,+BACZ,SAAA,CAAAkF,EAAQ,eACNjF,EAAAA,IAAAR,EAAA,CAAY,KAAK,YAAY,KAAK,KAAK,EAEzCyF,EAAQ,YAAcA,EAAQ,WAAa,SACzCzF,EAAY,CAAA,KAAK,kBAAkB,KAAK,IAAK,CAAA,EAE/CyF,EAAQ,oBAAsB,IAC7BjF,MAACR,GAAY,KAAK,YAAY,KAAK,IAAK,CAAA,CAAA,EAE5C,EAECQ,EAAA,IAAA,SAAA,CAAO,UAAU,mFAAmF,SAErG,aAAA,CAAA,CAAA,CAAA,CAAA,GApCKiF,EAAQ,YAAcX,CAsC/B,CACD,CAAA,CAAA,CACH,CACF,CAAA,SAEC,QAEC,CAAA,SAAA,CAAAvE,OAAC,KACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAEpE,UAAA,EACC8E,EAAkB,IAAI,CAACG,EAASX,IAC9BtE,EAAAA,IAAA,KAAA,CAAqC,UAAU,6BAC9C,SAAAA,EAAA,IAAC,QAAK,UAAU,iBACb,WAAQ,SAAW,qBAAA,CACtB,GAHOiF,EAAQ,YAAcX,CAI/B,CACD,CAAA,EACH,SAGC,KACC,CAAA,SAAA,CAACtE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAEpE,aAAA,EACC8E,EAAkB,IAAI,CAACG,EAASX,UAC9B,KAAqC,CAAA,UAAU,6BAC9C,SAAAtE,EAAAA,IAAC,OAAK,CAAA,UAAU,iBACb,SAAQiF,EAAA,WAAa,GAAGA,EAAQ,UAAU,QAAU,WACvD,CAHO,EAAAA,EAAQ,YAAcX,CAI/B,CACD,CAAA,EACH,EAGCM,GAAgBG,EAAY,IAAI,CAACG,EAASG,WACxC,KACC,CAAA,SAAA,CAACrF,EAAA,IAAA,KAAA,CAAG,UAAU,sDACX,SACHkF,EAAA,EACCJ,EAAkB,IAAI,CAACG,EAASK,IAC9BtF,OAAAA,OAAAA,EAAAA,IAAA,KAAA,CAA4C,UAAU,6BACrD,eAAC,OAAK,CAAA,UAAU,iBACb,UAAA4D,EAAAqB,EAAQ,WAAR,MAAArB,EAAkB,SAASsB,GAC1BlF,EAAAA,IAAC,OAAK,CAAA,UAAU,8BAA8B,SAAA,GAAC,CAAA,EAE9CA,EAAAA,IAAA,OAAA,CAAK,UAAU,8BAA8B,YAAC,CAAA,CAEnD,CAAA,GAPOiF,EAAQ,YAAcK,CAQ/B,EACD,CAAA,CAAA,EAdMD,CAeT,CACD,SAGA,KACC,CAAA,SAAA,CAACrF,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAEpE,WAAA,EACC8E,EAAkB,IAAI,CAACG,EAASX,IAC/BtE,WAAAA,OAAAA,EAAAA,IAAC,KAAqC,CAAA,UAAU,6BAC9C,SAAAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACZ,SAAA,EAAQ6D,EAAAqB,EAAA,WAAA,YAAArB,EAAU,MAAM,EAAG,GAAG,IAAI,CAACC,EAAS0B,IAC3CvF,EAAA,IAAC,OAAA,CAEC,UAAU,wDAET,SAAA6D,CAAA,EAHI0B,CAAA,MAMPrB,EAAAe,EAAQ,WAAR,YAAAf,EAAkB,SAAU,GAAK,GAChCnE,EAAA,KAAA,OAAA,CAAK,UAAU,mCAAmC,SAAA,CAAA,OAC9CwE,EAAAU,EAAQ,WAAR,YAAAV,EAAkB,SAAU,GAAK,CAAA,CACtC,CAAA,CAAA,CAAA,CAEJ,CAfO,EAAAU,EAAQ,YAAcX,CAgB/B,EACD,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAEJ,EAEAE,EAAmB,YAAc,qBClMjC,MAAMgB,GAAsB,CAC1Bf,EACAgB,EACAC,IACiC,CAC7B,GAAA,CAACA,GAAcD,EAEjB,OAAQA,EAAY,CAClB,IAAK,mBACI,MAAA,UACT,IAAK,oBACL,IAAK,aACL,IAAK,mBACI,MAAA,QACT,QACS,MAAA,OAAA,CAKb,MAAME,EAAelB,EAAgB,OAGjC,GAAAkB,GAAgB,GAAKA,GAAgB,EAAG,CACpC,MAAAC,EAAcnB,EAAgB,KAAKoB,GAAOA,EAAI,UAAYA,EAAI,SAAS,OAAS,CAAC,EACjFC,EAAarB,EAAgB,KAAKoB,GAAOA,EAAI,OAAO,EAE1D,GAAID,GAAeE,EACV,MAAA,SACT,CAIK,MAAA,OACT,EAEaC,GAA4C,CAAC,CACxD,gBAAAtB,EACA,WAAAgB,EACA,MAAAtC,EACA,aAAA6C,EAAe,EACf,gBAAArB,EAAkB,GAClB,aAAAC,EAAe,GACf,WAAAc,EAAa,GACb,eAAAb,EACA,YAAAtB,EACA,UAAA5D,CACF,IAAM,CAEE,MAAAsG,EAAyBxC,EAAAA,QAAQ,IAC9BgB,EAAgB,MAAM,EAAGuB,CAAY,EAC3C,CAACvB,EAAiBuB,CAAY,CAAC,EAG5BE,EAASzC,EAAAA,QAAQ,IACd+B,GAAoBS,EAAwBR,EAAYC,CAAU,EACxE,CAACO,EAAwBR,EAAYC,CAAU,CAAC,EAE7CS,EAAmBrH,EACvB,mBACA,gBACA,kBAAkBoH,CAAM,GACxB,CACE,CAAC,kBAAkB/C,GAAA,YAAAA,EAAO,IAAI,EAAE,EAAGA,GAAA,YAAAA,EAAO,IAC5C,EACAxD,CACF,EAEMyG,EAAiBjD,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAEvB,OAAA8C,EAAuB,SAAW,EAElCjG,EAAAA,IAAC,MAAI,CAAA,UAAWmG,EACd,SAAAnG,EAAAA,IAAC,MAAI,CAAA,UAAU,uBACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,+BACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wDAAwD,SAEtE,+BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,mCAAmC,SAEhD,yDAAA,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAKFA,EAAA,IAAC,MAAA,CACC,UAAWmG,EACX,MAAOC,EACP,oBAAmBjD,GAAA,YAAAA,EAAO,KAEzB,aAAW,UACVnD,EAAA,IAACwE,EAAA,CACC,gBAAiByB,EACjB,MAAA9C,EACA,YAAa,KAAK,IAAI8C,EAAuB,OAAQ,CAAC,EACtD,gBAAAtB,EACA,aAAAC,EACA,eAAAC,CAAA,CAGF,EAAA9E,EAAA,KAAC,MAAI,CAAA,UAAU,iCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wBACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2DAA2D,SAEzE,uBAAA,EACAD,EAAAA,KAAC,IAAE,CAAA,UAAU,2DACV,SAAA,CAAuBkG,EAAA,OAAO,WAASA,EAAuB,SAAW,EAAI,IAAM,GAAG,QAAA,CACzF,CAAA,CAAA,EACF,EAGAjG,EAAAA,IAAC,OAAI,UAAU,4BACZ,WAAuB,IAAI,CAACkC,EAAgBoC,IAC3CtE,EAAA,IAACkD,EAAA,CAEC,eAAAhB,EACA,MAAAiB,EACA,eAAgBwB,EAChB,WAAY,GACZ,YAAa,EACb,QAASE,EACT,YAAAtB,CAAA,EAPKrB,EAAe,YAAcA,EAAe,OAASoC,CAS7D,CAAA,EACH,EAGCG,EAAgB,OAASuB,GACvBhG,EAAAA,IAAA,MAAA,CAAI,UAAU,gCACb,SAAAD,EAAA,KAAC,IAAE,CAAA,UAAU,mCAAmC,SAAA,CAAA,WACrCiG,EAAa,OAAKvB,EAAgB,OAAO,kBAAA,CAAA,CACpD,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,CAEJ,CAEJ,EAEAsB,GAAa,YAAc,eC/GpB,MAAMM,GAAU,QAGVC,GAAiB,CAC5B,gBAAiB,GACjB,MAAO,GACP,MAAO,CACL,KAAM,QACN,YAAa,SAAA,CAEjB", "x_google_ignoreList": [0, 1, 2, 3]}
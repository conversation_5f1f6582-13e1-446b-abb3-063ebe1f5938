:root{--admesh-background: 255 255 255;--admesh-foreground: 9 9 11;--admesh-card: 255 255 255;--admesh-card-foreground: 9 9 11;--admesh-popover: 255 255 255;--admesh-popover-foreground: 9 9 11;--admesh-primary: 99 102 241;--admesh-primary-foreground: 248 250 252;--admesh-secondary: 244 244 245;--admesh-secondary-foreground: 39 39 42;--admesh-muted: 244 244 245;--admesh-muted-foreground: 113 113 122;--admesh-accent: 244 244 245;--admesh-accent-foreground: 39 39 42;--admesh-destructive: 239 68 68;--admesh-destructive-foreground: 248 250 252;--admesh-border: 228 228 231;--admesh-input: 228 228 231;--admesh-ring: 99 102 241;--admesh-radius: .75rem;--admesh-gradient-primary: linear-gradient(135deg, rgb(99, 102, 241) 0%, rgb(139, 92, 246) 100%);--admesh-gradient-secondary: linear-gradient(135deg, rgb(244, 244, 245) 0%, rgb(250, 250, 250) 100%);--admesh-gradient-card: linear-gradient(145deg, rgba(255, 255, 255, .9) 0%, rgba(255, 255, 255, .7) 100%);--admesh-gradient-glass: linear-gradient(145deg, rgba(255, 255, 255, .1) 0%, rgba(255, 255, 255, .05) 100%);--admesh-shadow-glow: 0 0 0 1px rgb(99 102 241 / .05), 0 1px 3px 0 rgb(99 102 241 / .1), 0 4px 6px 0 rgb(99 102 241 / .1);--admesh-shadow-colored: 0 8px 25px -8px rgb(99 102 241 / .3);--admesh-font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;--admesh-font-mono: "JetBrains Mono", "Fira Code", "Consolas", monospace;--admesh-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);--admesh-gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);--admesh-gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);--admesh-gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);--admesh-gradient-hover: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);--admesh-bg-primary: #ffffff;--admesh-bg-secondary: #f8fafc;--admesh-bg-tertiary: #f1f5f9;--admesh-bg-accent: #f0f9ff;--admesh-bg-glass: rgba(255, 255, 255, .8);--admesh-text-primary: #1e293b;--admesh-text-secondary: #475569;--admesh-text-muted: #64748b;--admesh-text-inverse: #ffffff;--admesh-border-primary: #e2e8f0;--admesh-border-secondary: #cbd5e1;--admesh-border-accent: #c7d2fe;--admesh-spacing-xs: .25rem;--admesh-spacing-sm: .5rem;--admesh-spacing-md: .75rem;--admesh-spacing-lg: 1rem;--admesh-spacing-xl: 1.5rem;--admesh-spacing-2xl: 2rem;--admesh-spacing-3xl: 2.5rem;--admesh-spacing-4xl: 3rem;--admesh-radius-sm: .375rem;--admesh-radius-md: .5rem;--admesh-radius-lg: .75rem;--admesh-radius-xl: 1rem;--admesh-radius-2xl: 1.5rem;--admesh-radius-full: 9999px;--admesh-shadow-xs: 0 1px 2px 0 rgb(0 0 0 / .05);--admesh-shadow-sm: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / .1), 0 8px 10px -6px rgb(0 0 0 / .1);--admesh-shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / .25);--admesh-shadow-glow: 0 0 20px rgb(99 102 241 / .3);--admesh-shadow-colored: 0 8px 25px -8px rgb(99 102 241 / .35);--admesh-font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;--admesh-font-mono: "JetBrains Mono", "Fira Code", "Monaco", "Cascadia Code", monospace;--admesh-font-size-xs: .75rem;--admesh-font-size-sm: .875rem;--admesh-font-size-base: 1rem;--admesh-font-size-lg: 1.125rem;--admesh-font-size-xl: 1.25rem;--admesh-font-size-2xl: 1.5rem;--admesh-font-size-3xl: 1.875rem;--admesh-leading-tight: 1.25;--admesh-leading-snug: 1.375;--admesh-leading-normal: 1.5;--admesh-leading-relaxed: 1.625;--admesh-leading-loose: 2;--admesh-font-light: 300;--admesh-font-normal: 400;--admesh-font-medium: 500;--admesh-font-semibold: 600;--admesh-font-bold: 700;--admesh-font-extrabold: 800;--admesh-transition-fast: .15s ease-in-out;--admesh-transition-normal: .25s ease-in-out;--admesh-transition-slow: .35s ease-in-out;--admesh-transition-bounce: .4s cubic-bezier(.68, -.55, .265, 1.55)}[data-admesh-theme=dark]{--admesh-background: 9 9 11;--admesh-foreground: 250 250 250;--admesh-card: 24 24 27;--admesh-card-foreground: 250 250 250;--admesh-popover: 24 24 27;--admesh-popover-foreground: 250 250 250;--admesh-primary: 129 140 248;--admesh-primary-foreground: 24 24 27;--admesh-secondary: 39 39 42;--admesh-secondary-foreground: 250 250 250;--admesh-muted: 39 39 42;--admesh-muted-foreground: 161 161 170;--admesh-accent: 39 39 42;--admesh-accent-foreground: 250 250 250;--admesh-destructive: 248 113 113;--admesh-destructive-foreground: 24 24 27;--admesh-border: 39 39 42;--admesh-input: 39 39 42;--admesh-ring: 129 140 248;--admesh-gradient-primary: linear-gradient(135deg, rgb(129, 140, 248) 0%, rgb(167, 139, 250) 100%);--admesh-gradient-secondary: linear-gradient(135deg, rgb(39, 39, 42) 0%, rgb(63, 63, 70) 100%);--admesh-gradient-card: linear-gradient(145deg, rgba(24, 24, 27, .9) 0%, rgba(39, 39, 42, .7) 100%);--admesh-gradient-glass: linear-gradient(145deg, rgba(255, 255, 255, .05) 0%, rgba(255, 255, 255, .02) 100%);--admesh-shadow-xs: 0 1px 2px 0 rgb(0 0 0 / .3);--admesh-shadow-sm: 0 1px 3px 0 rgb(0 0 0 / .4), 0 1px 2px -1px rgb(0 0 0 / .4);--admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / .4), 0 2px 4px -2px rgb(0 0 0 / .4);--admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / .4), 0 4px 6px -4px rgb(0 0 0 / .4);--admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / .5), 0 8px 10px -6px rgb(0 0 0 / .5);--admesh-shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / .6);--admesh-shadow-glow: 0 0 0 1px rgb(129 140 248 / .1), 0 1px 3px 0 rgb(129 140 248 / .2), 0 4px 6px 0 rgb(129 140 248 / .2);--admesh-shadow-colored: 0 8px 25px -8px rgb(129 140 248 / .4)}.admesh-component{font-family:var(--admesh-font-family);color:var(--admesh-text-primary);box-sizing:border-box;font-feature-settings:"cv02","cv03","cv04","cv11";font-variant-numeric:tabular-nums;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.admesh-component *,.admesh-component *:before,.admesh-component *:after{box-sizing:inherit}.admesh-button{position:relative;display:inline-flex;align-items:center;justify-content:center;gap:var(--admesh-spacing-sm);padding:var(--admesh-spacing-md) var(--admesh-spacing-xl);border:1px solid transparent;border-radius:var(--admesh-radius-lg);font-size:var(--admesh-font-size-sm);font-weight:var(--admesh-font-semibold);line-height:var(--admesh-leading-tight);text-decoration:none;cursor:pointer;transition:all var(--admesh-transition-normal);-webkit-user-select:none;user-select:none;overflow:hidden;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.admesh-button:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(45deg,transparent 30%,rgba(255,255,255,.1) 50%,transparent 70%);transform:translate(-100%);transition:transform var(--admesh-transition-normal)}.admesh-button:hover:before{transform:translate(100%)}.admesh-button:focus{outline:none;box-shadow:0 0 0 3px var(--admesh-primary-light)}.admesh-button:disabled{opacity:.6;cursor:not-allowed;transform:none!important}.admesh-button:disabled:before{display:none}.admesh-button--primary{background-color:hsl(var(--admesh-primary));color:hsl(var(--admesh-primary-foreground));border:1px solid hsl(var(--admesh-primary))}.admesh-button--primary:hover:not(:disabled){background-color:hsl(var(--admesh-primary) / .9)}.admesh-button--secondary{background-color:hsl(var(--admesh-secondary));color:hsl(var(--admesh-secondary-foreground));border:1px solid hsl(var(--admesh-border))}.admesh-button--secondary:hover:not(:disabled){background-color:hsl(var(--admesh-secondary) / .8)}.admesh-button--outline{border:1px solid hsl(var(--admesh-border));background-color:hsl(var(--admesh-background));color:hsl(var(--admesh-foreground))}.admesh-button--outline:hover:not(:disabled){background-color:hsl(var(--admesh-accent));color:hsl(var(--admesh-accent-foreground))}.admesh-button--ghost{border:1px solid transparent;background-color:transparent;color:hsl(var(--admesh-foreground))}.admesh-button--ghost:hover:not(:disabled){background-color:hsl(var(--admesh-accent));color:hsl(var(--admesh-accent-foreground))}.admesh-card{border-radius:calc(var(--admesh-radius) - 2px);border:1px solid hsl(var(--admesh-border));background-color:hsl(var(--admesh-card));color:hsl(var(--admesh-card-foreground));box-shadow:0 1px 3px #0000001a,0 1px 2px -1px #0000001a;transition:all .2s ease-in-out}.admesh-card:hover{box-shadow:0 10px 15px -3px #0000001a,0 4px 6px -4px #0000001a;transform:translateY(-2px)}.admesh-badge{position:relative;display:inline-flex;align-items:center;gap:.375rem;border-radius:calc(var(--admesh-radius) - 2px);padding:.375rem .875rem;font-size:.875rem;font-weight:600;line-height:1;white-space:nowrap;transition:all .3s cubic-bezier(.4,0,.2,1);border:1px solid transparent;box-shadow:var(--admesh-shadow-xs);overflow:hidden}.admesh-badge:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .6s ease}.admesh-badge:hover:before{left:100%}.admesh-badge--primary{background:var(--admesh-gradient-primary);color:rgb(var(--admesh-primary-foreground));border-color:rgb(var(--admesh-primary));box-shadow:var(--admesh-shadow-colored)}.admesh-badge--secondary{background:var(--admesh-gradient-secondary);color:rgb(var(--admesh-secondary-foreground));border-color:rgb(var(--admesh-border))}.admesh-badge--success{background:linear-gradient(135deg,#22c55e,#15803d);color:#fff;border-color:#22c55e;box-shadow:0 8px 25px -8px #22c55e4d}.admesh-badge--warning{background:linear-gradient(135deg,#fb923c,#f56565);color:#fff;border-color:#fb923c;box-shadow:0 8px 25px -8px #fb923c4d}.admesh-badge--outline{background:var(--admesh-gradient-glass);color:rgb(var(--admesh-foreground));border-color:rgb(var(--admesh-border));-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.admesh-badge--sm{padding:calc(var(--admesh-spacing-xs) * .5) var(--admesh-spacing-sm);font-size:calc(var(--admesh-font-size-xs) * .875)}.admesh-badge--lg{padding:var(--admesh-spacing-sm) var(--admesh-spacing-lg);font-size:var(--admesh-font-size-sm)}.admesh-sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.admesh-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.admesh-text-xs{font-size:var(--admesh-font-size-xs)}.admesh-text-sm{font-size:var(--admesh-font-size-sm)}.admesh-text-base{font-size:var(--admesh-font-size-base)}.admesh-text-lg{font-size:var(--admesh-font-size-lg)}.admesh-text-xl{font-size:var(--admesh-font-size-xl)}.admesh-text-2xl{font-size:var(--admesh-font-size-2xl)}.admesh-font-normal{font-weight:var(--admesh-font-normal)}.admesh-font-medium{font-weight:var(--admesh-font-medium)}.admesh-font-semibold{font-weight:var(--admesh-font-semibold)}.admesh-font-bold{font-weight:var(--admesh-font-bold)}.admesh-text-primary{color:var(--admesh-text-primary)}.admesh-text-secondary{color:var(--admesh-text-secondary)}.admesh-text-muted{color:var(--admesh-text-muted)}@media (max-width: 640px){.admesh-hidden-mobile{display:none}}@media (min-width: 641px){.admesh-hidden-desktop{display:none}}

:root{--admesh-primary: #6366f1;--admesh-primary-hover: #4f46e5;--admesh-primary-light: #a5b4fc;--admesh-primary-dark: #3730a3;--admesh-secondary: #64748b;--admesh-success: #10b981;--admesh-success-light: #6ee7b7;--admesh-warning: #f59e0b;--admesh-warning-light: #fbbf24;--admesh-error: #ef4444;--admesh-error-light: #fca5a5;--admesh-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);--admesh-gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);--admesh-gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);--admesh-gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);--admesh-gradient-hover: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);--admesh-bg-primary: #ffffff;--admesh-bg-secondary: #f8fafc;--admesh-bg-tertiary: #f1f5f9;--admesh-bg-accent: #f0f9ff;--admesh-bg-glass: rgba(255, 255, 255, .8);--admesh-text-primary: #1e293b;--admesh-text-secondary: #475569;--admesh-text-muted: #64748b;--admesh-text-inverse: #ffffff;--admesh-border-primary: #e2e8f0;--admesh-border-secondary: #cbd5e1;--admesh-border-accent: #c7d2fe;--admesh-spacing-xs: .25rem;--admesh-spacing-sm: .5rem;--admesh-spacing-md: .75rem;--admesh-spacing-lg: 1rem;--admesh-spacing-xl: 1.5rem;--admesh-spacing-2xl: 2rem;--admesh-spacing-3xl: 2.5rem;--admesh-spacing-4xl: 3rem;--admesh-radius-sm: .375rem;--admesh-radius-md: .5rem;--admesh-radius-lg: .75rem;--admesh-radius-xl: 1rem;--admesh-radius-2xl: 1.5rem;--admesh-radius-full: 9999px;--admesh-shadow-xs: 0 1px 2px 0 rgb(0 0 0 / .05);--admesh-shadow-sm: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / .1), 0 8px 10px -6px rgb(0 0 0 / .1);--admesh-shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / .25);--admesh-shadow-glow: 0 0 20px rgb(99 102 241 / .3);--admesh-shadow-colored: 0 8px 25px -8px rgb(99 102 241 / .35);--admesh-font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;--admesh-font-mono: "JetBrains Mono", "Fira Code", "Monaco", "Cascadia Code", monospace;--admesh-font-size-xs: .75rem;--admesh-font-size-sm: .875rem;--admesh-font-size-base: 1rem;--admesh-font-size-lg: 1.125rem;--admesh-font-size-xl: 1.25rem;--admesh-font-size-2xl: 1.5rem;--admesh-font-size-3xl: 1.875rem;--admesh-leading-tight: 1.25;--admesh-leading-snug: 1.375;--admesh-leading-normal: 1.5;--admesh-leading-relaxed: 1.625;--admesh-leading-loose: 2;--admesh-font-light: 300;--admesh-font-normal: 400;--admesh-font-medium: 500;--admesh-font-semibold: 600;--admesh-font-bold: 700;--admesh-font-extrabold: 800;--admesh-transition-fast: .15s ease-in-out;--admesh-transition-normal: .25s ease-in-out;--admesh-transition-slow: .35s ease-in-out;--admesh-transition-bounce: .4s cubic-bezier(.68, -.55, .265, 1.55)}[data-admesh-theme=dark]{--admesh-primary: #818cf8;--admesh-primary-hover: #6366f1;--admesh-primary-light: #c7d2fe;--admesh-primary-dark: #4338ca;--admesh-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);--admesh-gradient-card: linear-gradient(145deg, #1e293b 0%, #334155 100%);--admesh-gradient-hover: linear-gradient(145deg, #334155 0%, #475569 100%);--admesh-bg-primary: #0f172a;--admesh-bg-secondary: #1e293b;--admesh-bg-tertiary: #334155;--admesh-bg-accent: #1e1b4b;--admesh-bg-glass: rgba(30, 41, 59, .8);--admesh-text-primary: #f1f5f9;--admesh-text-secondary: #cbd5e1;--admesh-text-muted: #94a3b8;--admesh-text-inverse: #0f172a;--admesh-border-primary: #334155;--admesh-border-secondary: #475569;--admesh-border-accent: #4338ca;--admesh-shadow-sm: 0 1px 3px 0 rgb(0 0 0 / .3), 0 1px 2px -1px rgb(0 0 0 / .3);--admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / .3), 0 2px 4px -2px rgb(0 0 0 / .3);--admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / .3), 0 4px 6px -4px rgb(0 0 0 / .3);--admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / .4), 0 8px 10px -6px rgb(0 0 0 / .4);--admesh-shadow-glow: 0 0 20px rgb(129 140 248 / .4);--admesh-shadow-colored: 0 8px 25px -8px rgb(129 140 248 / .4)}.admesh-component{font-family:var(--admesh-font-family);color:var(--admesh-text-primary);box-sizing:border-box;font-feature-settings:"cv02","cv03","cv04","cv11";font-variant-numeric:tabular-nums;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.admesh-component *,.admesh-component *:before,.admesh-component *:after{box-sizing:inherit}.admesh-button{position:relative;display:inline-flex;align-items:center;justify-content:center;gap:var(--admesh-spacing-sm);padding:var(--admesh-spacing-md) var(--admesh-spacing-xl);border:1px solid transparent;border-radius:var(--admesh-radius-lg);font-size:var(--admesh-font-size-sm);font-weight:var(--admesh-font-semibold);line-height:var(--admesh-leading-tight);text-decoration:none;cursor:pointer;transition:all var(--admesh-transition-normal);-webkit-user-select:none;user-select:none;overflow:hidden;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.admesh-button:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(45deg,transparent 30%,rgba(255,255,255,.1) 50%,transparent 70%);transform:translate(-100%);transition:transform var(--admesh-transition-normal)}.admesh-button:hover:before{transform:translate(100%)}.admesh-button:focus{outline:none;box-shadow:0 0 0 3px var(--admesh-primary-light)}.admesh-button:disabled{opacity:.6;cursor:not-allowed;transform:none!important}.admesh-button:disabled:before{display:none}.admesh-button--primary{background:var(--admesh-gradient-primary);color:var(--admesh-text-inverse);box-shadow:var(--admesh-shadow-colored);border:1px solid var(--admesh-primary-light)}.admesh-button--primary:hover:not(:disabled){transform:translateY(-2px);box-shadow:var(--admesh-shadow-xl),var(--admesh-shadow-glow)}.admesh-button--primary:active:not(:disabled){transform:translateY(0)}.admesh-button--secondary{background:var(--admesh-bg-glass);color:var(--admesh-text-primary);border:1px solid var(--admesh-border-accent);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.admesh-button--secondary:hover:not(:disabled){background:var(--admesh-gradient-hover);border-color:var(--admesh-primary);transform:translateY(-1px);box-shadow:var(--admesh-shadow-lg)}.admesh-card{position:relative;background:var(--admesh-gradient-card);border:1px solid var(--admesh-border-primary);border-radius:var(--admesh-radius-xl);box-shadow:var(--admesh-shadow-md);overflow:hidden;transition:all var(--admesh-transition-normal);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.admesh-card:before{content:"";position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent,var(--admesh-primary-light),transparent);opacity:0;transition:opacity var(--admesh-transition-normal)}.admesh-card:hover{transform:translateY(-4px);box-shadow:var(--admesh-shadow-xl);border-color:var(--admesh-border-accent)}.admesh-card:hover:before{opacity:1}.admesh-badge{position:relative;display:inline-flex;align-items:center;gap:var(--admesh-spacing-xs);padding:var(--admesh-spacing-xs) var(--admesh-spacing-md);border-radius:var(--admesh-radius-full);font-size:var(--admesh-font-size-xs);font-weight:var(--admesh-font-semibold);line-height:var(--admesh-leading-tight);white-space:nowrap;border:1px solid transparent;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);transition:all var(--admesh-transition-fast)}.admesh-badge:before{content:"";position:absolute;top:0;right:0;bottom:0;left:0;border-radius:inherit;padding:1px;background:linear-gradient(135deg,#fff3,#ffffff0d);mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);mask-composite:xor;-webkit-mask-composite:xor}.admesh-badge--primary{background:var(--admesh-gradient-primary);color:var(--admesh-text-inverse);box-shadow:var(--admesh-shadow-sm),0 0 10px #6366f14d}.admesh-badge--secondary{background:var(--admesh-bg-glass);color:var(--admesh-text-secondary);border-color:var(--admesh-border-primary)}.admesh-badge--success{background:var(--admesh-gradient-success);color:var(--admesh-text-inverse);box-shadow:var(--admesh-shadow-sm),0 0 10px #10b9814d}.admesh-badge--warning{background:var(--admesh-gradient-warning);color:var(--admesh-text-inverse);box-shadow:var(--admesh-shadow-sm),0 0 10px #f59e0b4d}.admesh-badge--sm{padding:calc(var(--admesh-spacing-xs) * .5) var(--admesh-spacing-sm);font-size:calc(var(--admesh-font-size-xs) * .875)}.admesh-badge--lg{padding:var(--admesh-spacing-sm) var(--admesh-spacing-lg);font-size:var(--admesh-font-size-sm)}.admesh-sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.admesh-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.admesh-text-xs{font-size:var(--admesh-font-size-xs)}.admesh-text-sm{font-size:var(--admesh-font-size-sm)}.admesh-text-base{font-size:var(--admesh-font-size-base)}.admesh-text-lg{font-size:var(--admesh-font-size-lg)}.admesh-text-xl{font-size:var(--admesh-font-size-xl)}.admesh-text-2xl{font-size:var(--admesh-font-size-2xl)}.admesh-font-normal{font-weight:var(--admesh-font-normal)}.admesh-font-medium{font-weight:var(--admesh-font-medium)}.admesh-font-semibold{font-weight:var(--admesh-font-semibold)}.admesh-font-bold{font-weight:var(--admesh-font-bold)}.admesh-text-primary{color:var(--admesh-text-primary)}.admesh-text-secondary{color:var(--admesh-text-secondary)}.admesh-text-muted{color:var(--admesh-text-muted)}@media (max-width: 640px){.admesh-hidden-mobile{display:none}}@media (min-width: 641px){.admesh-hidden-desktop{display:none}}

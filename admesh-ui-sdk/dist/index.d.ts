import { default as default_2 } from 'react';

export declare const AdMeshBadge: default_2.FC<AdMeshBadgeProps>;

export declare interface AdMeshBadgeProps {
    type: BadgeType;
    variant?: BadgeVariant;
    size?: BadgeSize;
    className?: string;
}

export declare const AdMeshCompareTable: default_2.FC<AdMeshCompareTableProps>;

export declare interface AdMeshCompareTableProps {
    recommendations: AdMeshRecommendation[];
    theme?: AdMeshTheme;
    maxProducts?: number;
    showMatchScores?: boolean;
    showFeatures?: boolean;
    onProductClick?: (adId: string, admeshLink: string) => void;
    className?: string;
}

export declare interface AdMeshConfig {
    apiBaseUrl?: string;
    trackingEnabled?: boolean;
    defaultTheme?: AdMeshTheme;
    debug?: boolean;
}

export declare const AdMeshLayout: default_2.FC<AdMeshLayoutProps>;

export declare interface AdMeshLayoutProps {
    recommendations: AdMeshRecommendation[];
    intentType?: IntentType;
    theme?: AdMeshTheme;
    maxDisplayed?: number;
    showMatchScores?: boolean;
    showFeatures?: boolean;
    autoLayout?: boolean;
    onProductClick?: (adId: string, admeshLink: string) => void;
    onTrackView?: (data: TrackingData) => void;
    className?: string;
}

export declare const AdMeshLinkTracker: default_2.FC<AdMeshLinkTrackerProps>;

export declare interface AdMeshLinkTrackerProps {
    adId: string;
    admeshLink: string;
    productId?: string;
    children: React.ReactNode;
    onClick?: () => void;
    trackingData?: Record<string, any>;
    className?: string;
}

export declare const AdMeshProductCard: default_2.FC<AdMeshProductCardProps>;

export declare interface AdMeshProductCardProps {
    recommendation: AdMeshRecommendation;
    theme?: AdMeshTheme;
    showMatchScore?: boolean;
    showBadges?: boolean;
    maxKeywords?: number;
    onClick?: (adId: string, admeshLink: string) => void;
    onTrackView?: (data: TrackingData) => void;
    className?: string;
}

export declare interface AdMeshRecommendation {
    title: string;
    reason: string;
    intent_match_score: number;
    admesh_link: string;
    ad_id: string;
    product_id: string;
    features?: string[];
    has_free_tier?: boolean;
    integrations?: string[];
    pricing?: string;
    trial_days?: number;
    url?: string;
    reviews_summary?: string;
    reward_note?: string | null;
    security?: string[];
    slug?: string;
    support?: string[];
    redirect_url?: string;
    keywords?: string[];
    badges?: string[];
}

export declare interface AdMeshTheme {
    mode: 'light' | 'dark';
    accentColor?: string;
    borderRadius?: string;
    fontFamily?: string;
}

export declare interface AgentRecommendationResponse {
    session_id: string;
    intent: {
        categories?: string[];
        goal?: string;
        llm_intent_confidence_score?: number;
        known_mentions?: string[];
        intent_type?: string;
        intent_group?: string;
        keywords?: string[];
    };
    response: {
        summary?: string;
        recommendations: AdMeshRecommendation[];
        followup_suggestions?: {
            label: string;
            query: string;
            product_mentions: string[];
            admesh_links: Record<string, string>;
            session_id: string;
        }[];
    };
    tokens_used: number;
    model_used: string;
    recommendation_id?: string;
    end_of_session?: boolean;
}

export declare type BadgeSize = 'sm' | 'md' | 'lg';

export declare type BadgeType = 'Top Match' | 'Free Tier' | 'AI Powered' | 'Popular' | 'New' | 'Trial Available';

export declare type BadgeVariant = 'primary' | 'secondary' | 'success' | 'warning';

export declare const buildAdMeshLink: (baseLink: string, adId: string, additionalParams?: Record<string, string>) => string;

export declare const DEFAULT_CONFIG: {
    trackingEnabled: boolean;
    debug: boolean;
    theme: {
        mode: "light";
        accentColor: string;
    };
};

export declare const extractTrackingData: (recommendation: {
    ad_id: string;
    admesh_link: string;
    product_id: string;
}, additionalData?: Partial<TrackingData>) => TrackingData;

export declare type IntentType = 'compare_products' | 'best_for_use_case' | 'trial_demo' | 'budget_conscious';

export declare const setAdMeshTrackerConfig: (config: Partial<TrackingConfig>) => void;

declare interface TrackingConfig {
    apiBaseUrl?: string;
    enabled?: boolean;
    debug?: boolean;
    retryAttempts?: number;
    retryDelay?: number;
}

export declare interface TrackingData {
    adId: string;
    admeshLink: string;
    userId?: string;
    sessionId?: string;
    productId?: string;
    revenue?: number;
    conversionType?: string;
    metadata?: Record<string, any>;
}

export declare const useAdMeshTracker: (config?: Partial<TrackingConfig>) => UseAdMeshTrackerReturn;

export declare interface UseAdMeshTrackerReturn {
    trackClick: (data: TrackingData) => Promise<void>;
    trackConversion: (data: TrackingData) => Promise<void>;
    trackView: (data: TrackingData) => Promise<void>;
    isTracking: boolean;
    error: string | null;
}

export declare const VERSION = "0.1.0";

export { }

import React, { useState } from 'react';
import {
  AdMeshLayout,
  AdMeshProductCard,
  AdMeshCompareTable,
  AdMeshBadge,
  setAdMeshTrackerConfig,
  type AdMeshRecommendation,
  type AdMeshTheme
} from '../src';
import '../src/styles/index.css';

// Configure tracking for demo
setAdMeshTrackerConfig({
  apiBaseUrl: 'https://api.useadmesh.com',
  enabled: true,
  debug: true
});

// Sample recommendations data
const sampleRecommendations: AdMeshRecommendation[] = [
  {
    title: "HubSpot CRM",
    reason: "Perfect for remote teams with excellent collaboration features and free tier availability",
    intent_match_score: 0.92,
    admesh_link: "https://useadmesh.com/track?ad_id=hubspot-123&redirect=https://hubspot.com",
    ad_id: "hubspot-123",
    product_id: "hubspot-crm",
    features: ["Contact Management", "Email Marketing", "Sales Pipeline", "Reporting", "Mobile App"],
    has_free_tier: true,
    integrations: ["Gmail", "Outlook", "Slack", "Zoom"],
    pricing: "Free - $1,200/month",
    trial_days: 14,
    keywords: ["CRM", "Sales", "Marketing", "Customer Management"]
  },
  {
    title: "Salesforce CRM",
    reason: "Enterprise-grade CRM with advanced customization and automation capabilities",
    intent_match_score: 0.88,
    admesh_link: "https://useadmesh.com/track?ad_id=salesforce-456&redirect=https://salesforce.com",
    ad_id: "salesforce-456",
    product_id: "salesforce-crm",
    features: ["Advanced Analytics", "Custom Objects", "Workflow Automation", "AppExchange"],
    has_free_tier: false,
    integrations: ["Microsoft 365", "Google Workspace", "Slack"],
    pricing: "$25 - $300/user/month",
    trial_days: 30,
    keywords: ["CRM", "Enterprise", "Automation"]
  },
  {
    title: "OpenAI GPT-4 API",
    reason: "Leading AI language model with excellent performance for content generation and analysis",
    intent_match_score: 0.85,
    admesh_link: "https://useadmesh.com/track?ad_id=openai-789&redirect=https://openai.com",
    ad_id: "openai-789",
    product_id: "openai-gpt4",
    features: ["Natural Language Processing", "Code Generation", "Content Creation", "API Access"],
    has_free_tier: false,
    integrations: ["REST API", "Python SDK", "Node.js SDK"],
    pricing: "$0.03/1K tokens",
    trial_days: 0,
    keywords: ["AI", "Machine Learning", "Natural Language", "API"]
  }
];

function App() {
  const [theme, setTheme] = useState<AdMeshTheme>({ mode: 'light' });
  const [layoutType, setLayoutType] = useState<'auto' | 'cards' | 'compare'>('auto');

  const handleProductClick = (adId: string, admeshLink: string) => {
    console.log('Product clicked:', { adId, admeshLink });
    // In a real app, this would navigate to the product
    alert(`Clicked product with ad_id: ${adId}`);
  };

  const toggleTheme = () => {
    setTheme(prev => ({ 
      ...prev, 
      mode: prev.mode === 'light' ? 'dark' : 'light' 
    }));
  };

  const setAccentColor = (color: string) => {
    setTheme(prev => ({ ...prev, accentColor: color }));
  };

  return (
    <div 
      style={{ 
        minHeight: '100vh', 
        padding: '2rem',
        backgroundColor: theme.mode === 'dark' ? '#0f172a' : '#ffffff',
        color: theme.mode === 'dark' ? '#f8fafc' : '#0f172a'
      }}
      data-admesh-theme={theme.mode}
    >
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <header style={{ marginBottom: '2rem', textAlign: 'center' }}>
          <h1>AdMesh UI SDK Demo</h1>
          <p>Interactive demonstration of AdMesh UI components</p>
          
          {/* Theme Controls */}
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', marginTop: '1rem', flexWrap: 'wrap' }}>
            <button onClick={toggleTheme}>
              Toggle {theme.mode === 'light' ? 'Dark' : 'Light'} Mode
            </button>
            
            <button onClick={() => setAccentColor('#2563eb')}>Blue Accent</button>
            <button onClick={() => setAccentColor('#10b981')}>Green Accent</button>
            <button onClick={() => setAccentColor('#8b5cf6')}>Purple Accent</button>
            <button onClick={() => setAccentColor('#f59e0b')}>Orange Accent</button>
            
            <select 
              value={layoutType} 
              onChange={(e) => setLayoutType(e.target.value as any)}
              style={{ padding: '0.5rem' }}
            >
              <option value="auto">Auto Layout</option>
              <option value="cards">Cards Layout</option>
              <option value="compare">Compare Layout</option>
            </select>
          </div>
        </header>

        {/* Badge Examples */}
        <section style={{ marginBottom: '3rem' }}>
          <h2>Badges</h2>
          <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
            <AdMeshBadge type="Top Match" variant="primary" />
            <AdMeshBadge type="Free Tier" variant="success" />
            <AdMeshBadge type="AI Powered" variant="secondary" />
            <AdMeshBadge type="Popular" variant="warning" />
            <AdMeshBadge type="Trial Available" size="lg" />
          </div>
        </section>

        {/* Main Layout Demo */}
        <section style={{ marginBottom: '3rem' }}>
          <h2>Layout Component</h2>
          {layoutType === 'auto' && (
            <AdMeshLayout
              recommendations={sampleRecommendations}
              theme={theme}
              autoLayout={true}
              showMatchScores={true}
              showFeatures={true}
              onProductClick={handleProductClick}
            />
          )}
          
          {layoutType === 'cards' && (
            <AdMeshLayout
              recommendations={sampleRecommendations}
              theme={theme}
              intentType="best_for_use_case"
              autoLayout={false}
              showMatchScores={true}
              showFeatures={true}
              onProductClick={handleProductClick}
            />
          )}
          
          {layoutType === 'compare' && (
            <AdMeshCompareTable
              recommendations={sampleRecommendations}
              theme={theme}
              showMatchScores={true}
              showFeatures={true}
              onProductClick={handleProductClick}
            />
          )}
        </section>

        {/* Individual Card Demo */}
        <section style={{ marginBottom: '3rem' }}>
          <h2>Individual Product Card</h2>
          <div style={{ maxWidth: '400px' }}>
            <AdMeshProductCard
              recommendation={sampleRecommendations[0]}
              theme={theme}
              showMatchScore={true}
              showBadges={true}
              maxKeywords={4}
              onClick={handleProductClick}
            />
          </div>
        </section>

        {/* Footer */}
        <footer style={{ textAlign: 'center', marginTop: '4rem', padding: '2rem', borderTop: '1px solid #e2e8f0' }}>
          <p>
            Built with <strong>AdMesh UI SDK</strong> - 
            <a href="https://github.com/GouniManikumar12/admesh-ui-sdk" target="_blank" rel="noopener noreferrer">
              View on GitHub
            </a>
          </p>
        </footer>
      </div>
    </div>
  );
}

export default App;

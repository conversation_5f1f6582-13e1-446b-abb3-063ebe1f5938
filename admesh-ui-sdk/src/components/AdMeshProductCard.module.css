/* AdMeshProductCard - Modern Component Styles */

.admesh-product-card {
  position: relative;
  transition: all var(--admesh-transition-normal);
  cursor: pointer;
  overflow: hidden;
}

.admesh-product-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--admesh-transition-normal);
  pointer-events: none;
}

.admesh-product-card:hover::after {
  opacity: 1;
}

.admesh-product-card__container {
  position: relative;
  padding: var(--admesh-spacing-2xl);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--admesh-spacing-xl);
  z-index: 1;
}

/* Header */
.admesh-product-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--admesh-spacing-lg);
  min-height: 32px;
  margin-bottom: var(--admesh-spacing-sm);
}

.admesh-product-card__badges {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admesh-spacing-sm);
  flex: 1;
}

.admesh-product-card__match-score {
  position: relative;
  flex-shrink: 0;
  padding: var(--admesh-spacing-sm) var(--admesh-spacing-md);
  background: var(--admesh-bg-glass);
  border: 1px solid var(--admesh-border-accent);
  border-radius: var(--admesh-radius-full);
  font-weight: var(--admesh-font-semibold);
  font-size: var(--admesh-font-size-xs);
  backdrop-filter: blur(10px);
  box-shadow: var(--admesh-shadow-sm);
}

.admesh-product-card__match-score::before {
  content: '';
  position: absolute;
  top: 50%;
  left: var(--admesh-spacing-xs);
  width: 6px;
  height: 6px;
  background: var(--admesh-success);
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 6px var(--admesh-success);
}

/* Content */
.admesh-product-card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--admesh-spacing-lg);
}

.admesh-product-card__title {
  margin: 0;
  font-size: var(--admesh-font-size-xl);
  font-weight: var(--admesh-font-bold);
  line-height: var(--admesh-leading-tight);
  color: var(--admesh-text-primary);
  background: linear-gradient(135deg, var(--admesh-text-primary) 0%, var(--admesh-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admesh-product-card__reason {
  margin: 0;
  font-size: var(--admesh-font-size-base);
  line-height: var(--admesh-leading-relaxed);
  color: var(--admesh-text-secondary);
  font-weight: var(--admesh-font-normal);
}

/* Keywords */
.admesh-product-card__keywords {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admesh-spacing-xs);
  align-items: center;
}

.admesh-product-card__keyword {
  /* Inherits from admesh-badge styles */
}

.admesh-product-card__keyword-more {
  margin-left: var(--admesh-spacing-xs);
  font-style: italic;
}

/* Meta information */
.admesh-product-card__meta {
  display: flex;
  flex-direction: column;
  gap: var(--admesh-spacing-xs);
}

.admesh-product-card__pricing {
  margin: 0;
}

.admesh-product-card__trial {
  margin: 0;
  font-style: italic;
}

/* Footer */
.admesh-product-card__footer {
  margin-top: auto;
  padding-top: var(--admesh-spacing-xl);
  border-top: 1px solid var(--admesh-border-primary);
}

.admesh-product-card__cta {
  position: relative;
  width: 100%;
  justify-content: center;
  font-weight: var(--admesh-font-bold);
  font-size: var(--admesh-font-size-base);
  padding: var(--admesh-spacing-lg) var(--admesh-spacing-xl);
  background: var(--admesh-gradient-primary);
  border: none;
  border-radius: var(--admesh-radius-xl);
  color: var(--admesh-text-inverse);
  box-shadow: var(--admesh-shadow-colored);
  overflow: hidden;
}

.admesh-product-card__cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--admesh-transition-normal);
}

.admesh-product-card__cta:hover::before {
  left: 100%;
}

.admesh-product-card__cta:hover {
  transform: translateY(-2px);
  box-shadow: var(--admesh-shadow-xl), var(--admesh-shadow-glow);
}

/* Badge icon spacing */
.admesh-badge__icon {
  margin-right: var(--admesh-spacing-xs);
}

.admesh-badge__text {
  /* No additional styles needed */
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .admesh-product-card__container {
    padding: var(--admesh-spacing-lg);
    gap: var(--admesh-spacing-md);
  }
  
  .admesh-product-card__header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admesh-spacing-sm);
  }
  
  .admesh-product-card__match-score {
    align-self: flex-end;
  }
  
  .admesh-product-card__title {
    font-size: var(--admesh-font-size-base);
  }
}

/* Dark theme adjustments */
[data-admesh-theme="dark"] .admesh-product-card__match-score {
  background-color: var(--admesh-bg-tertiary);
  color: var(--admesh-text-secondary);
}

/* Focus styles for accessibility */
.admesh-product-card:focus-within {
  outline: 2px solid var(--admesh-primary);
  outline-offset: 2px;
}

.admesh-product-card__cta:focus {
  outline: 2px solid var(--admesh-primary);
  outline-offset: 2px;
}

/* Loading state (for future use) */
.admesh-product-card--loading {
  opacity: 0.7;
  pointer-events: none;
}

.admesh-product-card--loading .admesh-product-card__title,
.admesh-product-card--loading .admesh-product-card__reason {
  background: linear-gradient(90deg, var(--admesh-bg-tertiary) 25%, var(--admesh-bg-secondary) 50%, var(--admesh-bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--admesh-radius-sm);
  color: transparent;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* AdMeshProductCard - shadcn/ui Inspired Styles */

.admesh-product-card {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  height: 100%;
}

.admesh-product-card__container {
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Header */
.admesh-product-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.admesh-product-card__badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
}

.admesh-product-card__match-score {
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: hsl(var(--admesh-muted));
  color: hsl(var(--admesh-muted-foreground));
  border-radius: calc(var(--admesh-radius) - 2px);
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid hsl(var(--admesh-border));
}

.admesh-product-card__match-score::before {
  content: '';
  width: 6px;
  height: 6px;
  background-color: hsl(142.1 76.2% 36.3%);
  border-radius: 50%;
}

/* Content */
.admesh-product-card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.admesh-product-card__title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.25;
  color: hsl(var(--admesh-foreground));
  letter-spacing: -0.025em;
}

.admesh-product-card__reason {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
  color: hsl(var(--admesh-muted-foreground));
}

/* Keywords */
.admesh-product-card__keywords {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admesh-spacing-xs);
  align-items: center;
}

.admesh-product-card__keyword {
  /* Inherits from admesh-badge styles */
}

.admesh-product-card__keyword-more {
  margin-left: var(--admesh-spacing-xs);
  font-style: italic;
}

/* Meta information */
.admesh-product-card__meta {
  display: flex;
  flex-direction: column;
  gap: var(--admesh-spacing-xs);
}

.admesh-product-card__pricing {
  margin: 0;
}

.admesh-product-card__trial {
  margin: 0;
  font-style: italic;
}

/* Footer */
.admesh-product-card__footer {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid hsl(var(--admesh-border));
}

.admesh-product-card__cta {
  width: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: calc(var(--admesh-radius) - 2px);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  padding: 0.5rem 1rem;
  background-color: hsl(var(--admesh-primary));
  color: hsl(var(--admesh-primary-foreground));
  border: 1px solid hsl(var(--admesh-primary));
  cursor: pointer;
}

.admesh-product-card__cta:hover {
  background-color: hsl(var(--admesh-primary) / 0.9);
}

.admesh-product-card__cta:focus-visible {
  outline: 2px solid hsl(var(--admesh-ring));
  outline-offset: 2px;
}

/* Badge icon spacing */
.admesh-badge__icon {
  margin-right: var(--admesh-spacing-xs);
}

.admesh-badge__text {
  /* No additional styles needed */
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .admesh-product-card__container {
    padding: var(--admesh-spacing-lg);
    gap: var(--admesh-spacing-md);
  }
  
  .admesh-product-card__header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admesh-spacing-sm);
  }
  
  .admesh-product-card__match-score {
    align-self: flex-end;
  }
  
  .admesh-product-card__title {
    font-size: var(--admesh-font-size-base);
  }
}

/* Dark theme adjustments */
[data-admesh-theme="dark"] .admesh-product-card__match-score {
  background-color: var(--admesh-bg-tertiary);
  color: var(--admesh-text-secondary);
}

/* Focus styles for accessibility */
.admesh-product-card:focus-within {
  outline: 2px solid var(--admesh-primary);
  outline-offset: 2px;
}

.admesh-product-card__cta:focus {
  outline: 2px solid var(--admesh-primary);
  outline-offset: 2px;
}

/* Loading state (for future use) */
.admesh-product-card--loading {
  opacity: 0.7;
  pointer-events: none;
}

.admesh-product-card--loading .admesh-product-card__title,
.admesh-product-card--loading .admesh-product-card__reason {
  background: linear-gradient(90deg, var(--admesh-bg-tertiary) 25%, var(--admesh-bg-secondary) 50%, var(--admesh-bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--admesh-radius-sm);
  color: transparent;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

import React, { useMemo } from 'react';
import classNames from 'classnames';
import type { AdMeshProductCardProps, BadgeType } from '../types/index';
import { AdMeshBadge } from './AdMeshBadge';
import { AdMeshLinkTracker } from './AdMeshLinkTracker';
import styles from './AdMeshProductCard.module.css';

export const AdMeshProductCard: React.FC<AdMeshProductCardProps> = ({
  recommendation,
  theme,
  showMatchScore = true,
  showBadges = true,
  maxKeywords = 3,
  onClick,
  onTrackView,
  className
}) => {
  // Generate badges based on recommendation data
  const badges = useMemo((): BadgeType[] => {
    const generatedBadges: BadgeType[] = [];
    
    // Add Top Match badge for high match scores
    if (recommendation.intent_match_score >= 0.8) {
      generatedBadges.push('Top Match');
    }
    
    // Add Free Tier badge
    if (recommendation.has_free_tier) {
      generatedBadges.push('Free Tier');
    }
    
    // Add Trial Available badge
    if (recommendation.trial_days && recommendation.trial_days > 0) {
      generatedBadges.push('Trial Available');
    }
    
    // Add AI Powered badge (check if AI-related keywords exist)
    const aiKeywords = ['ai', 'artificial intelligence', 'machine learning', 'ml', 'automation'];
    const hasAIKeywords = recommendation.keywords?.some(keyword => 
      aiKeywords.some(ai => keyword.toLowerCase().includes(ai))
    ) || recommendation.title.toLowerCase().includes('ai');
    
    if (hasAIKeywords) {
      generatedBadges.push('AI Powered');
    }
    
    return generatedBadges;
  }, [recommendation]);

  // Format match score as percentage
  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);

  // Limit keywords display
  const displayKeywords = recommendation.keywords?.slice(0, maxKeywords) || [];
  const hasMoreKeywords = (recommendation.keywords?.length || 0) > maxKeywords;

  const cardClasses = classNames(
    'admesh-component',
    'admesh-card',
    styles['admesh-product-card'],
    {
      [`admesh-product-card--${theme?.mode}`]: theme?.mode,
    },
    className
  );

  const cardStyle = theme?.accentColor ? {
    '--admesh-primary': theme.accentColor,
    '--admesh-primary-hover': theme.accentColor + 'dd', // Add some transparency for hover
  } as React.CSSProperties : undefined;

  return (
    <AdMeshLinkTracker
      adId={recommendation.ad_id}
      admeshLink={recommendation.admesh_link}
      productId={recommendation.product_id}
      onClick={() => onClick?.(recommendation.ad_id, recommendation.admesh_link)}
      trackingData={{ 
        title: recommendation.title,
        matchScore: recommendation.intent_match_score 
      }}
      className={cardClasses}
    >
      <div
        className={styles['admesh-product-card__container']}
        style={cardStyle}
        data-admesh-theme={theme?.mode}
      >
        {/* Header with badges and match score */}
        <div className={styles['admesh-product-card__header']}>
          {showBadges && badges.length > 0 && (
            <div className={styles['admesh-product-card__badges']}>
              {badges.map((badge, index) => (
                <AdMeshBadge key={`${badge}-${index}`} type={badge} size="sm" />
              ))}
            </div>
          )}

          {showMatchScore && (
            <div className={styles['admesh-product-card__match-score']}>
              <span className="admesh-text-xs admesh-text-muted">
                {matchScorePercentage}% match
              </span>
            </div>
          )}
        </div>

        {/* Main content */}
        <div className={styles['admesh-product-card__content']}>
          <h3 className={styles['admesh-product-card__title']}>
            {recommendation.title}
          </h3>

          <p className={styles['admesh-product-card__reason']}>
            {recommendation.reason}
          </p>

          {/* Keywords */}
          {displayKeywords.length > 0 && (
            <div className={styles['admesh-product-card__keywords']}>
              {displayKeywords.map((keyword, index) => (
                <span
                  key={index}
                  className="admesh-badge admesh-badge--secondary admesh-badge--sm"
                >
                  {keyword}
                </span>
              ))}
              {hasMoreKeywords && (
                <span className={styles['admesh-product-card__keyword-more']}>
                  +{(recommendation.keywords?.length || 0) - maxKeywords} more
                </span>
              )}
            </div>
          )}

          {/* Additional info */}
          <div className={styles['admesh-product-card__meta']}>
            {recommendation.pricing && (
              <div className={styles['admesh-product-card__pricing']}>
                <span className="admesh-text-muted">Pricing: </span>
                <span className="admesh-font-medium">{recommendation.pricing}</span>
              </div>
            )}

            {recommendation.trial_days && recommendation.trial_days > 0 && (
              <div className={styles['admesh-product-card__trial']}>
                {recommendation.trial_days}-day free trial
              </div>
            )}
          </div>
        </div>

        {/* Footer with CTA */}
        <div className={styles['admesh-product-card__footer']}>
          <button className={classNames('admesh-button', 'admesh-button--primary', styles['admesh-product-card__cta'])}>
            Visit Offer
            <span className="admesh-sr-only">
              for {recommendation.title}
            </span>
          </button>
        </div>
      </div>
    </AdMeshLinkTracker>
  );
};

AdMeshProductCard.displayName = 'AdMeshProductCard';

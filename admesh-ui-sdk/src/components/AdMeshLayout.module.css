/* AdMeshLayout - Modern Component Styles */

.admesh-layout {
  width: 100%;
  position: relative;
}

/* Modern empty state */
.admesh-layout__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: var(--admesh-spacing-4xl);
  background: var(--admesh-gradient-card);
  border: 2px dashed var(--admesh-border-accent);
  border-radius: var(--admesh-radius-2xl);
  position: relative;
  overflow: hidden;
}

.admesh-layout__empty::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.admesh-layout__empty-content {
  text-align: center;
  max-width: 500px;
  position: relative;
  z-index: 1;
}

.admesh-layout__empty-content h3 {
  margin: 0 0 var(--admesh-spacing-lg) 0;
  font-size: var(--admesh-font-size-2xl);
  font-weight: var(--admesh-font-bold);
  background: linear-gradient(135deg, var(--admesh-text-primary) 0%, var(--admesh-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admesh-layout__empty-content p {
  margin: 0;
  font-size: var(--admesh-font-size-lg);
  color: var(--admesh-text-muted);
  line-height: var(--admesh-leading-relaxed);
}

/* Modern cards layout */
.admesh-layout--cards .admesh-layout__cards-container {
  display: flex;
  flex-direction: column;
  gap: var(--admesh-spacing-2xl);
}

.admesh-layout__header {
  text-align: center;
  margin-bottom: var(--admesh-spacing-2xl);
  position: relative;
}

.admesh-layout__header::after {
  content: '';
  position: absolute;
  bottom: -var(--admesh-spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--admesh-gradient-primary);
  border-radius: var(--admesh-radius-full);
}

.admesh-layout__title {
  margin: 0 0 var(--admesh-spacing-md) 0;
  font-size: var(--admesh-font-size-3xl);
  font-weight: var(--admesh-font-extrabold);
  background: linear-gradient(135deg, var(--admesh-text-primary) 0%, var(--admesh-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: var(--admesh-leading-tight);
}

.admesh-layout__subtitle {
  margin: 0;
  font-size: var(--admesh-font-size-lg);
  color: var(--admesh-text-muted);
  font-weight: var(--admesh-font-medium);
}

.admesh-layout__cards-grid {
  display: grid;
  gap: var(--admesh-spacing-2xl);
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* Responsive grid adjustments */
@media (min-width: 640px) {
  .admesh-layout__cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
}

@media (min-width: 768px) {
  .admesh-layout__cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (min-width: 1024px) {
  .admesh-layout__cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    max-width: 1200px;
    margin: 0 auto;
  }
}

/* Specific grid layouts based on count */
.admesh-layout__cards-grid:has(> :nth-child(1):last-child) {
  /* Single item - center it */
  grid-template-columns: 1fr;
  max-width: 400px;
  margin: 0 auto;
}

.admesh-layout__cards-grid:has(> :nth-child(2):last-child) {
  /* Two items */
  grid-template-columns: repeat(2, 1fr);
  max-width: 800px;
  margin: 0 auto;
}

.admesh-layout__cards-grid:has(> :nth-child(3):last-child) {
  /* Three items */
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  max-width: 1000px;
  margin: 0 auto;
}

/* More indicator */
.admesh-layout__more-indicator {
  text-align: center;
  padding: var(--admesh-spacing-lg);
  margin-top: var(--admesh-spacing-lg);
  background-color: var(--admesh-bg-secondary);
  border-radius: var(--admesh-radius-md);
}

.admesh-layout__more-indicator p {
  margin: 0;
  font-style: italic;
}

/* Compare layout */
.admesh-layout--compare {
  /* Compare table handles its own styling */
}

/* List layout (for future use) */
.admesh-layout--list .admesh-layout__list-container {
  display: flex;
  flex-direction: column;
  gap: var(--admesh-spacing-lg);
}

.admesh-layout--list .admesh-layout__list-item {
  display: flex;
  align-items: center;
  gap: var(--admesh-spacing-lg);
  padding: var(--admesh-spacing-lg);
  background-color: var(--admesh-bg-primary);
  border: 1px solid var(--admesh-border-primary);
  border-radius: var(--admesh-radius-md);
  transition: all 0.2s ease;
}

.admesh-layout--list .admesh-layout__list-item:hover {
  box-shadow: var(--admesh-shadow-md);
  border-color: var(--admesh-border-secondary);
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .admesh-layout__cards-grid {
    grid-template-columns: 1fr;
    gap: var(--admesh-spacing-lg);
  }
  
  .admesh-layout__cards-grid:has(> :nth-child(1):last-child),
  .admesh-layout__cards-grid:has(> :nth-child(2):last-child),
  .admesh-layout__cards-grid:has(> :nth-child(3):last-child) {
    grid-template-columns: 1fr;
    max-width: none;
  }
  
  .admesh-layout__header {
    margin-bottom: var(--admesh-spacing-md);
  }
  
  .admesh-layout__title {
    font-size: var(--admesh-font-size-lg);
  }
  
  .admesh-layout--cards .admesh-layout__cards-container {
    gap: var(--admesh-spacing-lg);
  }
}

/* Dark theme adjustments */
[data-admesh-theme="dark"] .admesh-layout__empty {
  background-color: var(--admesh-bg-secondary);
  border-color: var(--admesh-border-primary);
}

[data-admesh-theme="dark"] .admesh-layout__more-indicator {
  background-color: var(--admesh-bg-secondary);
}

[data-admesh-theme="dark"] .admesh-layout--list .admesh-layout__list-item {
  background-color: var(--admesh-bg-primary);
  border-color: var(--admesh-border-primary);
}

/* Loading state (for future use) */
.admesh-layout--loading {
  opacity: 0.7;
  pointer-events: none;
}

.admesh-layout--loading .admesh-layout__cards-grid {
  filter: blur(1px);
}

/* Animation for layout changes */
.admesh-layout {
  transition: all 0.3s ease;
}

.admesh-layout__cards-grid {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus management for accessibility */
.admesh-layout:focus-within {
  /* Ensure focus is visible within the layout */
}

/* Print styles */
@media print {
  .admesh-layout__cards-grid {
    grid-template-columns: 1fr;
    gap: var(--admesh-spacing-md);
  }
  
  .admesh-layout__more-indicator {
    display: none;
  }
}

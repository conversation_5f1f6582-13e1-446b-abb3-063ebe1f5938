{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "4XWCSwyxvxbqIAsrSdhpk", "sessionId": "Qj01L-obEc97quP6tpT5T", "payload": {"eventType": "dev"}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.11.0", "storybookVersion": "9.0.4", "cliVersion": "9.0.4"}}, "timestamp": 1748918790891}, "init-step": {"body": {"eventType": "init-step", "eventId": "O45lLcn-JbNF6eBixtVLg", "sessionId": "AHXxp6zKj7zanP8AFO7PC", "metadata": {"generatedAt": 1748897850228, "userSince": 1748897778474, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm"}, "storybookVersionSpecifier": "9.0.4", "language": "typescript"}, "payload": {"step": "new-user-check", "newUser": true}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "storybookVersion": "9.0.4", "cliVersion": "9.0.4"}}, "timestamp": 1748897850540}, "init": {"body": {"eventType": "init", "eventId": "MXESSFgfnfk1A_tCZ1mfB", "sessionId": "AHXxp6zKj7zanP8AFO7PC", "metadata": {"generatedAt": 1748897850228, "userSince": 1748897778474, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm"}, "storybookVersionSpecifier": "9.0.4", "language": "typescript"}, "payload": {"projectType": "REACT", "features": {"dev": true, "docs": true, "test": true}, "newUser": true}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "storybookVersion": "9.0.4", "cliVersion": "9.0.4"}}, "timestamp": 1748897858714}, "version-update": {"body": {"eventType": "version-update", "eventId": "QWo0jr_M4WphVk3A7udX_", "sessionId": "AHXxp6zKj7zanP8AFO7PC", "metadata": {"generatedAt": 1748898280963, "userSince": 1748897778474, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.0.0", "@storybook/addon-vitest": "9.0.4", "@vitest/browser": "3.2.0", "@vitest/coverage-v8": "3.2.0", "playwright": "1.52.0", "vitest": "3.2.0"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "storybookVersion": "9.0.4", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.0.4"}, "eslint-plugin-storybook": {"version": "9.0.4"}, "storybook": {"version": "9.0.4"}}, "addons": {"@storybook/addon-onboarding": {"version": "9.0.4"}, "@chromatic-com/storybook": {"version": "4.0.0"}, "@storybook/addon-docs": {"version": "9.0.4"}, "@storybook/addon-a11y": {"version": "9.0.4"}, "@storybook/addon-vitest": {"version": "9.0.4"}}}, "payload": {}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "storybookVersion": "9.0.4", "cliVersion": "9.0.4"}}, "timestamp": 1748898281822}, "dev": {"body": {"eventType": "dev", "eventId": "Q8YYKlFq8zUqld5x6UdWI", "sessionId": "Qj01L-obEc97quP6tpT5T", "metadata": {"generatedAt": 1748918792826, "userSince": 1748897778474, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.0.0", "@storybook/addon-vitest": "9.0.4", "@vitest/browser": "3.2.0", "@vitest/coverage-v8": "3.2.0", "playwright": "1.52.0", "vitest": "3.2.0"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "storybookVersion": "9.0.4", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.0.4"}, "eslint-plugin-storybook": {"version": "9.0.4"}, "storybook": {"version": "9.0.4"}}, "addons": {"@storybook/addon-onboarding": {"version": "9.0.4"}, "@chromatic-com/storybook": {"version": "4.0.0"}, "@storybook/addon-docs": {"version": "9.0.4"}, "@storybook/addon-a11y": {"version": "9.0.4"}, "@storybook/addon-vitest": {"version": "9.0.4"}}}, "payload": {"versionStatus": "cached", "storyIndex": {"storyCount": 25, "componentCount": 3, "pageStoryCount": 0, "playStoryCount": 0, "autodocsCount": 3, "mdxCount": 0, "exampleStoryCount": 8, "exampleDocsCount": 2, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "svelteCsfV4Count": 0, "svelteCsfV5Count": 0, "version": 5}, "storyStats": {"factory": 0, "play": 0, "render": 2, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 25, "storyFn": 0, "mount": 0, "moduleMock": 0}}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.11.0", "storybookVersion": "9.0.4", "cliVersion": "9.0.4"}}, "timestamp": 1748918793236}, "addon-onboarding": {"body": {"eventType": "addon-onboarding", "eventId": "-KZ6hLE68xfPsZ8OizERx", "sessionId": "AHXxp6zKj7zanP8AFO7PC", "metadata": {"generatedAt": 1748898280963, "userSince": 1748897778474, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.0.0", "@storybook/addon-vitest": "9.0.4", "@vitest/browser": "3.2.0", "@vitest/coverage-v8": "3.2.0", "playwright": "1.52.0", "vitest": "3.2.0"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "storybookVersion": "9.0.4", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.0.4"}, "eslint-plugin-storybook": {"version": "9.0.4"}, "storybook": {"version": "9.0.4"}}, "addons": {"@storybook/addon-onboarding": {"version": "9.0.4"}, "@chromatic-com/storybook": {"version": "4.0.0"}, "@storybook/addon-docs": {"version": "9.0.4"}, "@storybook/addon-a11y": {"version": "9.0.4"}, "@storybook/addon-vitest": {"version": "9.0.4"}}}, "payload": {"step": "2:Controls", "addonVersion": "9.0.4"}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "storybookVersion": "9.0.4", "cliVersion": "9.0.4"}}, "timestamp": 1748898309632}, "addon-visual-tests": {"body": {"eventType": "addon-visual-tests", "eventId": "pd-zqxNPir3swrzFxPbto", "sessionId": "AHXxp6zKj7zanP8AFO7PC", "metadata": {"generatedAt": 1748900157972, "userSince": 1748897778474, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.0.0", "@storybook/addon-vitest": "9.0.4", "@vitest/browser": "3.2.0", "@vitest/coverage-v8": "3.2.0", "playwright": "1.52.0", "vitest": "3.2.0"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "storybookVersion": "9.0.4", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.0.4"}, "eslint-plugin-storybook": {"version": "9.0.4"}, "storybook": {"version": "9.0.4"}}, "addons": {"@storybook/addon-onboarding": {"version": "9.0.4"}, "@chromatic-com/storybook": {"version": "4.0.0"}, "@storybook/addon-docs": {"version": "9.0.4"}, "@storybook/addon-a11y": {"version": "9.0.4"}, "@storybook/addon-vitest": {"version": "9.0.4"}}}, "payload": {"location": "Authentication", "screen": "Welcome", "addonVersion": "4.0.0"}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.11.0", "storybookVersion": "9.0.4", "cliVersion": "9.0.4"}}, "timestamp": 1748900295259}, "canceled": {"body": {"eventType": "canceled", "eventId": "c08EQAKPz2LrWgID_-Xq-", "sessionId": "AHXxp6zKj7zanP8AFO7PC", "payload": {"eventType": "init"}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "storybookVersion": "9.0.4"}}, "timestamp": 1748899167318}, "error": {"body": {"eventType": "error", "eventId": "dCRqsDRuoxI0XlQPNxsob", "sessionId": "AHXxp6zKj7zanP8AFO7PC", "metadata": {"generatedAt": 1748902772805, "userSince": 1748897778474, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.0.0", "@storybook/addon-vitest": "9.0.4", "@vitest/browser": "3.2.0", "@vitest/coverage-v8": "3.2.0", "playwright": "1.52.0", "vitest": "3.2.0"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "storybookVersion": "9.0.4", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.0.4"}, "eslint-plugin-storybook": {"version": "9.0.4"}, "storybook": {"version": "9.0.4"}}, "addons": {"@storybook/addon-onboarding": {"version": "9.0.4"}, "@chromatic-com/storybook": {"version": "4.0.0"}, "@storybook/addon-docs": {"version": "9.0.4"}, "@storybook/addon-a11y": {"version": "9.0.4"}, "@storybook/addon-vitest": {"version": "9.0.4"}}}, "payload": {"code": 1, "name": "SB_MANAGER_UNCAUGHT_0001 (UncaughtManagerError)", "category": "MANAGER_UNCAUGHT", "eventType": "browser", "errorHash": "d211db7c7d70bde031df42becc6c6c5b49aab9313bb0a53e126a44cb31eb11f7", "isErrorInstance": true}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.11.0", "storybookVersion": "9.0.4", "cliVersion": "9.0.4"}}, "timestamp": 1748902813671}}}
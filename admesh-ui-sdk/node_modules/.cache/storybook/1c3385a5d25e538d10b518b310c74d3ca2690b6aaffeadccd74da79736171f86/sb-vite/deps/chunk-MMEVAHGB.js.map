{"version": 3, "sources": ["../../../../../lodash/_basePick.js", "../../../../../lodash/_isFlattenable.js", "../../../../../lodash/_baseFlatten.js", "../../../../../lodash/flatten.js", "../../../../../lodash/_flatRest.js", "../../../../../lodash/pick.js"], "sourcesContent": ["var basePickBy = require('./_basePickBy'),\n    hasIn = require('./hasIn');\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function(value, path) {\n    return hasIn(object, path);\n  });\n}\n\nmodule.exports = basePick;\n", "var Symbol = require('./_Symbol'),\n    isArguments = require('./isArguments'),\n    isArray = require('./isArray');\n\n/** Built-in value references. */\nvar spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) ||\n    !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\n\nmodule.exports = isFlattenable;\n", "var arrayPush = require('./_arrayPush'),\n    isFlattenable = require('./_isFlattenable');\n\n/**\n * The base implementation of `_.flatten` with support for restricting flattening.\n *\n * @private\n * @param {Array} array The array to flatten.\n * @param {number} depth The maximum recursion depth.\n * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.\n * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.\n * @param {Array} [result=[]] The initial result value.\n * @returns {Array} Returns the new flattened array.\n */\nfunction baseFlatten(array, depth, predicate, isStrict, result) {\n  var index = -1,\n      length = array.length;\n\n  predicate || (predicate = isFlattenable);\n  result || (result = []);\n\n  while (++index < length) {\n    var value = array[index];\n    if (depth > 0 && predicate(value)) {\n      if (depth > 1) {\n        // Recursively flatten arrays (susceptible to call stack limits).\n        baseFlatten(value, depth - 1, predicate, isStrict, result);\n      } else {\n        arrayPush(result, value);\n      }\n    } else if (!isStrict) {\n      result[result.length] = value;\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseFlatten;\n", "var baseFlatten = require('./_baseFlatten');\n\n/**\n * Flattens `array` a single level deep.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flatten([1, [2, [3, [4]], 5]]);\n * // => [1, 2, [3, [4]], 5]\n */\nfunction flatten(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, 1) : [];\n}\n\nmodule.exports = flatten;\n", "var flatten = require('./flatten'),\n    overRest = require('./_overRest'),\n    setToString = require('./_setToString');\n\n/**\n * A specialized version of `baseRest` which flattens the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @returns {Function} Returns the new function.\n */\nfunction flatRest(func) {\n  return setToString(overRest(func, undefined, flatten), func + '');\n}\n\nmodule.exports = flatRest;\n", "var basePick = require('./_basePick'),\n    flatRest = require('./_flatRest');\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function(object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\n\nmodule.exports = pick;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,QAAQ;AAWZ,aAAS,SAAS,QAAQ,OAAO;AAC/B,aAAO,WAAW,QAAQ,OAAO,SAAS,OAAO,MAAM;AACrD,eAAO,MAAM,QAAQ,IAAI;AAAA,MAC3B,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AAAA,QAAI,SAAS;AAAb,QACI,cAAc;AADlB,QAEI,UAAU;AAGd,QAAI,mBAAmB,SAAS,OAAO,qBAAqB;AAS5D,aAAS,cAAc,OAAO;AAC5B,aAAO,QAAQ,KAAK,KAAK,YAAY,KAAK,KACxC,CAAC,EAAE,oBAAoB,SAAS,MAAM,gBAAgB;AAAA,IAC1D;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,gBAAgB;AAapB,aAAS,YAAY,OAAO,OAAO,WAAW,UAAU,QAAQ;AAC9D,UAAI,QAAQ,IACR,SAAS,MAAM;AAEnB,oBAAc,YAAY;AAC1B,iBAAW,SAAS,CAAC;AAErB,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,MAAM,KAAK;AACvB,YAAI,QAAQ,KAAK,UAAU,KAAK,GAAG;AACjC,cAAI,QAAQ,GAAG;AAEb,wBAAY,OAAO,QAAQ,GAAG,WAAW,UAAU,MAAM;AAAA,UAC3D,OAAO;AACL,sBAAU,QAAQ,KAAK;AAAA,UACzB;AAAA,QACF,WAAW,CAAC,UAAU;AACpB,iBAAO,OAAO,MAAM,IAAI;AAAA,QAC1B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrCjB;AAAA;AAAA,QAAI,cAAc;AAgBlB,aAAS,QAAQ,OAAO;AACtB,UAAI,SAAS,SAAS,OAAO,IAAI,MAAM;AACvC,aAAO,SAAS,YAAY,OAAO,CAAC,IAAI,CAAC;AAAA,IAC3C;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAAA,QAAI,UAAU;AAAd,QACI,WAAW;AADf,QAEI,cAAc;AASlB,aAAS,SAAS,MAAM;AACtB,aAAO,YAAY,SAAS,MAAM,QAAW,OAAO,GAAG,OAAO,EAAE;AAAA,IAClE;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,WAAW;AAmBf,QAAI,OAAO,SAAS,SAAS,QAAQ,OAAO;AAC1C,aAAO,UAAU,OAAO,CAAC,IAAI,SAAS,QAAQ,KAAK;AAAA,IACrD,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}
{"version": 3, "sources": ["../../../../../lodash/startCase.js"], "sourcesContent": ["var createCompounder = require('./_createCompounder'),\n    upperFirst = require('./upperFirst');\n\n/**\n * Converts `string` to\n * [start case](https://en.wikipedia.org/wiki/Letter_case#Stylistic_or_specialised_usage).\n *\n * @static\n * @memberOf _\n * @since 3.1.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the start cased string.\n * @example\n *\n * _.startCase('--foo-bar--');\n * // => 'Foo Bar'\n *\n * _.startCase('fooBar');\n * // => 'Foo Bar'\n *\n * _.startCase('__FOO_BAR__');\n * // => 'FOO BAR'\n */\nvar startCase = createCompounder(function(result, word, index) {\n  return result + (index ? ' ' : '') + upperFirst(word);\n});\n\nmodule.exports = startCase;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,mBAAmB;AAAvB,QACI,aAAa;AAuBjB,QAAI,YAAY,iBAAiB,SAAS,QAAQ,MAAM,OAAO;AAC7D,aAAO,UAAU,QAAQ,MAAM,MAAM,WAAW,IAAI;AAAA,IACtD,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}
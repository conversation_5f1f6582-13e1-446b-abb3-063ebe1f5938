import {
  require_react
} from "./chunk-XQWWUXQD.js";
import {
  require_preview_api
} from "./chunk-DQSSUQZP.js";
import {
  __toESM
} from "./chunk-KEXKKQVW.js";

// node_modules/@storybook/react/dist/chunk-XLZBPYSH.mjs
var import_react = __toESM(require_react(), 1);
var import_preview_api = __toESM(require_preview_api(), 1);
var applyDecorators = (storyFn, decorators) => (0, import_preview_api.defaultDecorateStory)((context) => import_react.default.createElement(storyFn, context), decorators);

export {
  applyDecorators
};
//# sourceMappingURL=chunk-U4SLB3OH.js.map

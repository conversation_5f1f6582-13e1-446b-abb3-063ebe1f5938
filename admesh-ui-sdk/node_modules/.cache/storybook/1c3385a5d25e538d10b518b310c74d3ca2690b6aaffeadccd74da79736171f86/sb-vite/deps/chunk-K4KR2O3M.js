import {
  require_baseMerge,
  require_createAssigner
} from "./chunk-A5CLDMAE.js";
import {
  __commonJS
} from "./chunk-KEXKKQVW.js";

// node_modules/lodash/merge.js
var require_merge = __commonJS({
  "node_modules/lodash/merge.js"(exports, module) {
    var baseMerge = require_baseMerge();
    var createAssigner = require_createAssigner();
    var merge = createAssigner(function(object, source, srcIndex) {
      baseMerge(object, source, srcIndex);
    });
    module.exports = merge;
  }
});

export {
  require_merge
};
//# sourceMappingURL=chunk-K4KR2O3M.js.map

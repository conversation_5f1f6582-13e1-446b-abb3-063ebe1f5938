{"version": 3, "sources": ["../../../../../lodash/_defineProperty.js", "../../../../../lodash/_baseAssignValue.js"], "sourcesContent": ["var getNative = require('./_getNative');\n\nvar defineProperty = (function() {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}());\n\nmodule.exports = defineProperty;\n", "var defineProperty = require('./_defineProperty');\n\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction baseAssignValue(object, key, value) {\n  if (key == '__proto__' && defineProperty) {\n    defineProperty(object, key, {\n      'configurable': true,\n      'enumerable': true,\n      'value': value,\n      'writable': true\n    });\n  } else {\n    object[key] = value;\n  }\n}\n\nmodule.exports = baseAssignValue;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,QAAI,YAAY;AAEhB,QAAI,iBAAkB,WAAW;AAC/B,UAAI;AACF,YAAI,OAAO,UAAU,QAAQ,gBAAgB;AAC7C,aAAK,CAAC,GAAG,IAAI,CAAC,CAAC;AACf,eAAO;AAAA,MACT,SAAS,GAAG;AAAA,MAAC;AAAA,IACf,EAAE;AAEF,WAAO,UAAU;AAAA;AAAA;;;ACVjB;AAAA;AAAA,QAAI,iBAAiB;AAWrB,aAAS,gBAAgB,QAAQ,KAAK,OAAO;AAC3C,UAAI,OAAO,eAAe,gBAAgB;AACxC,uBAAe,QAAQ,KAAK;AAAA,UAC1B,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,SAAS;AAAA,UACT,YAAY;AAAA,QACd,CAAC;AAAA,MACH,OAAO;AACL,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}
{"version": 3, "sources": ["../../../../../lodash/_baseForOwn.js"], "sourcesContent": ["var baseFor = require('./_baseFor'),\n    keys = require('./keys');\n\n/**\n * The base implementation of `_.forOwn` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Object} Returns `object`.\n */\nfunction baseForOwn(object, iteratee) {\n  return object && baseFor(object, iteratee, keys);\n}\n\nmodule.exports = baseForOwn;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,UAAU;AAAd,QACI,OAAO;AAUX,aAAS,WAAW,QAAQ,UAAU;AACpC,aAAO,UAAU,QAAQ,QAAQ,UAAU,IAAI;AAAA,IACjD;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}
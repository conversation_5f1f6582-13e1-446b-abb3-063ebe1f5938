import {
  require_overArg
} from "./chunk-GYNE55BH.js";
import {
  __commonJS
} from "./chunk-KEXKKQVW.js";

// node_modules/lodash/_getPrototype.js
var require_getPrototype = __commonJS({
  "node_modules/lodash/_getPrototype.js"(exports, module) {
    var overArg = require_overArg();
    var getPrototype = overArg(Object.getPrototypeOf, Object);
    module.exports = getPrototype;
  }
});

export {
  require_getPrototype
};
//# sourceMappingURL=chunk-7IZGUACU.js.map

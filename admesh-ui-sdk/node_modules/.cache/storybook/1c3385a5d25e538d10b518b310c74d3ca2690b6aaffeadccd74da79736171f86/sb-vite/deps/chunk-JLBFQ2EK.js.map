{"version": 3, "sources": ["../../../../../ts-dedent/src/index.ts"], "sourcesContent": ["export function dedent(\n  templ: TemplateStringsArray | string,\n  ...values: unknown[]\n): string {\n  let strings = Array.from(typeof templ === 'string' ? [templ] : templ);\n\n  // 1. Remove trailing whitespace.\n  strings[strings.length - 1] = strings[strings.length - 1].replace(\n    /\\r?\\n([\\t ]*)$/,\n    '',\n  );\n\n  // 2. Find all line breaks to determine the highest common indentation level.\n  const indentLengths = strings.reduce((arr, str) => {\n    const matches = str.match(/\\n([\\t ]+|(?!\\s).)/g);\n    if (matches) {\n      return arr.concat(\n        matches.map((match) => match.match(/[\\t ]/g)?.length ?? 0),\n      );\n    }\n    return arr;\n  }, <number[]>[]);\n\n  // 3. Remove the common indentation from all strings.\n  if (indentLengths.length) {\n    const pattern = new RegExp(`\\n[\\t ]{${Math.min(...indentLengths)}}`, 'g');\n\n    strings = strings.map((str) => str.replace(pattern, '\\n'));\n  }\n\n  // 4. Remove leading whitespace.\n  strings[0] = strings[0].replace(/^\\r?\\n/, '');\n\n  // 5. Perform interpolation.\n  let string = strings[0];\n\n  values.forEach((value, i) => {\n    // 5.1 Read current indentation level\n    const endentations = string.match(/(?:^|\\n)( *)$/)\n    const endentation = endentations ? endentations[1] : ''\n    let indentedValue = value\n    // 5.2 Add indentation to values with multiline strings\n    if (typeof value === 'string' && value.includes('\\n')) {\n      indentedValue = String(value)\n        .split('\\n')\n        .map((str, i) => {\n          return i === 0 ? str : `${endentation}${str}`\n        })\n        .join('\\n');\n    }\n\n    string += indentedValue + strings[i + 1];\n  });\n\n  return string;\n}\n\nexport default dedent;\n"], "mappings": ";AAAM,SAAU,OACd,OAAoC;AACpC,MAAA,SAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAoB;AAApB,WAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAEA,MAAI,UAAU,MAAM,KAAK,OAAO,UAAU,WAAW,CAAC,KAAK,IAAI,KAAK;AAGpE,UAAQ,QAAQ,SAAS,CAAC,IAAI,QAAQ,QAAQ,SAAS,CAAC,EAAE,QACxD,kBACA,EAAE;AAIJ,MAAM,gBAAgB,QAAQ,OAAO,SAAC,KAAK,KAAG;AAC5C,QAAM,UAAU,IAAI,MAAM,qBAAqB;AAC/C,QAAI,SAAS;AACX,aAAO,IAAI,OACT,QAAQ,IAAI,SAAC,OAAK;AAAA,YAAA,IAAA;AAAK,gBAAA,MAAA,KAAA,MAAM,MAAM,QAAQ,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,KAAI;MAAC,CAAA,CAAC;;AAG9D,WAAO;EACT,GAAa,CAAA,CAAE;AAGf,MAAI,cAAc,QAAQ;AACxB,QAAM,YAAU,IAAI,OAAO,YAAW,KAAK,IAAG,MAAR,MAAY,aAAa,IAAA,KAAM,GAAG;AAExE,cAAU,QAAQ,IAAI,SAAC,KAAG;AAAK,aAAA,IAAI,QAAQ,WAAS,IAAI;IAAzB,CAA0B;;AAI3D,UAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE;AAG5C,MAAI,SAAS,QAAQ,CAAC;AAEtB,SAAO,QAAQ,SAAC,OAAO,GAAC;AAEtB,QAAM,eAAe,OAAO,MAAM,eAAe;AACjD,QAAM,cAAc,eAAe,aAAa,CAAC,IAAI;AACrD,QAAI,gBAAgB;AAEpB,QAAI,OAAO,UAAU,YAAY,MAAM,SAAS,IAAI,GAAG;AACrD,sBAAgB,OAAO,KAAK,EACzB,MAAM,IAAI,EACV,IAAI,SAAC,KAAKA,IAAC;AACV,eAAOA,OAAM,IAAI,MAAM,KAAG,cAAc;MAC1C,CAAC,EACA,KAAK,IAAI;;AAGd,cAAU,gBAAgB,QAAQ,IAAI,CAAC;EACzC,CAAC;AAED,SAAO;AACT;AAEA,IAAA,cAAe;", "names": ["i"]}
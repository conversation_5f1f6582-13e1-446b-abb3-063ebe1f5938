import {
  require_upperFirst
} from "./chunk-NA3I52LO.js";
import {
  require_createCompounder
} from "./chunk-EQURAP73.js";
import {
  require_toString
} from "./chunk-Z3KLNPTG.js";
import {
  __commonJS
} from "./chunk-KEXKKQVW.js";

// node_modules/lodash/capitalize.js
var require_capitalize = __commonJS({
  "node_modules/lodash/capitalize.js"(exports, module) {
    var toString = require_toString();
    var upperFirst = require_upperFirst();
    function capitalize(string) {
      return upperFirst(toString(string).toLowerCase());
    }
    module.exports = capitalize;
  }
});

// node_modules/lodash/camelCase.js
var require_camelCase = __commonJS({
  "node_modules/lodash/camelCase.js"(exports, module) {
    var capitalize = require_capitalize();
    var createCompounder = require_createCompounder();
    var camelCase = createCompounder(function(result, word, index) {
      word = word.toLowerCase();
      return result + (index ? capitalize(word) : word);
    });
    module.exports = camelCase;
  }
});

export {
  require_camelCase
};
//# sourceMappingURL=chunk-VQ2MSYAS.js.map

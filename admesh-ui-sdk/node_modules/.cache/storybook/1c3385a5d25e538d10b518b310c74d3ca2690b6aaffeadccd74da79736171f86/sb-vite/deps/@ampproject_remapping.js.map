{"version": 3, "sources": ["../../../../../@jridgewell/resolve-uri/src/resolve-uri.ts", "../../../../../@jridgewell/trace-mapping/src/resolve.ts", "../../../../../@jridgewell/trace-mapping/src/strip-filename.ts", "../../../../../@jridgewell/trace-mapping/src/sourcemap-segment.ts", "../../../../../@jridgewell/trace-mapping/src/sort.ts", "../../../../../@jridgewell/trace-mapping/src/binary-search.ts", "../../../../../@jridgewell/trace-mapping/src/by-source.ts", "../../../../../@jridgewell/trace-mapping/src/any-map.ts", "../../../../../@jridgewell/trace-mapping/src/trace-mapping.ts", "../../../../../@jridgewell/set-array/src/set-array.ts", "../../../../../@jridgewell/gen-mapping/src/sourcemap-segment.ts", "../../../../../@jridgewell/gen-mapping/src/gen-mapping.ts", "../../../../../@ampproject/remapping/src/source-map-tree.ts", "../../../../../@ampproject/remapping/src/build-source-map-tree.ts", "../../../../../@ampproject/remapping/src/source-map.ts", "../../../../../@ampproject/remapping/src/remapping.ts"], "sourcesContent": ["// Matches the scheme of a URL, eg \"http://\"\nconst schemeRegex = /^[\\w+.-]+:\\/\\//;\n\n/**\n * Matches the parts of a URL:\n * 1. Scheme, including \":\", guaranteed.\n * 2. User/password, including \"@\", optional.\n * 3. Host, guaranteed.\n * 4. Port, including \":\", optional.\n * 5. Path, including \"/\", optional.\n * 6. Query, including \"?\", optional.\n * 7. Hash, including \"#\", optional.\n */\nconst urlRegex = /^([\\w+.-]+:)\\/\\/([^@/#?]*@)?([^:/#?]*)(:\\d+)?(\\/[^#?]*)?(\\?[^#]*)?(#.*)?/;\n\n/**\n * File URLs are weird. They dont' need the regular `//` in the scheme, they may or may not start\n * with a leading `/`, they can have a domain (but only if they don't start with a Windows drive).\n *\n * 1. Host, optional.\n * 2. Path, which may include \"/\", guaranteed.\n * 3. Query, including \"?\", optional.\n * 4. Hash, including \"#\", optional.\n */\nconst fileRegex = /^file:(?:\\/\\/((?![a-z]:)[^/#?]*)?)?(\\/?[^#?]*)(\\?[^#]*)?(#.*)?/i;\n\ntype Url = {\n  scheme: string;\n  user: string;\n  host: string;\n  port: string;\n  path: string;\n  query: string;\n  hash: string;\n  type: UrlType;\n};\n\nconst enum UrlType {\n  Empty = 1,\n  Hash = 2,\n  Query = 3,\n  RelativePath = 4,\n  AbsolutePath = 5,\n  SchemeRelative = 6,\n  Absolute = 7,\n}\n\nfunction isAbsoluteUrl(input: string): boolean {\n  return schemeRegex.test(input);\n}\n\nfunction isSchemeRelativeUrl(input: string): boolean {\n  return input.startsWith('//');\n}\n\nfunction isAbsolutePath(input: string): boolean {\n  return input.startsWith('/');\n}\n\nfunction isFileUrl(input: string): boolean {\n  return input.startsWith('file:');\n}\n\nfunction isRelative(input: string): boolean {\n  return /^[.?#]/.test(input);\n}\n\nfunction parseAbsoluteUrl(input: string): Url {\n  const match = urlRegex.exec(input)!;\n  return makeUrl(\n    match[1],\n    match[2] || '',\n    match[3],\n    match[4] || '',\n    match[5] || '/',\n    match[6] || '',\n    match[7] || '',\n  );\n}\n\nfunction parseFileUrl(input: string): Url {\n  const match = fileRegex.exec(input)!;\n  const path = match[2];\n  return makeUrl(\n    'file:',\n    '',\n    match[1] || '',\n    '',\n    isAbsolutePath(path) ? path : '/' + path,\n    match[3] || '',\n    match[4] || '',\n  );\n}\n\nfunction makeUrl(\n  scheme: string,\n  user: string,\n  host: string,\n  port: string,\n  path: string,\n  query: string,\n  hash: string,\n): Url {\n  return {\n    scheme,\n    user,\n    host,\n    port,\n    path,\n    query,\n    hash,\n    type: UrlType.Absolute,\n  };\n}\n\nfunction parseUrl(input: string): Url {\n  if (isSchemeRelativeUrl(input)) {\n    const url = parseAbsoluteUrl('http:' + input);\n    url.scheme = '';\n    url.type = UrlType.SchemeRelative;\n    return url;\n  }\n\n  if (isAbsolutePath(input)) {\n    const url = parseAbsoluteUrl('http://foo.com' + input);\n    url.scheme = '';\n    url.host = '';\n    url.type = UrlType.AbsolutePath;\n    return url;\n  }\n\n  if (isFileUrl(input)) return parseFileUrl(input);\n\n  if (isAbsoluteUrl(input)) return parseAbsoluteUrl(input);\n\n  const url = parseAbsoluteUrl('http://foo.com/' + input);\n  url.scheme = '';\n  url.host = '';\n  url.type = input\n    ? input.startsWith('?')\n      ? UrlType.Query\n      : input.startsWith('#')\n      ? UrlType.Hash\n      : UrlType.RelativePath\n    : UrlType.Empty;\n  return url;\n}\n\nfunction stripPathFilename(path: string): string {\n  // If a path ends with a parent directory \"..\", then it's a relative path with excess parent\n  // paths. It's not a file, so we can't strip it.\n  if (path.endsWith('/..')) return path;\n  const index = path.lastIndexOf('/');\n  return path.slice(0, index + 1);\n}\n\nfunction mergePaths(url: Url, base: Url) {\n  normalizePath(base, base.type);\n\n  // If the path is just a \"/\", then it was an empty path to begin with (remember, we're a relative\n  // path).\n  if (url.path === '/') {\n    url.path = base.path;\n  } else {\n    // Resolution happens relative to the base path's directory, not the file.\n    url.path = stripPathFilename(base.path) + url.path;\n  }\n}\n\n/**\n * The path can have empty directories \"//\", unneeded parents \"foo/..\", or current directory\n * \"foo/.\". We need to normalize to a standard representation.\n */\nfunction normalizePath(url: Url, type: UrlType) {\n  const rel = type <= UrlType.RelativePath;\n  const pieces = url.path.split('/');\n\n  // We need to preserve the first piece always, so that we output a leading slash. The item at\n  // pieces[0] is an empty string.\n  let pointer = 1;\n\n  // Positive is the number of real directories we've output, used for popping a parent directory.\n  // Eg, \"foo/bar/..\" will have a positive 2, and we can decrement to be left with just \"foo\".\n  let positive = 0;\n\n  // We need to keep a trailing slash if we encounter an empty directory (eg, splitting \"foo/\" will\n  // generate `[\"foo\", \"\"]` pieces). And, if we pop a parent directory. But once we encounter a\n  // real directory, we won't need to append, unless the other conditions happen again.\n  let addTrailingSlash = false;\n\n  for (let i = 1; i < pieces.length; i++) {\n    const piece = pieces[i];\n\n    // An empty directory, could be a trailing slash, or just a double \"//\" in the path.\n    if (!piece) {\n      addTrailingSlash = true;\n      continue;\n    }\n\n    // If we encounter a real directory, then we don't need to append anymore.\n    addTrailingSlash = false;\n\n    // A current directory, which we can always drop.\n    if (piece === '.') continue;\n\n    // A parent directory, we need to see if there are any real directories we can pop. Else, we\n    // have an excess of parents, and we'll need to keep the \"..\".\n    if (piece === '..') {\n      if (positive) {\n        addTrailingSlash = true;\n        positive--;\n        pointer--;\n      } else if (rel) {\n        // If we're in a relativePath, then we need to keep the excess parents. Else, in an absolute\n        // URL, protocol relative URL, or an absolute path, we don't need to keep excess.\n        pieces[pointer++] = piece;\n      }\n      continue;\n    }\n\n    // We've encountered a real directory. Move it to the next insertion pointer, which accounts for\n    // any popped or dropped directories.\n    pieces[pointer++] = piece;\n    positive++;\n  }\n\n  let path = '';\n  for (let i = 1; i < pointer; i++) {\n    path += '/' + pieces[i];\n  }\n  if (!path || (addTrailingSlash && !path.endsWith('/..'))) {\n    path += '/';\n  }\n  url.path = path;\n}\n\n/**\n * Attempts to resolve `input` URL/path relative to `base`.\n */\nexport default function resolve(input: string, base: string | undefined): string {\n  if (!input && !base) return '';\n\n  const url = parseUrl(input);\n  let inputType = url.type;\n\n  if (base && inputType !== UrlType.Absolute) {\n    const baseUrl = parseUrl(base);\n    const baseType = baseUrl.type;\n\n    switch (inputType) {\n      case UrlType.Empty:\n        url.hash = baseUrl.hash;\n      // fall through\n\n      case UrlType.Hash:\n        url.query = baseUrl.query;\n      // fall through\n\n      case UrlType.Query:\n      case UrlType.RelativePath:\n        mergePaths(url, baseUrl);\n      // fall through\n\n      case UrlType.AbsolutePath:\n        // The host, user, and port are joined, you can't copy one without the others.\n        url.user = baseUrl.user;\n        url.host = baseUrl.host;\n        url.port = baseUrl.port;\n      // fall through\n\n      case UrlType.SchemeRelative:\n        // The input doesn't have a schema at least, so we need to copy at least that over.\n        url.scheme = baseUrl.scheme;\n    }\n    if (baseType > inputType) inputType = baseType;\n  }\n\n  normalizePath(url, inputType);\n\n  const queryHash = url.query + url.hash;\n  switch (inputType) {\n    // This is impossible, because of the empty checks at the start of the function.\n    // case UrlType.Empty:\n\n    case UrlType.Hash:\n    case UrlType.Query:\n      return queryHash;\n\n    case UrlType.RelativePath: {\n      // The first char is always a \"/\", and we need it to be relative.\n      const path = url.path.slice(1);\n\n      if (!path) return queryHash || '.';\n\n      if (isRelative(base || input) && !isRelative(path)) {\n        // If base started with a leading \".\", or there is no base and input started with a \".\",\n        // then we need to ensure that the relative path starts with a \".\". We don't know if\n        // relative starts with a \"..\", though, so check before prepending.\n        return './' + path + queryHash;\n      }\n\n      return path + queryHash;\n    }\n\n    case UrlType.AbsolutePath:\n      return url.path + queryHash;\n\n    default:\n      return url.scheme + '//' + url.user + url.host + url.port + url.path + queryHash;\n  }\n}\n", "import resolveUri from '@jridgewell/resolve-uri';\n\nexport default function resolve(input: string, base: string | undefined): string {\n  // The base is always treated as a directory, if it's not empty.\n  // https://github.com/mozilla/source-map/blob/8cb3ee57/lib/util.js#L327\n  // https://github.com/chromium/chromium/blob/da4adbb3/third_party/blink/renderer/devtools/front_end/sdk/SourceMap.js#L400-L401\n  if (base && !base.endsWith('/')) base += '/';\n\n  return resolveUri(input, base);\n}\n", "/**\n * Removes everything after the last \"/\", but leaves the slash.\n */\nexport default function stripFilename(path: string | undefined | null): string {\n  if (!path) return '';\n  const index = path.lastIndexOf('/');\n  return path.slice(0, index + 1);\n}\n", "type GeneratedColumn = number;\ntype SourcesIndex = number;\ntype SourceLine = number;\ntype SourceColumn = number;\ntype NamesIndex = number;\n\ntype GeneratedLine = number;\n\nexport type SourceMapSegment =\n  | [GeneratedColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn, NamesIndex];\n\nexport type ReverseSegment = [SourceColumn, GeneratedLine, GeneratedColumn];\n\nexport const COLUMN = 0;\nexport const SOURCES_INDEX = 1;\nexport const SOURCE_LINE = 2;\nexport const SOURCE_COLUMN = 3;\nexport const NAMES_INDEX = 4;\n\nexport const REV_GENERATED_LINE = 1;\nexport const REV_GENERATED_COLUMN = 2;\n", "import { COLUMN } from './sourcemap-segment';\n\nimport type { SourceMapSegment } from './sourcemap-segment';\n\nexport default function maybeSort(\n  mappings: SourceMapSegment[][],\n  owned: boolean,\n): SourceMapSegment[][] {\n  const unsortedIndex = nextUnsortedSegmentLine(mappings, 0);\n  if (unsortedIndex === mappings.length) return mappings;\n\n  // If we own the array (meaning we parsed it from JSON), then we're free to directly mutate it. If\n  // not, we do not want to modify the consumer's input array.\n  if (!owned) mappings = mappings.slice();\n\n  for (let i = unsortedIndex; i < mappings.length; i = nextUnsortedSegmentLine(mappings, i + 1)) {\n    mappings[i] = sortSegments(mappings[i], owned);\n  }\n  return mappings;\n}\n\nfunction nextUnsortedSegmentLine(mappings: SourceMapSegment[][], start: number): number {\n  for (let i = start; i < mappings.length; i++) {\n    if (!isSorted(mappings[i])) return i;\n  }\n  return mappings.length;\n}\n\nfunction isSorted(line: SourceMapSegment[]): boolean {\n  for (let j = 1; j < line.length; j++) {\n    if (line[j][COLUMN] < line[j - 1][COLUMN]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction sortSegments(line: SourceMapSegment[], owned: boolean): SourceMapSegment[] {\n  if (!owned) line = line.slice();\n  return line.sort(sortComparator);\n}\n\nfunction sortComparator(a: SourceMapSegment, b: SourceMapSegment): number {\n  return a[COLUMN] - b[COLUMN];\n}\n", "import type { SourceMapSegment, ReverseSegment } from './sourcemap-segment';\nimport { COLUMN } from './sourcemap-segment';\n\nexport type MemoState = {\n  lastKey: number;\n  lastNeedle: number;\n  lastIndex: number;\n};\n\nexport let found = false;\n\n/**\n * A binary search implementation that returns the index if a match is found.\n * If no match is found, then the left-index (the index associated with the item that comes just\n * before the desired index) is returned. To maintain proper sort order, a splice would happen at\n * the next index:\n *\n * ```js\n * const array = [1, 3];\n * const needle = 2;\n * const index = binarySearch(array, needle, (item, needle) => item - needle);\n *\n * assert.equal(index, 0);\n * array.splice(index + 1, 0, needle);\n * assert.deepEqual(array, [1, 2, 3]);\n * ```\n */\nexport function binarySearch(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  low: number,\n  high: number,\n): number {\n  while (low <= high) {\n    const mid = low + ((high - low) >> 1);\n    const cmp = haystack[mid][COLUMN] - needle;\n\n    if (cmp === 0) {\n      found = true;\n      return mid;\n    }\n\n    if (cmp < 0) {\n      low = mid + 1;\n    } else {\n      high = mid - 1;\n    }\n  }\n\n  found = false;\n  return low - 1;\n}\n\nexport function upperBound(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  index: number,\n): number {\n  for (let i = index + 1; i < haystack.length; index = i++) {\n    if (haystack[i][COLUMN] !== needle) break;\n  }\n  return index;\n}\n\nexport function lowerBound(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  index: number,\n): number {\n  for (let i = index - 1; i >= 0; index = i--) {\n    if (haystack[i][COLUMN] !== needle) break;\n  }\n  return index;\n}\n\nexport function memoizedState(): MemoState {\n  return {\n    lastKey: -1,\n    lastNeedle: -1,\n    lastIndex: -1,\n  };\n}\n\n/**\n * This overly complicated beast is just to record the last tested line/column and the resulting\n * index, allowing us to skip a few tests if mappings are monotonically increasing.\n */\nexport function memoizedBinarySearch(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  state: MemoState,\n  key: number,\n): number {\n  const { lastKey, lastNeedle, lastIndex } = state;\n\n  let low = 0;\n  let high = haystack.length - 1;\n  if (key === lastKey) {\n    if (needle === lastNeedle) {\n      found = lastIndex !== -1 && haystack[lastIndex][COLUMN] === needle;\n      return lastIndex;\n    }\n\n    if (needle >= lastNeedle) {\n      // lastIndex may be -1 if the previous needle was not found.\n      low = lastIndex === -1 ? 0 : lastIndex;\n    } else {\n      high = lastIndex;\n    }\n  }\n  state.lastKey = key;\n  state.lastNeedle = needle;\n\n  return (state.lastIndex = binarySearch(haystack, needle, low, high));\n}\n", "import { COLUMN, SOURCES_INDEX, SOURCE_LINE, SOURCE_COLUMN } from './sourcemap-segment';\nimport { memoizedBinarySearch, upperBound } from './binary-search';\n\nimport type { ReverseSegment, SourceMapSegment } from './sourcemap-segment';\nimport type { MemoState } from './binary-search';\n\nexport type Source = {\n  __proto__: null;\n  [line: number]: Exclude<ReverseSegment, [number]>[];\n};\n\n// Rebuilds the original source files, with mappings that are ordered by source line/column instead\n// of generated line/column.\nexport default function buildBySources(\n  decoded: readonly SourceMapSegment[][],\n  memos: MemoState[],\n): Source[] {\n  const sources: Source[] = memos.map(buildNullArray);\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n      if (seg.length === 1) continue;\n\n      const sourceIndex = seg[SOURCES_INDEX];\n      const sourceLine = seg[SOURCE_LINE];\n      const sourceColumn = seg[SOURCE_COLUMN];\n      const originalSource = sources[sourceIndex];\n      const originalLine = (originalSource[sourceLine] ||= []);\n      const memo = memos[sourceIndex];\n\n      // The binary search either found a match, or it found the left-index just before where the\n      // segment should go. Either way, we want to insert after that. And there may be multiple\n      // generated segments associated with an original location, so there may need to move several\n      // indexes before we find where we need to insert.\n      let index = upperBound(\n        originalLine,\n        sourceColumn,\n        memoizedBinarySearch(originalLine, sourceColumn, memo, sourceLine),\n      );\n\n      memo.lastIndex = ++index;\n      insert(originalLine, index, [sourceColumn, i, seg[COLUMN]]);\n    }\n  }\n\n  return sources;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n\n// Null arrays allow us to use ordered index keys without actually allocating contiguous memory like\n// a real array. We use a null-prototype object to avoid prototype pollution and deoptimizations.\n// Numeric properties on objects are magically sorted in ascending order by the engine regardless of\n// the insertion order. So, by setting any numeric keys, even out of order, we'll get ascending\n// order when iterating with for-in.\nfunction buildNullArray<T extends { __proto__: null }>(): T {\n  return { __proto__: null } as T;\n}\n", "import { TraceMap, presortedDecodedMap, decodedMappings } from './trace-mapping';\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n} from './sourcemap-segment';\n\nimport type {\n  DecodedSourceMap,\n  DecodedSourceMapXInput,\n  EncodedSourceMapXInput,\n  SectionedSourceMapXInput,\n  SectionedSourceMapInput,\n  SectionXInput,\n} from './types';\nimport type { SourceMapSegment } from './sourcemap-segment';\n\ntype AnyMap = {\n  new (map: SectionedSourceMapInput, mapUrl?: string | null): TraceMap;\n  (map: SectionedSourceMapInput, mapUrl?: string | null): TraceMap;\n};\n\nexport const AnyMap: AnyMap = function (map, mapUrl) {\n  const parsed = parse(map);\n\n  if (!('sections' in parsed)) {\n    return new TraceMap(parsed as DecodedSourceMapXInput | EncodedSourceMapXInput, mapUrl);\n  }\n\n  const mappings: SourceMapSegment[][] = [];\n  const sources: string[] = [];\n  const sourcesContent: (string | null)[] = [];\n  const names: string[] = [];\n  const ignoreList: number[] = [];\n\n  recurse(\n    parsed,\n    mapUrl,\n    mappings,\n    sources,\n    sourcesContent,\n    names,\n    ignoreList,\n    0,\n    0,\n    Infinity,\n    Infinity,\n  );\n\n  const joined: DecodedSourceMap = {\n    version: 3,\n    file: parsed.file,\n    names,\n    sources,\n    sourcesContent,\n    mappings,\n    ignoreList,\n  };\n\n  return presortedDecodedMap(joined);\n} as AnyMap;\n\nfunction parse<T>(map: T): Exclude<T, string> {\n  return typeof map === 'string' ? JSON.parse(map) : map;\n}\n\nfunction recurse(\n  input: SectionedSourceMapXInput,\n  mapUrl: string | null | undefined,\n  mappings: SourceMapSegment[][],\n  sources: string[],\n  sourcesContent: (string | null)[],\n  names: string[],\n  ignoreList: number[],\n  lineOffset: number,\n  columnOffset: number,\n  stopLine: number,\n  stopColumn: number,\n) {\n  const { sections } = input;\n  for (let i = 0; i < sections.length; i++) {\n    const { map, offset } = sections[i];\n\n    let sl = stopLine;\n    let sc = stopColumn;\n    if (i + 1 < sections.length) {\n      const nextOffset = sections[i + 1].offset;\n      sl = Math.min(stopLine, lineOffset + nextOffset.line);\n\n      if (sl === stopLine) {\n        sc = Math.min(stopColumn, columnOffset + nextOffset.column);\n      } else if (sl < stopLine) {\n        sc = columnOffset + nextOffset.column;\n      }\n    }\n\n    addSection(\n      map,\n      mapUrl,\n      mappings,\n      sources,\n      sourcesContent,\n      names,\n      ignoreList,\n      lineOffset + offset.line,\n      columnOffset + offset.column,\n      sl,\n      sc,\n    );\n  }\n}\n\nfunction addSection(\n  input: SectionXInput['map'],\n  mapUrl: string | null | undefined,\n  mappings: SourceMapSegment[][],\n  sources: string[],\n  sourcesContent: (string | null)[],\n  names: string[],\n  ignoreList: number[],\n  lineOffset: number,\n  columnOffset: number,\n  stopLine: number,\n  stopColumn: number,\n) {\n  const parsed = parse(input);\n  if ('sections' in parsed) return recurse(...(arguments as unknown as Parameters<typeof recurse>));\n\n  const map = new TraceMap(parsed, mapUrl);\n  const sourcesOffset = sources.length;\n  const namesOffset = names.length;\n  const decoded = decodedMappings(map);\n  const { resolvedSources, sourcesContent: contents, ignoreList: ignores } = map;\n\n  append(sources, resolvedSources);\n  append(names, map.names);\n\n  if (contents) append(sourcesContent, contents);\n  else for (let i = 0; i < resolvedSources.length; i++) sourcesContent.push(null);\n\n  if (ignores) for (let i = 0; i < ignores.length; i++) ignoreList.push(ignores[i] + sourcesOffset);\n\n  for (let i = 0; i < decoded.length; i++) {\n    const lineI = lineOffset + i;\n\n    // We can only add so many lines before we step into the range that the next section's map\n    // controls. When we get to the last line, then we'll start checking the segments to see if\n    // they've crossed into the column range. But it may not have any columns that overstep, so we\n    // still need to check that we don't overstep lines, too.\n    if (lineI > stopLine) return;\n\n    // The out line may already exist in mappings (if we're continuing the line started by a\n    // previous section). Or, we may have jumped ahead several lines to start this section.\n    const out = getLine(mappings, lineI);\n    // On the 0th loop, the section's column offset shifts us forward. On all other lines (since the\n    // map can be multiple lines), it doesn't.\n    const cOffset = i === 0 ? columnOffset : 0;\n\n    const line = decoded[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n      const column = cOffset + seg[COLUMN];\n\n      // If this segment steps into the column range that the next section's map controls, we need\n      // to stop early.\n      if (lineI === stopLine && column >= stopColumn) return;\n\n      if (seg.length === 1) {\n        out.push([column]);\n        continue;\n      }\n\n      const sourcesIndex = sourcesOffset + seg[SOURCES_INDEX];\n      const sourceLine = seg[SOURCE_LINE];\n      const sourceColumn = seg[SOURCE_COLUMN];\n      out.push(\n        seg.length === 4\n          ? [column, sourcesIndex, sourceLine, sourceColumn]\n          : [column, sourcesIndex, sourceLine, sourceColumn, namesOffset + seg[NAMES_INDEX]],\n      );\n    }\n  }\n}\n\nfunction append<T>(arr: T[], other: T[]) {\n  for (let i = 0; i < other.length; i++) arr.push(other[i]);\n}\n\nfunction getLine<T>(arr: T[][], index: number): T[] {\n  for (let i = arr.length; i <= index; i++) arr[i] = [];\n  return arr[index];\n}\n", "import { encode, decode } from '@jridgewell/sourcemap-codec';\n\nimport resolve from './resolve';\nimport stripFilename from './strip-filename';\nimport maybeSort from './sort';\nimport buildBySources from './by-source';\nimport {\n  memoizedState,\n  memoizedBinarySearch,\n  upperBound,\n  lowerBound,\n  found as bsFound,\n} from './binary-search';\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n  REV_GENERATED_LINE,\n  REV_GENERATED_COLUMN,\n} from './sourcemap-segment';\n\nimport type { SourceMapSegment, ReverseSegment } from './sourcemap-segment';\nimport type {\n  SourceMapV3,\n  DecodedSourceMap,\n  EncodedSourceMap,\n  InvalidOriginalMapping,\n  OriginalMapping,\n  InvalidGeneratedMapping,\n  GeneratedMapping,\n  SourceMapInput,\n  Needle,\n  SourceNeedle,\n  SourceMap,\n  EachMapping,\n  Bias,\n  XInput,\n} from './types';\nimport type { Source } from './by-source';\nimport type { MemoState } from './binary-search';\n\nexport type { SourceMapSegment } from './sourcemap-segment';\nexport type {\n  SourceMap,\n  DecodedSourceMap,\n  EncodedSourceMap,\n  Section,\n  SectionedSourceMap,\n  SourceMapV3,\n  Bias,\n  EachMapping,\n  GeneratedMapping,\n  InvalidGeneratedMapping,\n  InvalidOriginalMapping,\n  Needle,\n  OriginalMapping,\n  OriginalMapping as Mapping,\n  SectionedSourceMapInput,\n  SourceMapInput,\n  SourceNeedle,\n  XInput,\n  EncodedSourceMapXInput,\n  DecodedSourceMapXInput,\n  SectionedSourceMapXInput,\n  SectionXInput,\n} from './types';\n\ninterface PublicMap {\n  _encoded: TraceMap['_encoded'];\n  _decoded: TraceMap['_decoded'];\n  _decodedMemo: TraceMap['_decodedMemo'];\n  _bySources: TraceMap['_bySources'];\n  _bySourceMemos: TraceMap['_bySourceMemos'];\n}\n\nconst LINE_GTR_ZERO = '`line` must be greater than 0 (lines start at line 1)';\nconst COL_GTR_EQ_ZERO = '`column` must be greater than or equal to 0 (columns start at column 0)';\n\nexport const LEAST_UPPER_BOUND = -1;\nexport const GREATEST_LOWER_BOUND = 1;\n\nexport { AnyMap } from './any-map';\n\nexport class TraceMap implements SourceMap {\n  declare version: SourceMapV3['version'];\n  declare file: SourceMapV3['file'];\n  declare names: SourceMapV3['names'];\n  declare sourceRoot: SourceMapV3['sourceRoot'];\n  declare sources: SourceMapV3['sources'];\n  declare sourcesContent: SourceMapV3['sourcesContent'];\n  declare ignoreList: SourceMapV3['ignoreList'];\n\n  declare resolvedSources: string[];\n  private declare _encoded: string | undefined;\n\n  private declare _decoded: SourceMapSegment[][] | undefined;\n  private declare _decodedMemo: MemoState;\n\n  private declare _bySources: Source[] | undefined;\n  private declare _bySourceMemos: MemoState[] | undefined;\n\n  constructor(map: SourceMapInput, mapUrl?: string | null) {\n    const isString = typeof map === 'string';\n\n    if (!isString && (map as unknown as { _decodedMemo: any })._decodedMemo) return map as TraceMap;\n\n    const parsed = (isString ? JSON.parse(map) : map) as DecodedSourceMap | EncodedSourceMap;\n\n    const { version, file, names, sourceRoot, sources, sourcesContent } = parsed;\n    this.version = version;\n    this.file = file;\n    this.names = names || [];\n    this.sourceRoot = sourceRoot;\n    this.sources = sources;\n    this.sourcesContent = sourcesContent;\n    this.ignoreList = parsed.ignoreList || (parsed as XInput).x_google_ignoreList || undefined;\n\n    const from = resolve(sourceRoot || '', stripFilename(mapUrl));\n    this.resolvedSources = sources.map((s) => resolve(s || '', from));\n\n    const { mappings } = parsed;\n    if (typeof mappings === 'string') {\n      this._encoded = mappings;\n      this._decoded = undefined;\n    } else {\n      this._encoded = undefined;\n      this._decoded = maybeSort(mappings, isString);\n    }\n\n    this._decodedMemo = memoizedState();\n    this._bySources = undefined;\n    this._bySourceMemos = undefined;\n  }\n}\n\n/**\n * Typescript doesn't allow friend access to private fields, so this just casts the map into a type\n * with public access modifiers.\n */\nfunction cast(map: unknown): PublicMap {\n  return map as any;\n}\n\n/**\n * Returns the encoded (VLQ string) form of the SourceMap's mappings field.\n */\nexport function encodedMappings(map: TraceMap): EncodedSourceMap['mappings'] {\n  return (cast(map)._encoded ??= encode(cast(map)._decoded!));\n}\n\n/**\n * Returns the decoded (array of lines of segments) form of the SourceMap's mappings field.\n */\nexport function decodedMappings(map: TraceMap): Readonly<DecodedSourceMap['mappings']> {\n  return (cast(map)._decoded ||= decode(cast(map)._encoded!));\n}\n\n/**\n * A low-level API to find the segment associated with a generated line/column (think, from a\n * stack trace). Line and column here are 0-based, unlike `originalPositionFor`.\n */\nexport function traceSegment(\n  map: TraceMap,\n  line: number,\n  column: number,\n): Readonly<SourceMapSegment> | null {\n  const decoded = decodedMappings(map);\n\n  // It's common for parent source maps to have pointers to lines that have no\n  // mapping (like a \"//# sourceMappingURL=\") at the end of the child file.\n  if (line >= decoded.length) return null;\n\n  const segments = decoded[line];\n  const index = traceSegmentInternal(\n    segments,\n    cast(map)._decodedMemo,\n    line,\n    column,\n    GREATEST_LOWER_BOUND,\n  );\n\n  return index === -1 ? null : segments[index];\n}\n\n/**\n * A higher-level API to find the source/line/column associated with a generated line/column\n * (think, from a stack trace). Line is 1-based, but column is 0-based, due to legacy behavior in\n * `source-map` library.\n */\nexport function originalPositionFor(\n  map: TraceMap,\n  needle: Needle,\n): OriginalMapping | InvalidOriginalMapping {\n  let { line, column, bias } = needle;\n  line--;\n  if (line < 0) throw new Error(LINE_GTR_ZERO);\n  if (column < 0) throw new Error(COL_GTR_EQ_ZERO);\n\n  const decoded = decodedMappings(map);\n\n  // It's common for parent source maps to have pointers to lines that have no\n  // mapping (like a \"//# sourceMappingURL=\") at the end of the child file.\n  if (line >= decoded.length) return OMapping(null, null, null, null);\n\n  const segments = decoded[line];\n  const index = traceSegmentInternal(\n    segments,\n    cast(map)._decodedMemo,\n    line,\n    column,\n    bias || GREATEST_LOWER_BOUND,\n  );\n\n  if (index === -1) return OMapping(null, null, null, null);\n\n  const segment = segments[index];\n  if (segment.length === 1) return OMapping(null, null, null, null);\n\n  const { names, resolvedSources } = map;\n  return OMapping(\n    resolvedSources[segment[SOURCES_INDEX]],\n    segment[SOURCE_LINE] + 1,\n    segment[SOURCE_COLUMN],\n    segment.length === 5 ? names[segment[NAMES_INDEX]] : null,\n  );\n}\n\n/**\n * Finds the generated line/column position of the provided source/line/column source position.\n */\nexport function generatedPositionFor(\n  map: TraceMap,\n  needle: SourceNeedle,\n): GeneratedMapping | InvalidGeneratedMapping {\n  const { source, line, column, bias } = needle;\n  return generatedPosition(map, source, line, column, bias || GREATEST_LOWER_BOUND, false);\n}\n\n/**\n * Finds all generated line/column positions of the provided source/line/column source position.\n */\nexport function allGeneratedPositionsFor(map: TraceMap, needle: SourceNeedle): GeneratedMapping[] {\n  const { source, line, column, bias } = needle;\n  // SourceMapConsumer uses LEAST_UPPER_BOUND for some reason, so we follow suit.\n  return generatedPosition(map, source, line, column, bias || LEAST_UPPER_BOUND, true);\n}\n\n/**\n * Iterates each mapping in generated position order.\n */\nexport function eachMapping(map: TraceMap, cb: (mapping: EachMapping) => void): void {\n  const decoded = decodedMappings(map);\n  const { names, resolvedSources } = map;\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n\n      const generatedLine = i + 1;\n      const generatedColumn = seg[0];\n      let source = null;\n      let originalLine = null;\n      let originalColumn = null;\n      let name = null;\n      if (seg.length !== 1) {\n        source = resolvedSources[seg[1]];\n        originalLine = seg[2] + 1;\n        originalColumn = seg[3];\n      }\n      if (seg.length === 5) name = names[seg[4]];\n\n      cb({\n        generatedLine,\n        generatedColumn,\n        source,\n        originalLine,\n        originalColumn,\n        name,\n      } as EachMapping);\n    }\n  }\n}\n\nfunction sourceIndex(map: TraceMap, source: string): number {\n  const { sources, resolvedSources } = map;\n  let index = sources.indexOf(source);\n  if (index === -1) index = resolvedSources.indexOf(source);\n  return index;\n}\n\n/**\n * Retrieves the source content for a particular source, if its found. Returns null if not.\n */\nexport function sourceContentFor(map: TraceMap, source: string): string | null {\n  const { sourcesContent } = map;\n  if (sourcesContent == null) return null;\n  const index = sourceIndex(map, source);\n  return index === -1 ? null : sourcesContent[index];\n}\n\n/**\n * Determines if the source is marked to ignore by the source map.\n */\nexport function isIgnored(map: TraceMap, source: string): boolean {\n  const { ignoreList } = map;\n  if (ignoreList == null) return false;\n  const index = sourceIndex(map, source);\n  return index === -1 ? false : ignoreList.includes(index);\n}\n\n/**\n * A helper that skips sorting of the input map's mappings array, which can be expensive for larger\n * maps.\n */\nexport function presortedDecodedMap(map: DecodedSourceMap, mapUrl?: string): TraceMap {\n  const tracer = new TraceMap(clone(map, []), mapUrl);\n  cast(tracer)._decoded = map.mappings;\n  return tracer;\n}\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function decodedMap(\n  map: TraceMap,\n): Omit<DecodedSourceMap, 'mappings'> & { mappings: readonly SourceMapSegment[][] } {\n  return clone(map, decodedMappings(map));\n}\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function encodedMap(map: TraceMap): EncodedSourceMap {\n  return clone(map, encodedMappings(map));\n}\n\nfunction clone<T extends string | readonly SourceMapSegment[][]>(\n  map: TraceMap | DecodedSourceMap,\n  mappings: T,\n): T extends string ? EncodedSourceMap : DecodedSourceMap {\n  return {\n    version: map.version,\n    file: map.file,\n    names: map.names,\n    sourceRoot: map.sourceRoot,\n    sources: map.sources,\n    sourcesContent: map.sourcesContent,\n    mappings,\n    ignoreList: map.ignoreList || (map as XInput).x_google_ignoreList,\n  } as any;\n}\n\nfunction OMapping(source: null, line: null, column: null, name: null): InvalidOriginalMapping;\nfunction OMapping(\n  source: string,\n  line: number,\n  column: number,\n  name: string | null,\n): OriginalMapping;\nfunction OMapping(\n  source: string | null,\n  line: number | null,\n  column: number | null,\n  name: string | null,\n): OriginalMapping | InvalidOriginalMapping {\n  return { source, line, column, name } as any;\n}\n\nfunction GMapping(line: null, column: null): InvalidGeneratedMapping;\nfunction GMapping(line: number, column: number): GeneratedMapping;\nfunction GMapping(\n  line: number | null,\n  column: number | null,\n): GeneratedMapping | InvalidGeneratedMapping {\n  return { line, column } as any;\n}\n\nfunction traceSegmentInternal(\n  segments: SourceMapSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): number;\nfunction traceSegmentInternal(\n  segments: ReverseSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): number;\nfunction traceSegmentInternal(\n  segments: SourceMapSegment[] | ReverseSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): number {\n  let index = memoizedBinarySearch(segments, column, memo, line);\n  if (bsFound) {\n    index = (bias === LEAST_UPPER_BOUND ? upperBound : lowerBound)(segments, column, index);\n  } else if (bias === LEAST_UPPER_BOUND) index++;\n\n  if (index === -1 || index === segments.length) return -1;\n  return index;\n}\n\nfunction sliceGeneratedPositions(\n  segments: ReverseSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): GeneratedMapping[] {\n  let min = traceSegmentInternal(segments, memo, line, column, GREATEST_LOWER_BOUND);\n\n  // We ignored the bias when tracing the segment so that we're guarnateed to find the first (in\n  // insertion order) segment that matched. Even if we did respect the bias when tracing, we would\n  // still need to call `lowerBound()` to find the first segment, which is slower than just looking\n  // for the GREATEST_LOWER_BOUND to begin with. The only difference that matters for us is when the\n  // binary search didn't match, in which case GREATEST_LOWER_BOUND just needs to increment to\n  // match LEAST_UPPER_BOUND.\n  if (!bsFound && bias === LEAST_UPPER_BOUND) min++;\n\n  if (min === -1 || min === segments.length) return [];\n\n  // We may have found the segment that started at an earlier column. If this is the case, then we\n  // need to slice all generated segments that match _that_ column, because all such segments span\n  // to our desired column.\n  const matchedColumn = bsFound ? column : segments[min][COLUMN];\n\n  // The binary search is not guaranteed to find the lower bound when a match wasn't found.\n  if (!bsFound) min = lowerBound(segments, matchedColumn, min);\n  const max = upperBound(segments, matchedColumn, min);\n\n  const result = [];\n  for (; min <= max; min++) {\n    const segment = segments[min];\n    result.push(GMapping(segment[REV_GENERATED_LINE] + 1, segment[REV_GENERATED_COLUMN]));\n  }\n  return result;\n}\n\nfunction generatedPosition(\n  map: TraceMap,\n  source: string,\n  line: number,\n  column: number,\n  bias: Bias,\n  all: false,\n): GeneratedMapping | InvalidGeneratedMapping;\nfunction generatedPosition(\n  map: TraceMap,\n  source: string,\n  line: number,\n  column: number,\n  bias: Bias,\n  all: true,\n): GeneratedMapping[];\nfunction generatedPosition(\n  map: TraceMap,\n  source: string,\n  line: number,\n  column: number,\n  bias: Bias,\n  all: boolean,\n): GeneratedMapping | InvalidGeneratedMapping | GeneratedMapping[] {\n  line--;\n  if (line < 0) throw new Error(LINE_GTR_ZERO);\n  if (column < 0) throw new Error(COL_GTR_EQ_ZERO);\n\n  const { sources, resolvedSources } = map;\n  let sourceIndex = sources.indexOf(source);\n  if (sourceIndex === -1) sourceIndex = resolvedSources.indexOf(source);\n  if (sourceIndex === -1) return all ? [] : GMapping(null, null);\n\n  const generated = (cast(map)._bySources ||= buildBySources(\n    decodedMappings(map),\n    (cast(map)._bySourceMemos = sources.map(memoizedState)),\n  ));\n\n  const segments = generated[sourceIndex][line];\n  if (segments == null) return all ? [] : GMapping(null, null);\n\n  const memo = cast(map)._bySourceMemos![sourceIndex];\n\n  if (all) return sliceGeneratedPositions(segments, memo, line, column, bias);\n\n  const index = traceSegmentInternal(segments, memo, line, column, bias);\n  if (index === -1) return GMapping(null, null);\n\n  const segment = segments[index];\n  return GMapping(segment[REV_GENERATED_LINE] + 1, segment[REV_GENERATED_COLUMN]);\n}\n", "type Key = string | number | symbol;\n\n/**\n * SetArray acts like a `Set` (allowing only one occurrence of a string `key`), but provides the\n * index of the `key` in the backing array.\n *\n * This is designed to allow synchronizing a second array with the contents of the backing array,\n * like how in a sourcemap `sourcesContent[i]` is the source content associated with `source[i]`,\n * and there are never duplicates.\n */\nexport class SetArray<T extends Key = Key> {\n  private declare _indexes: Record<T, number | undefined>;\n  declare array: readonly T[];\n\n  constructor() {\n    this._indexes = { __proto__: null } as any;\n    this.array = [];\n  }\n}\n\ninterface PublicSet<T extends Key> {\n  array: T[];\n  _indexes: SetArray<T>['_indexes'];\n}\n\n/**\n * Typescript doesn't allow friend access to private fields, so this just casts the set into a type\n * with public access modifiers.\n */\nfunction cast<T extends Key>(set: SetArray<T>): PublicSet<T> {\n  return set as any;\n}\n\n/**\n * Gets the index associated with `key` in the backing array, if it is already present.\n */\nexport function get<T extends Key>(setarr: SetArray<T>, key: T): number | undefined {\n  return cast(setarr)._indexes[key];\n}\n\n/**\n * Puts `key` into the backing array, if it is not already present. Returns\n * the index of the `key` in the backing array.\n */\nexport function put<T extends Key>(setarr: SetArray<T>, key: T): number {\n  // The key may or may not be present. If it is present, it's a number.\n  const index = get(setarr, key);\n  if (index !== undefined) return index;\n\n  const { array, _indexes: indexes } = cast(setarr);\n\n  const length = array.push(key);\n  return (indexes[key] = length - 1);\n}\n\n/**\n * Pops the last added item out of the SetArray.\n */\nexport function pop<T extends Key>(setarr: SetArray<T>): void {\n  const { array, _indexes: indexes } = cast(setarr);\n  if (array.length === 0) return;\n\n  const last = array.pop()!;\n  indexes[last] = undefined;\n}\n\n/**\n * Removes the key, if it exists in the set.\n */\nexport function remove<T extends Key>(setarr: SetArray<T>, key: T): void {\n  const index = get(setarr, key);\n  if (index === undefined) return;\n\n  const { array, _indexes: indexes } = cast(setarr);\n  for (let i = index + 1; i < array.length; i++) {\n    const k = array[i];\n    array[i - 1] = k;\n    indexes[k]!--;\n  }\n  indexes[key] = undefined;\n  array.pop();\n}\n", "type GeneratedColumn = number;\ntype SourcesIndex = number;\ntype SourceLine = number;\ntype SourceColumn = number;\ntype NamesIndex = number;\n\nexport type SourceMapSegment =\n  | [GeneratedColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn, NamesIndex];\n\nexport const COLUMN = 0;\nexport const SOURCES_INDEX = 1;\nexport const SOURCE_LINE = 2;\nexport const SOURCE_COLUMN = 3;\nexport const NAMES_INDEX = 4;\n", "import { SetArray, put, remove } from '@jridgewell/set-array';\nimport { encode } from '@jridgewell/sourcemap-codec';\nimport { TraceMap, decodedMappings } from '@jridgewell/trace-mapping';\n\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n} from './sourcemap-segment';\n\nimport type { SourceMapInput } from '@jridgewell/trace-mapping';\nimport type { SourceMapSegment } from './sourcemap-segment';\nimport type { DecodedSourceMap, EncodedSourceMap, Pos, Mapping } from './types';\n\nexport type { DecodedSourceMap, EncodedSourceMap, Mapping };\n\nexport type Options = {\n  file?: string | null;\n  sourceRoot?: string | null;\n};\n\nconst NO_NAME = -1;\n\n/**\n * Provides the state to generate a sourcemap.\n */\nexport class GenMapping {\n  private declare _names: SetArray<string>;\n  private declare _sources: SetArray<string>;\n  private declare _sourcesContent: (string | null)[];\n  private declare _mappings: SourceMapSegment[][];\n  private declare _ignoreList: SetArray<number>;\n  declare file: string | null | undefined;\n  declare sourceRoot: string | null | undefined;\n\n  constructor({ file, sourceRoot }: Options = {}) {\n    this._names = new SetArray();\n    this._sources = new SetArray();\n    this._sourcesContent = [];\n    this._mappings = [];\n    this.file = file;\n    this.sourceRoot = sourceRoot;\n    this._ignoreList = new SetArray();\n  }\n}\n\ninterface PublicMap {\n  _names: GenMapping['_names'];\n  _sources: GenMapping['_sources'];\n  _sourcesContent: GenMapping['_sourcesContent'];\n  _mappings: GenMapping['_mappings'];\n  _ignoreList: GenMapping['_ignoreList'];\n}\n\n/**\n * Typescript doesn't allow friend access to private fields, so this just casts the map into a type\n * with public access modifiers.\n */\nfunction cast(map: unknown): PublicMap {\n  return map as any;\n}\n\n/**\n * A low-level API to associate a generated position with an original source position. Line and\n * column here are 0-based, unlike `addMapping`.\n */\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source?: null,\n  sourceLine?: null,\n  sourceColumn?: null,\n  name?: null,\n  content?: null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: string,\n  sourceLine: number,\n  sourceColumn: number,\n  name?: null,\n  content?: string | null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: string,\n  sourceLine: number,\n  sourceColumn: number,\n  name: string,\n  content?: string | null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source?: string | null,\n  sourceLine?: number | null,\n  sourceColumn?: number | null,\n  name?: string | null,\n  content?: string | null,\n): void {\n  return addSegmentInternal(\n    false,\n    map,\n    genLine,\n    genColumn,\n    source,\n    sourceLine,\n    sourceColumn,\n    name,\n    content,\n  );\n}\n\n/**\n * A high-level API to associate a generated position with an original source position. Line is\n * 1-based, but column is 0-based, due to legacy behavior in `source-map` library.\n */\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source?: null;\n    original?: null;\n    name?: null;\n    content?: null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: string;\n    original: Pos;\n    name?: null;\n    content?: string | null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: string;\n    original: Pos;\n    name: string;\n    content?: string | null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source?: string | null;\n    original?: Pos | null;\n    name?: string | null;\n    content?: string | null;\n  },\n): void {\n  return addMappingInternal(false, map, mapping as Parameters<typeof addMappingInternal>[2]);\n}\n\n/**\n * Same as `addSegment`, but will only add the segment if it generates useful information in the\n * resulting map. This only works correctly if segments are added **in order**, meaning you should\n * not add a segment with a lower generated line/column than one that came before.\n */\nexport const maybeAddSegment: typeof addSegment = (\n  map,\n  genLine,\n  genColumn,\n  source,\n  sourceLine,\n  sourceColumn,\n  name,\n  content,\n) => {\n  return addSegmentInternal(\n    true,\n    map,\n    genLine,\n    genColumn,\n    source,\n    sourceLine,\n    sourceColumn,\n    name,\n    content,\n  );\n};\n\n/**\n * Same as `addMapping`, but will only add the mapping if it generates useful information in the\n * resulting map. This only works correctly if mappings are added **in order**, meaning you should\n * not add a mapping with a lower generated line/column than one that came before.\n */\nexport const maybeAddMapping: typeof addMapping = (map, mapping) => {\n  return addMappingInternal(true, map, mapping as Parameters<typeof addMappingInternal>[2]);\n};\n\n/**\n * Adds/removes the content of the source file to the source map.\n */\nexport function setSourceContent(map: GenMapping, source: string, content: string | null): void {\n  const { _sources: sources, _sourcesContent: sourcesContent } = cast(map);\n  const index = put(sources, source);\n  sourcesContent[index] = content;\n}\n\nexport function setIgnore(map: GenMapping, source: string, ignore = true) {\n  const { _sources: sources, _sourcesContent: sourcesContent, _ignoreList: ignoreList } = cast(map);\n  const index = put(sources, source);\n  if (index === sourcesContent.length) sourcesContent[index] = null;\n  if (ignore) put(ignoreList, index);\n  else remove(ignoreList, index);\n}\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function toDecodedMap(map: GenMapping): DecodedSourceMap {\n  const {\n    _mappings: mappings,\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _names: names,\n    _ignoreList: ignoreList,\n  } = cast(map);\n  removeEmptyFinalLines(mappings);\n\n  return {\n    version: 3,\n    file: map.file || undefined,\n    names: names.array,\n    sourceRoot: map.sourceRoot || undefined,\n    sources: sources.array,\n    sourcesContent,\n    mappings,\n    ignoreList: ignoreList.array,\n  };\n}\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function toEncodedMap(map: GenMapping): EncodedSourceMap {\n  const decoded = toDecodedMap(map);\n  return {\n    ...decoded,\n    mappings: encode(decoded.mappings as SourceMapSegment[][]),\n  };\n}\n\n/**\n * Constructs a new GenMapping, using the already present mappings of the input.\n */\nexport function fromMap(input: SourceMapInput): GenMapping {\n  const map = new TraceMap(input);\n  const gen = new GenMapping({ file: map.file, sourceRoot: map.sourceRoot });\n\n  putAll(cast(gen)._names, map.names);\n  putAll(cast(gen)._sources, map.sources as string[]);\n  cast(gen)._sourcesContent = map.sourcesContent || map.sources.map(() => null);\n  cast(gen)._mappings = decodedMappings(map) as GenMapping['_mappings'];\n  if (map.ignoreList) putAll(cast(gen)._ignoreList, map.ignoreList);\n\n  return gen;\n}\n\n/**\n * Returns an array of high-level mapping objects for every recorded segment, which could then be\n * passed to the `source-map` library.\n */\nexport function allMappings(map: GenMapping): Mapping[] {\n  const out: Mapping[] = [];\n  const { _mappings: mappings, _sources: sources, _names: names } = cast(map);\n\n  for (let i = 0; i < mappings.length; i++) {\n    const line = mappings[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n\n      const generated = { line: i + 1, column: seg[COLUMN] };\n      let source: string | undefined = undefined;\n      let original: Pos | undefined = undefined;\n      let name: string | undefined = undefined;\n\n      if (seg.length !== 1) {\n        source = sources.array[seg[SOURCES_INDEX]];\n        original = { line: seg[SOURCE_LINE] + 1, column: seg[SOURCE_COLUMN] };\n\n        if (seg.length === 5) name = names.array[seg[NAMES_INDEX]];\n      }\n\n      out.push({ generated, source, original, name } as Mapping);\n    }\n  }\n\n  return out;\n}\n\n// This split declaration is only so that terser can elminiate the static initialization block.\nfunction addSegmentInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: S,\n  sourceLine: S extends string ? number : null | undefined,\n  sourceColumn: S extends string ? number : null | undefined,\n  name: S extends string ? string | null | undefined : null | undefined,\n  content: S extends string ? string | null | undefined : null | undefined,\n): void {\n  const {\n    _mappings: mappings,\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _names: names,\n  } = cast(map);\n  const line = getLine(mappings, genLine);\n  const index = getColumnIndex(line, genColumn);\n\n  if (!source) {\n    if (skipable && skipSourceless(line, index)) return;\n    return insert(line, index, [genColumn]);\n  }\n\n  // Sigh, TypeScript can't figure out sourceLine and sourceColumn aren't nullish if source\n  // isn't nullish.\n  assert<number>(sourceLine);\n  assert<number>(sourceColumn);\n\n  const sourcesIndex = put(sources, source);\n  const namesIndex = name ? put(names, name) : NO_NAME;\n  if (sourcesIndex === sourcesContent.length) sourcesContent[sourcesIndex] = content ?? null;\n\n  if (skipable && skipSource(line, index, sourcesIndex, sourceLine, sourceColumn, namesIndex)) {\n    return;\n  }\n\n  return insert(\n    line,\n    index,\n    name\n      ? [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex]\n      : [genColumn, sourcesIndex, sourceLine, sourceColumn],\n  );\n}\n\nfunction assert<T>(_val: unknown): asserts _val is T {\n  // noop.\n}\n\nfunction getLine(mappings: SourceMapSegment[][], index: number): SourceMapSegment[] {\n  for (let i = mappings.length; i <= index; i++) {\n    mappings[i] = [];\n  }\n  return mappings[index];\n}\n\nfunction getColumnIndex(line: SourceMapSegment[], genColumn: number): number {\n  let index = line.length;\n  for (let i = index - 1; i >= 0; index = i--) {\n    const current = line[i];\n    if (genColumn >= current[COLUMN]) break;\n  }\n  return index;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n\nfunction removeEmptyFinalLines(mappings: SourceMapSegment[][]) {\n  const { length } = mappings;\n  let len = length;\n  for (let i = len - 1; i >= 0; len = i, i--) {\n    if (mappings[i].length > 0) break;\n  }\n  if (len < length) mappings.length = len;\n}\n\nfunction putAll<T extends string | number>(setarr: SetArray<T>, array: T[]) {\n  for (let i = 0; i < array.length; i++) put(setarr, array[i]);\n}\n\nfunction skipSourceless(line: SourceMapSegment[], index: number): boolean {\n  // The start of a line is already sourceless, so adding a sourceless segment to the beginning\n  // doesn't generate any useful information.\n  if (index === 0) return true;\n\n  const prev = line[index - 1];\n  // If the previous segment is also sourceless, then adding another sourceless segment doesn't\n  // genrate any new information. Else, this segment will end the source/named segment and point to\n  // a sourceless position, which is useful.\n  return prev.length === 1;\n}\n\nfunction skipSource(\n  line: SourceMapSegment[],\n  index: number,\n  sourcesIndex: number,\n  sourceLine: number,\n  sourceColumn: number,\n  namesIndex: number,\n): boolean {\n  // A source/named segment at the start of a line gives position at that genColumn\n  if (index === 0) return false;\n\n  const prev = line[index - 1];\n\n  // If the previous segment is sourceless, then we're transitioning to a source.\n  if (prev.length === 1) return false;\n\n  // If the previous segment maps to the exact same source position, then this segment doesn't\n  // provide any new position information.\n  return (\n    sourcesIndex === prev[SOURCES_INDEX] &&\n    sourceLine === prev[SOURCE_LINE] &&\n    sourceColumn === prev[SOURCE_COLUMN] &&\n    namesIndex === (prev.length === 5 ? prev[NAMES_INDEX] : NO_NAME)\n  );\n}\n\nfunction addMappingInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: S;\n    original: S extends string ? Pos : null | undefined;\n    name: S extends string ? string | null | undefined : null | undefined;\n    content: S extends string ? string | null | undefined : null | undefined;\n  },\n) {\n  const { generated, source, original, name, content } = mapping;\n  if (!source) {\n    return addSegmentInternal(\n      skipable,\n      map,\n      generated.line - 1,\n      generated.column,\n      null,\n      null,\n      null,\n      null,\n      null,\n    );\n  }\n  assert<Pos>(original);\n  return addSegmentInternal(\n    skipable,\n    map,\n    generated.line - 1,\n    generated.column,\n    source as string,\n    original.line - 1,\n    original.column,\n    name,\n    content,\n  );\n}\n", "import { GenMapping, maybeAddSegment, setIgnore, setSourceContent } from '@jridgewell/gen-mapping';\nimport { traceSegment, decodedMappings } from '@jridgewell/trace-mapping';\n\nimport type { TraceMap } from '@jridgewell/trace-mapping';\n\nexport type SourceMapSegmentObject = {\n  column: number;\n  line: number;\n  name: string;\n  source: string;\n  content: string | null;\n  ignore: boolean;\n};\n\nexport type OriginalSource = {\n  map: null;\n  sources: Sources[];\n  source: string;\n  content: string | null;\n  ignore: boolean;\n};\n\nexport type MapSource = {\n  map: TraceMap;\n  sources: Sources[];\n  source: string;\n  content: null;\n  ignore: false;\n};\n\nexport type Sources = OriginalSource | MapSource;\n\nconst SOURCELESS_MAPPING = /* #__PURE__ */ SegmentObject('', -1, -1, '', null, false);\nconst EMPTY_SOURCES: Sources[] = [];\n\nfunction SegmentObject(\n  source: string,\n  line: number,\n  column: number,\n  name: string,\n  content: string | null,\n  ignore: boolean\n): SourceMapSegmentObject {\n  return { source, line, column, name, content, ignore };\n}\n\nfunction Source(\n  map: TraceMap,\n  sources: Sources[],\n  source: '',\n  content: null,\n  ignore: false\n): MapSource;\nfunction Source(\n  map: null,\n  sources: Sources[],\n  source: string,\n  content: string | null,\n  ignore: boolean\n): OriginalSource;\nfunction Source(\n  map: TraceMap | null,\n  sources: Sources[],\n  source: string | '',\n  content: string | null,\n  ignore: boolean\n): Sources {\n  return {\n    map,\n    sources,\n    source,\n    content,\n    ignore,\n  } as any;\n}\n\n/**\n * MapSource represents a single sourcemap, with the ability to trace mappings into its child nodes\n * (which may themselves be SourceMapTrees).\n */\nexport function MapSource(map: TraceMap, sources: Sources[]): MapSource {\n  return Source(map, sources, '', null, false);\n}\n\n/**\n * A \"leaf\" node in the sourcemap tree, representing an original, unmodified source file. Recursive\n * segment tracing ends at the `OriginalSource`.\n */\nexport function OriginalSource(\n  source: string,\n  content: string | null,\n  ignore: boolean\n): OriginalSource {\n  return Source(null, EMPTY_SOURCES, source, content, ignore);\n}\n\n/**\n * traceMappings is only called on the root level SourceMapTree, and begins the process of\n * resolving each mapping in terms of the original source files.\n */\nexport function traceMappings(tree: MapSource): GenMapping {\n  // TODO: Eventually support sourceRoot, which has to be removed because the sources are already\n  // fully resolved. We'll need to make sources relative to the sourceRoot before adding them.\n  const gen = new GenMapping({ file: tree.map.file });\n  const { sources: rootSources, map } = tree;\n  const rootNames = map.names;\n  const rootMappings = decodedMappings(map);\n\n  for (let i = 0; i < rootMappings.length; i++) {\n    const segments = rootMappings[i];\n\n    for (let j = 0; j < segments.length; j++) {\n      const segment = segments[j];\n      const genCol = segment[0];\n      let traced: SourceMapSegmentObject | null = SOURCELESS_MAPPING;\n\n      // 1-length segments only move the current generated column, there's no source information\n      // to gather from it.\n      if (segment.length !== 1) {\n        const source = rootSources[segment[1]];\n        traced = originalPositionFor(\n          source,\n          segment[2],\n          segment[3],\n          segment.length === 5 ? rootNames[segment[4]] : ''\n        );\n\n        // If the trace is invalid, then the trace ran into a sourcemap that doesn't contain a\n        // respective segment into an original source.\n        if (traced == null) continue;\n      }\n\n      const { column, line, name, content, source, ignore } = traced;\n\n      maybeAddSegment(gen, i, genCol, source, line, column, name);\n      if (source && content != null) setSourceContent(gen, source, content);\n      if (ignore) setIgnore(gen, source, true);\n    }\n  }\n\n  return gen;\n}\n\n/**\n * originalPositionFor is only called on children SourceMapTrees. It recurses down into its own\n * child SourceMapTrees, until we find the original source map.\n */\nexport function originalPositionFor(\n  source: Sources,\n  line: number,\n  column: number,\n  name: string\n): SourceMapSegmentObject | null {\n  if (!source.map) {\n    return SegmentObject(source.source, line, column, name, source.content, source.ignore);\n  }\n\n  const segment = traceSegment(source.map, line, column);\n\n  // If we couldn't find a segment, then this doesn't exist in the sourcemap.\n  if (segment == null) return null;\n  // 1-length segments only move the current generated column, there's no source information\n  // to gather from it.\n  if (segment.length === 1) return SOURCELESS_MAPPING;\n\n  return originalPositionFor(\n    source.sources[segment[1]],\n    segment[2],\n    segment[3],\n    segment.length === 5 ? source.map.names[segment[4]] : name\n  );\n}\n", "import { TraceMap } from '@jridgewell/trace-mapping';\n\nimport { OriginalSource, MapSource } from './source-map-tree';\n\nimport type { Sources, MapSource as MapSourceType } from './source-map-tree';\nimport type { SourceMapInput, SourceMapLoader, LoaderContext } from './types';\n\nfunction asArray<T>(value: T | T[]): T[] {\n  if (Array.isArray(value)) return value;\n  return [value];\n}\n\n/**\n * Recursively builds a tree structure out of sourcemap files, with each node\n * being either an `OriginalSource` \"leaf\" or a `SourceMapTree` composed of\n * `OriginalSource`s and `SourceMapTree`s.\n *\n * Every sourcemap is composed of a collection of source files and mappings\n * into locations of those source files. When we generate a `SourceMapTree` for\n * the sourcemap, we attempt to load each source file's own sourcemap. If it\n * does not have an associated sourcemap, it is considered an original,\n * unmodified source file.\n */\nexport default function buildSourceMapTree(\n  input: SourceMapInput | SourceMapInput[],\n  loader: SourceMapLoader\n): MapSourceType {\n  const maps = asArray(input).map((m) => new TraceMap(m, ''));\n  const map = maps.pop()!;\n\n  for (let i = 0; i < maps.length; i++) {\n    if (maps[i].sources.length > 1) {\n      throw new Error(\n        `Transformation map ${i} must have exactly one source file.\\n` +\n          'Did you specify these with the most recent transformation maps first?'\n      );\n    }\n  }\n\n  let tree = build(map, loader, '', 0);\n  for (let i = maps.length - 1; i >= 0; i--) {\n    tree = MapSource(maps[i], [tree]);\n  }\n  return tree;\n}\n\nfunction build(\n  map: TraceMap,\n  loader: SourceMapLoader,\n  importer: string,\n  importerDepth: number\n): MapSourceType {\n  const { resolvedSources, sourcesContent, ignoreList } = map;\n\n  const depth = importerDepth + 1;\n  const children = resolvedSources.map((sourceFile: string | null, i: number): Sources => {\n    // The loading context gives the loader more information about why this file is being loaded\n    // (eg, from which importer). It also allows the loader to override the location of the loaded\n    // sourcemap/original source, or to override the content in the sourcesContent field if it's\n    // an unmodified source file.\n    const ctx: LoaderContext = {\n      importer,\n      depth,\n      source: sourceFile || '',\n      content: undefined,\n      ignore: undefined,\n    };\n\n    // Use the provided loader callback to retrieve the file's sourcemap.\n    // TODO: We should eventually support async loading of sourcemap files.\n    const sourceMap = loader(ctx.source, ctx);\n\n    const { source, content, ignore } = ctx;\n\n    // If there is a sourcemap, then we need to recurse into it to load its source files.\n    if (sourceMap) return build(new TraceMap(sourceMap, source), loader, source, depth);\n\n    // Else, it's an unmodified source file.\n    // The contents of this unmodified source file can be overridden via the loader context,\n    // allowing it to be explicitly null or a string. If it remains undefined, we fall back to\n    // the importing sourcemap's `sourcesContent` field.\n    const sourceContent =\n      content !== undefined ? content : sourcesContent ? sourcesContent[i] : null;\n    const ignored = ignore !== undefined ? ignore : ignoreList ? ignoreList.includes(i) : false;\n    return OriginalSource(source, sourceContent, ignored);\n  });\n\n  return MapSource(map, children);\n}\n", "import { toDecodedMap, toEncodedMap } from '@jridgewell/gen-mapping';\n\nimport type { GenMapping } from '@jridgewell/gen-mapping';\nimport type { DecodedSourceMap, EncodedSourceMap, Options } from './types';\n\n/**\n * A SourceMap v3 compatible sourcemap, which only includes fields that were\n * provided to it.\n */\nexport default class SourceMap {\n  declare file?: string | null;\n  declare mappings: EncodedSourceMap['mappings'] | DecodedSourceMap['mappings'];\n  declare sourceRoot?: string;\n  declare names: string[];\n  declare sources: (string | null)[];\n  declare sourcesContent?: (string | null)[];\n  declare version: 3;\n  declare ignoreList: number[] | undefined;\n\n  constructor(map: GenMapping, options: Options) {\n    const out = options.decodedMappings ? toDecodedMap(map) : toEncodedMap(map);\n    this.version = out.version; // SourceMap spec says this should be first.\n    this.file = out.file;\n    this.mappings = out.mappings as SourceMap['mappings'];\n    this.names = out.names as SourceMap['names'];\n    this.ignoreList = out.ignoreList as SourceMap['ignoreList'];\n    this.sourceRoot = out.sourceRoot;\n\n    this.sources = out.sources as SourceMap['sources'];\n    if (!options.excludeContent) {\n      this.sourcesContent = out.sourcesContent as SourceMap['sourcesContent'];\n    }\n  }\n\n  toString(): string {\n    return JSON.stringify(this);\n  }\n}\n", "import buildSourceMapTree from './build-source-map-tree';\nimport { traceMappings } from './source-map-tree';\nimport SourceMap from './source-map';\n\nimport type { SourceMapInput, SourceMapLoader, Options } from './types';\nexport type {\n  SourceMapSegment,\n  EncodedSourceMap,\n  EncodedSourceMap as RawSourceMap,\n  DecodedSourceMap,\n  SourceMapInput,\n  SourceMapLoader,\n  LoaderContext,\n  Options,\n} from './types';\nexport type { SourceMap };\n\n/**\n * Traces through all the mappings in the root sourcemap, through the sources\n * (and their sourcemaps), all the way back to the original source location.\n *\n * `loader` will be called every time we encounter a source file. If it returns\n * a sourcemap, we will recurse into that sourcemap to continue the trace. If\n * it returns a falsey value, that source file is treated as an original,\n * unmodified source file.\n *\n * Pass `excludeContent` to exclude any self-containing source file content\n * from the output sourcemap.\n *\n * Pass `decodedMappings` to receive a SourceMap with decoded (instead of\n * VLQ encoded) mappings.\n */\nexport default function remapping(\n  input: SourceMapInput | SourceMapInput[],\n  loader: SourceMapLoader,\n  options?: boolean | Options\n): SourceMap {\n  const opts =\n    typeof options === 'object' ? options : { excludeContent: !!options, decodedMappings: false };\n  const tree = buildSourceMapTree(input, loader);\n  return new SourceMap(traceMappings(tree), opts);\n}\n"], "mappings": ";;;;;;;;;;;;;;AACA,YAAM,cAAc;AAYpB,YAAM,WAAW;AAWjB,YAAM,YAAY;AAuBlB,eAAS,cAAc,OAAa;AAClC,eAAO,YAAY,KAAK,KAAK;MAC/B;AAEA,eAAS,oBAAoB,OAAa;AACxC,eAAO,MAAM,WAAW,IAAI;MAC9B;AAEA,eAAS,eAAe,OAAa;AACnC,eAAO,MAAM,WAAW,GAAG;MAC7B;AAEA,eAAS,UAAU,OAAa;AAC9B,eAAO,MAAM,WAAW,OAAO;MACjC;AAEA,eAAS,WAAW,OAAa;AAC/B,eAAO,SAAS,KAAK,KAAK;MAC5B;AAEA,eAAS,iBAAiB,OAAa;AACrC,cAAM,QAAQ,SAAS,KAAK,KAAK;AACjC,eAAO,QACL,MAAM,CAAC,GACP,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,GACP,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,KAAK,KACZ,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,KAAK,EAAE;MAElB;AAEA,eAAS,aAAa,OAAa;AACjC,cAAM,QAAQ,UAAU,KAAK,KAAK;AAClC,cAAM,OAAO,MAAM,CAAC;AACpB,eAAO,QACL,SACA,IACA,MAAM,CAAC,KAAK,IACZ,IACA,eAAe,IAAI,IAAI,OAAO,MAAM,MACpC,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,KAAK,EAAE;MAElB;AAEA,eAAS,QACP,QACA,MACA,MACA,MACA,MACA,OACA,MAAY;AAEZ,eAAO;UACL;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAI;;MAER;AAEA,eAAS,SAAS,OAAa;AAC7B,YAAI,oBAAoB,KAAK,GAAG;AAC9B,gBAAMA,OAAM,iBAAiB,UAAU,KAAK;AAC5C,UAAAA,KAAI,SAAS;AACb,UAAAA,KAAI,OAAI;AACR,iBAAOA;;AAGT,YAAI,eAAe,KAAK,GAAG;AACzB,gBAAMA,OAAM,iBAAiB,mBAAmB,KAAK;AACrD,UAAAA,KAAI,SAAS;AACb,UAAAA,KAAI,OAAO;AACX,UAAAA,KAAI,OAAI;AACR,iBAAOA;;AAGT,YAAI,UAAU,KAAK;AAAG,iBAAO,aAAa,KAAK;AAE/C,YAAI,cAAc,KAAK;AAAG,iBAAO,iBAAiB,KAAK;AAEvD,cAAM,MAAM,iBAAiB,oBAAoB,KAAK;AACtD,YAAI,SAAS;AACb,YAAI,OAAO;AACX,YAAI,OAAO,QACP,MAAM,WAAW,GAAG,QAElB,MAAM,WAAW,GAAG;AAI1B,eAAO;MACT;AAEA,eAAS,kBAAkB,MAAY;AAGrC,YAAI,KAAK,SAAS,KAAK;AAAG,iBAAO;AACjC,cAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,eAAO,KAAK,MAAM,GAAG,QAAQ,CAAC;MAChC;AAEA,eAAS,WAAW,KAAU,MAAS;AACrC,sBAAc,MAAM,KAAK,IAAI;AAI7B,YAAI,IAAI,SAAS,KAAK;AACpB,cAAI,OAAO,KAAK;eACX;AAEL,cAAI,OAAO,kBAAkB,KAAK,IAAI,IAAI,IAAI;;MAElD;AAMA,eAAS,cAAc,KAAU,MAAa;AAC5C,cAAM,MAAM,QAAI;AAChB,cAAM,SAAS,IAAI,KAAK,MAAM,GAAG;AAIjC,YAAI,UAAU;AAId,YAAI,WAAW;AAKf,YAAI,mBAAmB;AAEvB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAM,QAAQ,OAAO,CAAC;AAGtB,cAAI,CAAC,OAAO;AACV,+BAAmB;AACnB;;AAIF,6BAAmB;AAGnB,cAAI,UAAU;AAAK;AAInB,cAAI,UAAU,MAAM;AAClB,gBAAI,UAAU;AACZ,iCAAmB;AACnB;AACA;uBACS,KAAK;AAGd,qBAAO,SAAS,IAAI;;AAEtB;;AAKF,iBAAO,SAAS,IAAI;AACpB;;AAGF,YAAI,OAAO;AACX,iBAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,kBAAQ,MAAM,OAAO,CAAC;;AAExB,YAAI,CAAC,QAAS,oBAAoB,CAAC,KAAK,SAAS,KAAK,GAAI;AACxD,kBAAQ;;AAEV,YAAI,OAAO;MACb;eAKwB,QAAQ,OAAe,MAAwB;AACrE,YAAI,CAAC,SAAS,CAAC;AAAM,iBAAO;AAE5B,cAAM,MAAM,SAAS,KAAK;AAC1B,YAAI,YAAY,IAAI;AAEpB,YAAI,QAAQ,cAAS,GAAuB;AAC1C,gBAAM,UAAU,SAAS,IAAI;AAC7B,gBAAM,WAAW,QAAQ;AAEzB,kBAAQ,WAAS;YACf,KAAA;AACE,kBAAI,OAAO,QAAQ;;YAGrB,KAAA;AACE,kBAAI,QAAQ,QAAQ;;YAGtB,KAAA;YACA,KAAA;AACE,yBAAW,KAAK,OAAO;;YAGzB,KAAA;AAEE,kBAAI,OAAO,QAAQ;AACnB,kBAAI,OAAO,QAAQ;AACnB,kBAAI,OAAO,QAAQ;;YAGrB,KAAA;AAEE,kBAAI,SAAS,QAAQ;;AAEzB,cAAI,WAAW;AAAW,wBAAY;;AAGxC,sBAAc,KAAK,SAAS;AAE5B,cAAM,YAAY,IAAI,QAAQ,IAAI;AAClC,gBAAQ,WAAS;;;UAIf,KAAA;UACA,KAAA;AACE,mBAAO;UAET,KAAA,GAA2B;AAEzB,kBAAM,OAAO,IAAI,KAAK,MAAM,CAAC;AAE7B,gBAAI,CAAC;AAAM,qBAAO,aAAa;AAE/B,gBAAI,WAAW,QAAQ,KAAK,KAAK,CAAC,WAAW,IAAI,GAAG;AAIlD,qBAAO,OAAO,OAAO;;AAGvB,mBAAO,OAAO;;UAGhB,KAAA;AACE,mBAAO,IAAI,OAAO;UAEpB;AACE,mBAAO,IAAI,SAAS,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO;;MAE7E;;;;;;;;;;;;;ACpTc,eAAU,QAAQ,OAAe,MAAwB;AAIrE,YAAI,QAAQ,CAAC,KAAK,SAAS,GAAG;AAAG,kBAAQ;AAEzC,eAAO,WAAW,OAAO,IAAI;MAC/B;ACNwB,eAAA,cAAc,MAA+B;AACnE,YAAI,CAAC;AAAM,iBAAO;AAClB,cAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,eAAO,KAAK,MAAM,GAAG,QAAQ,CAAC;MAChC;ACQO,YAAM,SAAS;AACf,YAAM,gBAAgB;AACtB,YAAM,cAAc;AACpB,YAAM,gBAAgB;AACtB,YAAM,cAAc;AAEpB,YAAM,qBAAqB;AAC3B,YAAM,uBAAuB;AClBtB,eAAU,UACtB,UACA,OAAc;AAEd,cAAM,gBAAgB,wBAAwB,UAAU,CAAC;AACzD,YAAI,kBAAkB,SAAS;AAAQ,iBAAO;AAI9C,YAAI,CAAC;AAAO,qBAAW,SAAS,MAAK;AAErC,iBAAS,IAAI,eAAe,IAAI,SAAS,QAAQ,IAAI,wBAAwB,UAAU,IAAI,CAAC,GAAG;AAC7F,mBAAS,CAAC,IAAI,aAAa,SAAS,CAAC,GAAG,KAAK;;AAE/C,eAAO;MACT;AAEA,eAAS,wBAAwB,UAAgC,OAAa;AAC5E,iBAAS,IAAI,OAAO,IAAI,SAAS,QAAQ,KAAK;AAC5C,cAAI,CAAC,SAAS,SAAS,CAAC,CAAC;AAAG,mBAAO;;AAErC,eAAO,SAAS;MAClB;AAEA,eAAS,SAAS,MAAwB;AACxC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,KAAK,CAAC,EAAE,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE,MAAM,GAAG;AACzC,mBAAO;;;AAGX,eAAO;MACT;AAEA,eAAS,aAAa,MAA0B,OAAc;AAC5D,YAAI,CAAC;AAAO,iBAAO,KAAK,MAAK;AAC7B,eAAO,KAAK,KAAK,cAAc;MACjC;AAEA,eAAS,eAAe,GAAqB,GAAmB;AAC9D,eAAO,EAAE,MAAM,IAAI,EAAE,MAAM;MAC7B;ACnCO,UAAI,QAAQ;AAkBb,eAAU,aACd,UACA,QACA,KACA,MAAY;AAEZ,eAAO,OAAO,MAAM;AAClB,gBAAM,MAAM,OAAQ,OAAO,OAAQ;AACnC,gBAAM,MAAM,SAAS,GAAG,EAAE,MAAM,IAAI;AAEpC,cAAI,QAAQ,GAAG;AACb,oBAAQ;AACR,mBAAO;;AAGT,cAAI,MAAM,GAAG;AACX,kBAAM,MAAM;iBACP;AACL,mBAAO,MAAM;;;AAIjB,gBAAQ;AACR,eAAO,MAAM;MACf;eAEgB,WACd,UACA,QACA,OAAa;AAEb,iBAAS,IAAI,QAAQ,GAAG,IAAI,SAAS,QAAQ,QAAQ,KAAK;AACxD,cAAI,SAAS,CAAC,EAAE,MAAM,MAAM;AAAQ;;AAEtC,eAAO;MACT;eAEgB,WACd,UACA,QACA,OAAa;AAEb,iBAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,QAAQ,KAAK;AAC3C,cAAI,SAAS,CAAC,EAAE,MAAM,MAAM;AAAQ;;AAEtC,eAAO;MACT;eAEgB,gBAAa;AAC3B,eAAO;UACL,SAAS;UACT,YAAY;UACZ,WAAW;;MAEf;AAMM,eAAU,qBACd,UACA,QACA,OACA,KAAW;AAEX,cAAM,EAAE,SAAS,YAAY,UAAS,IAAK;AAE3C,YAAI,MAAM;AACV,YAAI,OAAO,SAAS,SAAS;AAC7B,YAAI,QAAQ,SAAS;AACnB,cAAI,WAAW,YAAY;AACzB,oBAAQ,cAAc,MAAM,SAAS,SAAS,EAAE,MAAM,MAAM;AAC5D,mBAAO;;AAGT,cAAI,UAAU,YAAY;AAExB,kBAAM,cAAc,KAAK,IAAI;iBACxB;AACL,mBAAO;;;AAGX,cAAM,UAAU;AAChB,cAAM,aAAa;AAEnB,eAAQ,MAAM,YAAY,aAAa,UAAU,QAAQ,KAAK,IAAI;MACpE;ACrGc,eAAU,eACtB,SACA,OAAkB;AAElB,cAAM,UAAoB,MAAM,IAAI,cAAc;AAElD,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,OAAO,QAAQ,CAAC;AACtB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,MAAM,KAAK,CAAC;AAClB,gBAAI,IAAI,WAAW;AAAG;AAEtB,kBAAMC,eAAc,IAAI,aAAa;AACrC,kBAAM,aAAa,IAAI,WAAW;AAClC,kBAAM,eAAe,IAAI,aAAa;AACtC,kBAAM,iBAAiB,QAAQA,YAAW;AAC1C,kBAAM,eAAgB,eAAe,UAAU,MAAzB,eAAe,UAAU,IAAM,CAAA;AACrD,kBAAM,OAAO,MAAMA,YAAW;AAM9B,gBAAI,QAAQ,WACV,cACA,cACA,qBAAqB,cAAc,cAAc,MAAM,UAAU,CAAC;AAGpE,iBAAK,YAAY,EAAE;AACnB,mBAAO,cAAc,OAAO,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,CAAC;;;AAI9D,eAAO;MACT;AAEA,eAAS,OAAU,OAAY,OAAe,OAAQ;AACpD,iBAAS,IAAI,MAAM,QAAQ,IAAI,OAAO,KAAK;AACzC,gBAAM,CAAC,IAAI,MAAM,IAAI,CAAC;;AAExB,cAAM,KAAK,IAAI;MACjB;AAOA,eAAS,iBAAc;AACrB,eAAO,EAAE,WAAW,KAAI;MAC1B;ACxCa,YAAA,SAAiB,SAAU,KAAK,QAAM;AACjD,cAAM,SAAS,MAAM,GAAG;AAExB,YAAI,EAAE,cAAc,SAAS;AAC3B,iBAAO,IAAI,SAAS,QAA2D,MAAM;;AAGvF,cAAM,WAAiC,CAAA;AACvC,cAAM,UAAoB,CAAA;AAC1B,cAAM,iBAAoC,CAAA;AAC1C,cAAM,QAAkB,CAAA;AACxB,cAAM,aAAuB,CAAA;AAE7B,gBACE,QACA,QACA,UACA,SACA,gBACA,OACA,YACA,GACA,GACA,UACA,QAAQ;AAGV,cAAM,SAA2B;UAC/B,SAAS;UACT,MAAM,OAAO;UACb;UACA;UACA;UACA;UACA;;AAGF,eAAO,oBAAoB,MAAM;MACnC;AAEA,eAAS,MAAS,KAAM;AACtB,eAAO,OAAO,QAAQ,WAAW,KAAK,MAAM,GAAG,IAAI;MACrD;AAEA,eAAS,QACP,OACA,QACA,UACA,SACA,gBACA,OACA,YACA,YACA,cACA,UACA,YAAkB;AAElB,cAAM,EAAE,SAAQ,IAAK;AACrB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,EAAE,KAAK,OAAM,IAAK,SAAS,CAAC;AAElC,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,IAAI,IAAI,SAAS,QAAQ;AAC3B,kBAAM,aAAa,SAAS,IAAI,CAAC,EAAE;AACnC,iBAAK,KAAK,IAAI,UAAU,aAAa,WAAW,IAAI;AAEpD,gBAAI,OAAO,UAAU;AACnB,mBAAK,KAAK,IAAI,YAAY,eAAe,WAAW,MAAM;uBACjD,KAAK,UAAU;AACxB,mBAAK,eAAe,WAAW;;;AAInC,qBACE,KACA,QACA,UACA,SACA,gBACA,OACA,YACA,aAAa,OAAO,MACpB,eAAe,OAAO,QACtB,IACA,EAAE;;MAGR;AAEA,eAAS,WACP,OACA,QACA,UACA,SACA,gBACA,OACA,YACA,YACA,cACA,UACA,YAAkB;AAElB,cAAM,SAAS,MAAM,KAAK;AAC1B,YAAI,cAAc;AAAQ,iBAAO,QAAQ,GAAI,SAAmD;AAEhG,cAAM,MAAM,IAAI,SAAS,QAAQ,MAAM;AACvC,cAAM,gBAAgB,QAAQ;AAC9B,cAAM,cAAc,MAAM;AAC1B,cAAM,UAAU,gBAAgB,GAAG;AACnC,cAAM,EAAE,iBAAiB,gBAAgB,UAAU,YAAY,QAAO,IAAK;AAE3E,eAAO,SAAS,eAAe;AAC/B,eAAO,OAAO,IAAI,KAAK;AAEvB,YAAI;AAAU,iBAAO,gBAAgB,QAAQ;;AACxC,mBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ;AAAK,2BAAe,KAAK,IAAI;AAE9E,YAAI;AAAS,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAAK,uBAAW,KAAK,QAAQ,CAAC,IAAI,aAAa;AAEhG,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,QAAQ,aAAa;AAM3B,cAAI,QAAQ;AAAU;AAItB,gBAAM,MAAM,QAAQ,UAAU,KAAK;AAGnC,gBAAM,UAAU,MAAM,IAAI,eAAe;AAEzC,gBAAM,OAAO,QAAQ,CAAC;AACtB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,MAAM,KAAK,CAAC;AAClB,kBAAM,SAAS,UAAU,IAAI,MAAM;AAInC,gBAAI,UAAU,YAAY,UAAU;AAAY;AAEhD,gBAAI,IAAI,WAAW,GAAG;AACpB,kBAAI,KAAK,CAAC,MAAM,CAAC;AACjB;;AAGF,kBAAM,eAAe,gBAAgB,IAAI,aAAa;AACtD,kBAAM,aAAa,IAAI,WAAW;AAClC,kBAAM,eAAe,IAAI,aAAa;AACtC,gBAAI,KACF,IAAI,WAAW,IACX,CAAC,QAAQ,cAAc,YAAY,YAAY,IAC/C,CAAC,QAAQ,cAAc,YAAY,cAAc,cAAc,IAAI,WAAW,CAAC,CAAC;;;MAI5F;AAEA,eAAS,OAAU,KAAU,OAAU;AACrC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,cAAI,KAAK,MAAM,CAAC,CAAC;MAC1D;AAEA,eAAS,QAAW,KAAY,OAAa;AAC3C,iBAAS,IAAI,IAAI,QAAQ,KAAK,OAAO;AAAK,cAAI,CAAC,IAAI,CAAA;AACnD,eAAO,IAAI,KAAK;MAClB;ACpHA,YAAM,gBAAgB;AACtB,YAAM,kBAAkB;AAEX,YAAA,oBAAoB;AAC1B,YAAM,uBAAuB;YAIvB,SAAQ;QAkBnB,YAAY,KAAqB,QAAsB;AACrD,gBAAM,WAAW,OAAO,QAAQ;AAEhC,cAAI,CAAC,YAAa,IAAyC;AAAc,mBAAO;AAEhF,gBAAM,SAAU,WAAW,KAAK,MAAM,GAAG,IAAI;AAE7C,gBAAM,EAAE,SAAS,MAAM,OAAO,YAAY,SAAS,eAAc,IAAK;AACtE,eAAK,UAAU;AACf,eAAK,OAAO;AACZ,eAAK,QAAQ,SAAS,CAAA;AACtB,eAAK,aAAa;AAClB,eAAK,UAAU;AACf,eAAK,iBAAiB;AACtB,eAAK,aAAa,OAAO,cAAe,OAAkB,uBAAuB;AAEjF,gBAAM,OAAO,QAAQ,cAAc,IAAI,cAAc,MAAM,CAAC;AAC5D,eAAK,kBAAkB,QAAQ,IAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,IAAI,CAAC;AAEhE,gBAAM,EAAE,SAAQ,IAAK;AACrB,cAAI,OAAO,aAAa,UAAU;AAChC,iBAAK,WAAW;AAChB,iBAAK,WAAW;iBACX;AACL,iBAAK,WAAW;AAChB,iBAAK,WAAW,UAAU,UAAU,QAAQ;;AAG9C,eAAK,eAAe,cAAa;AACjC,eAAK,aAAa;AAClB,eAAK,iBAAiB;;MAEzB;AAMD,eAAS,KAAK,KAAY;AACxB,eAAO;MACT;AAKM,eAAU,gBAAgB,KAAa;;;AAC3C,gBAAO,MAAA,KAAC,KAAK,GAAG,GAAE,cAAQ,QAAA,OAAA,SAAA,KAAA,GAAR,WAAaC,eAAAA,OAAO,KAAK,GAAG,EAAE,QAAS;MAC3D;AAKM,eAAU,gBAAgB,KAAa;;AAC3C,gBAAO,KAAC,KAAK,GAAG,GAAE,aAAQ,GAAR,WAAaC,eAAAA,OAAO,KAAK,GAAG,EAAE,QAAS;MAC3D;eAMgB,aACd,KACA,MACA,QAAc;AAEd,cAAM,UAAU,gBAAgB,GAAG;AAInC,YAAI,QAAQ,QAAQ;AAAQ,iBAAO;AAEnC,cAAM,WAAW,QAAQ,IAAI;AAC7B,cAAM,QAAQ,qBACZ,UACA,KAAK,GAAG,EAAE,cACV,MACA,QACA,oBAAoB;AAGtB,eAAO,UAAU,KAAK,OAAO,SAAS,KAAK;MAC7C;AAOgB,eAAA,oBACd,KACA,QAAc;AAEd,YAAI,EAAE,MAAM,QAAQ,KAAI,IAAK;AAC7B;AACA,YAAI,OAAO;AAAG,gBAAM,IAAI,MAAM,aAAa;AAC3C,YAAI,SAAS;AAAG,gBAAM,IAAI,MAAM,eAAe;AAE/C,cAAM,UAAU,gBAAgB,GAAG;AAInC,YAAI,QAAQ,QAAQ;AAAQ,iBAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAElE,cAAM,WAAW,QAAQ,IAAI;AAC7B,cAAM,QAAQ,qBACZ,UACA,KAAK,GAAG,EAAE,cACV,MACA,QACA,QAAQ,oBAAoB;AAG9B,YAAI,UAAU;AAAI,iBAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAExD,cAAM,UAAU,SAAS,KAAK;AAC9B,YAAI,QAAQ,WAAW;AAAG,iBAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAEhE,cAAM,EAAE,OAAO,gBAAe,IAAK;AACnC,eAAO,SACL,gBAAgB,QAAQ,aAAa,CAAC,GACtC,QAAQ,WAAW,IAAI,GACvB,QAAQ,aAAa,GACrB,QAAQ,WAAW,IAAI,MAAM,QAAQ,WAAW,CAAC,IAAI,IAAI;MAE7D;AAKgB,eAAA,qBACd,KACA,QAAoB;AAEpB,cAAM,EAAE,QAAQ,MAAM,QAAQ,KAAI,IAAK;AACvC,eAAO,kBAAkB,KAAK,QAAQ,MAAM,QAAQ,QAAQ,sBAAsB,KAAK;MACzF;AAKgB,eAAA,yBAAyB,KAAe,QAAoB;AAC1E,cAAM,EAAE,QAAQ,MAAM,QAAQ,KAAI,IAAK;AAEvC,eAAO,kBAAkB,KAAK,QAAQ,MAAM,QAAQ,QAAQ,mBAAmB,IAAI;MACrF;AAKgB,eAAA,YAAY,KAAe,IAAkC;AAC3E,cAAM,UAAU,gBAAgB,GAAG;AACnC,cAAM,EAAE,OAAO,gBAAe,IAAK;AAEnC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,OAAO,QAAQ,CAAC;AACtB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,MAAM,KAAK,CAAC;AAElB,kBAAM,gBAAgB,IAAI;AAC1B,kBAAM,kBAAkB,IAAI,CAAC;AAC7B,gBAAI,SAAS;AACb,gBAAI,eAAe;AACnB,gBAAI,iBAAiB;AACrB,gBAAI,OAAO;AACX,gBAAI,IAAI,WAAW,GAAG;AACpB,uBAAS,gBAAgB,IAAI,CAAC,CAAC;AAC/B,6BAAe,IAAI,CAAC,IAAI;AACxB,+BAAiB,IAAI,CAAC;;AAExB,gBAAI,IAAI,WAAW;AAAG,qBAAO,MAAM,IAAI,CAAC,CAAC;AAEzC,eAAG;cACD;cACA;cACA;cACA;cACA;cACA;YACc,CAAA;;;MAGtB;AAEA,eAAS,YAAY,KAAe,QAAc;AAChD,cAAM,EAAE,SAAS,gBAAe,IAAK;AACrC,YAAI,QAAQ,QAAQ,QAAQ,MAAM;AAClC,YAAI,UAAU;AAAI,kBAAQ,gBAAgB,QAAQ,MAAM;AACxD,eAAO;MACT;AAKgB,eAAA,iBAAiB,KAAe,QAAc;AAC5D,cAAM,EAAE,eAAc,IAAK;AAC3B,YAAI,kBAAkB;AAAM,iBAAO;AACnC,cAAM,QAAQ,YAAY,KAAK,MAAM;AACrC,eAAO,UAAU,KAAK,OAAO,eAAe,KAAK;MACnD;AAKgB,eAAA,UAAU,KAAe,QAAc;AACrD,cAAM,EAAE,WAAU,IAAK;AACvB,YAAI,cAAc;AAAM,iBAAO;AAC/B,cAAM,QAAQ,YAAY,KAAK,MAAM;AACrC,eAAO,UAAU,KAAK,QAAQ,WAAW,SAAS,KAAK;MACzD;AAMgB,eAAA,oBAAoB,KAAuB,QAAe;AACxE,cAAM,SAAS,IAAI,SAAS,MAAM,KAAK,CAAA,CAAE,GAAG,MAAM;AAClD,aAAK,MAAM,EAAE,WAAW,IAAI;AAC5B,eAAO;MACT;AAMM,eAAU,WACd,KAAa;AAEb,eAAO,MAAM,KAAK,gBAAgB,GAAG,CAAC;MACxC;AAMM,eAAU,WAAW,KAAa;AACtC,eAAO,MAAM,KAAK,gBAAgB,GAAG,CAAC;MACxC;AAEA,eAAS,MACP,KACA,UAAW;AAEX,eAAO;UACL,SAAS,IAAI;UACb,MAAM,IAAI;UACV,OAAO,IAAI;UACX,YAAY,IAAI;UAChB,SAAS,IAAI;UACb,gBAAgB,IAAI;UACpB;UACA,YAAY,IAAI,cAAe,IAAe;;MAElD;AASA,eAAS,SACP,QACA,MACA,QACA,MAAmB;AAEnB,eAAO,EAAE,QAAQ,MAAM,QAAQ,KAAI;MACrC;AAIA,eAAS,SACP,MACA,QAAqB;AAErB,eAAO,EAAE,MAAM,OAAM;MACvB;AAgBA,eAAS,qBACP,UACA,MACA,MACA,QACA,MAAU;AAEV,YAAI,QAAQ,qBAAqB,UAAU,QAAQ,MAAM,IAAI;AAC7D,YAAIC,OAAS;AACX,mBAAS,SAAS,oBAAoB,aAAa,YAAY,UAAU,QAAQ,KAAK;mBAC7E,SAAS;AAAmB;AAEvC,YAAI,UAAU,MAAM,UAAU,SAAS;AAAQ,iBAAO;AACtD,eAAO;MACT;AAEA,eAAS,wBACP,UACA,MACA,MACA,QACA,MAAU;AAEV,YAAI,MAAM,qBAAqB,UAAU,MAAM,MAAM,QAAQ,oBAAoB;AAQjF,YAAI,CAACA,SAAW,SAAS;AAAmB;AAE5C,YAAI,QAAQ,MAAM,QAAQ,SAAS;AAAQ,iBAAO,CAAA;AAKlD,cAAM,gBAAgBA,QAAU,SAAS,SAAS,GAAG,EAAE,MAAM;AAG7D,YAAI,CAACA;AAAS,gBAAM,WAAW,UAAU,eAAe,GAAG;AAC3D,cAAM,MAAM,WAAW,UAAU,eAAe,GAAG;AAEnD,cAAM,SAAS,CAAA;AACf,eAAO,OAAO,KAAK,OAAO;AACxB,gBAAM,UAAU,SAAS,GAAG;AAC5B,iBAAO,KAAK,SAAS,QAAQ,kBAAkB,IAAI,GAAG,QAAQ,oBAAoB,CAAC,CAAC;;AAEtF,eAAO;MACT;AAkBA,eAAS,kBACP,KACA,QACA,MACA,QACA,MACA,KAAY;;AAEZ;AACA,YAAI,OAAO;AAAG,gBAAM,IAAI,MAAM,aAAa;AAC3C,YAAI,SAAS;AAAG,gBAAM,IAAI,MAAM,eAAe;AAE/C,cAAM,EAAE,SAAS,gBAAe,IAAK;AACrC,YAAIH,eAAc,QAAQ,QAAQ,MAAM;AACxC,YAAIA,iBAAgB;AAAI,UAAAA,eAAc,gBAAgB,QAAQ,MAAM;AACpE,YAAIA,iBAAgB;AAAI,iBAAO,MAAM,CAAA,IAAK,SAAS,MAAM,IAAI;AAE7D,cAAM,aAAY,KAAC,KAAK,GAAG,GAAE,eAAU,GAAV,aAAe,eAC1C,gBAAgB,GAAG,GAClB,KAAK,GAAG,EAAE,iBAAiB,QAAQ,IAAI,aAAa,CAAC;AAGxD,cAAM,WAAW,UAAUA,YAAW,EAAE,IAAI;AAC5C,YAAI,YAAY;AAAM,iBAAO,MAAM,CAAA,IAAK,SAAS,MAAM,IAAI;AAE3D,cAAM,OAAO,KAAK,GAAG,EAAE,eAAgBA,YAAW;AAElD,YAAI;AAAK,iBAAO,wBAAwB,UAAU,MAAM,MAAM,QAAQ,IAAI;AAE1E,cAAM,QAAQ,qBAAqB,UAAU,MAAM,MAAM,QAAQ,IAAI;AACrE,YAAI,UAAU;AAAI,iBAAO,SAAS,MAAM,IAAI;AAE5C,cAAM,UAAU,SAAS,KAAK;AAC9B,eAAO,SAAS,QAAQ,kBAAkB,IAAI,GAAG,QAAQ,oBAAoB,CAAC;MAChF;;;;;;;;;;;;;;;;;;;;;;;;;;;;YCxea,SAAQ;QAInB,cAAA;AACE,eAAK,WAAW,EAAE,WAAW,KAAI;AACjC,eAAK,QAAQ,CAAA;;;AAajB,eAAS,KAAoB,KAAgB;AAC3C,eAAO;MACT;eAKgB,IAAmB,QAAqB,KAAM;AAC5D,eAAO,KAAK,MAAM,EAAE,SAAS,GAAG;MAClC;eAMgB,IAAmB,QAAqB,KAAM;AAE5D,cAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,YAAI,UAAU;AAAW,iBAAO;AAEhC,cAAM,EAAE,OAAO,UAAU,QAAO,IAAK,KAAK,MAAM;AAEhD,cAAM,SAAS,MAAM,KAAK,GAAG;AAC7B,eAAQ,QAAQ,GAAG,IAAI,SAAS;MAClC;eAKgB,IAAmB,QAAmB;AACpD,cAAM,EAAE,OAAO,UAAU,QAAO,IAAK,KAAK,MAAM;AAChD,YAAI,MAAM,WAAW;AAAG;AAExB,cAAM,OAAO,MAAM,IAAG;AACtB,gBAAQ,IAAI,IAAI;MAClB;eAKgB,OAAsB,QAAqB,KAAM;AAC/D,cAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,YAAI,UAAU;AAAW;AAEzB,cAAM,EAAE,OAAO,UAAU,QAAO,IAAK,KAAK,MAAM;AAChD,iBAAS,IAAI,QAAQ,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC7C,gBAAM,IAAI,MAAM,CAAC;AACjB,gBAAM,IAAI,CAAC,IAAI;AACf,kBAAQ,CAAC;;AAEX,gBAAQ,GAAG,IAAI;AACf,cAAM,IAAG;MACX;;;;;;;;;;;;;;;;;;ACtEO,YAAM,SAAS;AACf,YAAM,gBAAgB;AACtB,YAAM,cAAc;AACpB,YAAM,gBAAgB;AACtB,YAAM,cAAc;ACQ3B,YAAM,UAAU;YAKH,WAAU;QASrB,YAAY,EAAE,MAAM,WAAU,IAAc,CAAA,GAAE;AAC5C,eAAK,SAAS,IAAII,SAAAA,SAAQ;AAC1B,eAAK,WAAW,IAAIA,SAAAA,SAAQ;AAC5B,eAAK,kBAAkB,CAAA;AACvB,eAAK,YAAY,CAAA;AACjB,eAAK,OAAO;AACZ,eAAK,aAAa;AAClB,eAAK,cAAc,IAAIA,SAAAA,SAAQ;;MAElC;AAcD,eAAS,KAAK,KAAY;AACxB,eAAO;MACT;eAoCgB,WACd,KACA,SACA,WACA,QACA,YACA,cACA,MACA,SAAuB;AAEvB,eAAO,mBACL,OACA,KACA,SACA,WACA,QACA,YACA,cACA,MACA,OAAO;MAEX;AAoCgB,eAAA,WACd,KACA,SAMC;AAED,eAAO,mBAAmB,OAAO,KAAK,OAAmD;MAC3F;YAOa,kBAAqC,CAChD,KACA,SACA,WACA,QACA,YACA,cACA,MACA,YACE;AACF,eAAO,mBACL,MACA,KACA,SACA,WACA,QACA,YACA,cACA,MACA,OAAO;MAEX;YAOa,kBAAqC,CAAC,KAAK,YAAW;AACjE,eAAO,mBAAmB,MAAM,KAAK,OAAmD;MAC1F;eAKgB,iBAAiB,KAAiB,QAAgB,SAAsB;AACtF,cAAM,EAAE,UAAU,SAAS,iBAAiB,eAAc,IAAK,KAAK,GAAG;AACvE,cAAM,QAAQC,SAAAA,IAAI,SAAS,MAAM;AACjC,uBAAe,KAAK,IAAI;MAC1B;AAEM,eAAU,UAAU,KAAiB,QAAgB,SAAS,MAAI;AACtE,cAAM,EAAE,UAAU,SAAS,iBAAiB,gBAAgB,aAAa,WAAU,IAAK,KAAK,GAAG;AAChG,cAAM,QAAQA,SAAAA,IAAI,SAAS,MAAM;AACjC,YAAI,UAAU,eAAe;AAAQ,yBAAe,KAAK,IAAI;AAC7D,YAAI;AAAQA,mBAAAA,IAAI,YAAY,KAAK;;AAC5BC,mBAAAA,OAAO,YAAY,KAAK;MAC/B;AAMM,eAAU,aAAa,KAAe;AAC1C,cAAM,EACJ,WAAW,UACX,UAAU,SACV,iBAAiB,gBACjB,QAAQ,OACR,aAAa,WAAU,IACrB,KAAK,GAAG;AACZ,8BAAsB,QAAQ;AAE9B,eAAO;UACL,SAAS;UACT,MAAM,IAAI,QAAQ;UAClB,OAAO,MAAM;UACb,YAAY,IAAI,cAAc;UAC9B,SAAS,QAAQ;UACjB;UACA;UACA,YAAY,WAAW;;MAE3B;AAMM,eAAU,aAAa,KAAe;AAC1C,cAAM,UAAU,aAAa,GAAG;AAChC,eACK,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,OAAO,GACV,EAAA,UAAUC,eAAAA,OAAO,QAAQ,QAAgC,EAAC,CAC1D;MACJ;AAKM,eAAU,QAAQ,OAAqB;AAC3C,cAAM,MAAM,IAAIC,aAAAA,SAAS,KAAK;AAC9B,cAAM,MAAM,IAAI,WAAW,EAAE,MAAM,IAAI,MAAM,YAAY,IAAI,WAAU,CAAE;AAEzE,eAAO,KAAK,GAAG,EAAE,QAAQ,IAAI,KAAK;AAClC,eAAO,KAAK,GAAG,EAAE,UAAU,IAAI,OAAmB;AAClD,aAAK,GAAG,EAAE,kBAAkB,IAAI,kBAAkB,IAAI,QAAQ,IAAI,MAAM,IAAI;AAC5E,aAAK,GAAG,EAAE,YAAYC,aAAAA,gBAAgB,GAAG;AACzC,YAAI,IAAI;AAAY,iBAAO,KAAK,GAAG,EAAE,aAAa,IAAI,UAAU;AAEhE,eAAO;MACT;AAMM,eAAU,YAAY,KAAe;AACzC,cAAM,MAAiB,CAAA;AACvB,cAAM,EAAE,WAAW,UAAU,UAAU,SAAS,QAAQ,MAAK,IAAK,KAAK,GAAG;AAE1E,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,OAAO,SAAS,CAAC;AACvB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,MAAM,KAAK,CAAC;AAElB,kBAAM,YAAY,EAAE,MAAM,IAAI,GAAG,QAAQ,IAAI,MAAM,EAAC;AACpD,gBAAI,SAA6B;AACjC,gBAAI,WAA4B;AAChC,gBAAI,OAA2B;AAE/B,gBAAI,IAAI,WAAW,GAAG;AACpB,uBAAS,QAAQ,MAAM,IAAI,aAAa,CAAC;AACzC,yBAAW,EAAE,MAAM,IAAI,WAAW,IAAI,GAAG,QAAQ,IAAI,aAAa,EAAC;AAEnE,kBAAI,IAAI,WAAW;AAAG,uBAAO,MAAM,MAAM,IAAI,WAAW,CAAC;YAC1D;AAED,gBAAI,KAAK,EAAE,WAAW,QAAQ,UAAU,KAAI,CAAa;UAC1D;QACF;AAED,eAAO;MACT;AAGA,eAAS,mBACP,UACA,KACA,SACA,WACA,QACA,YACA,cACA,MACA,SAAwE;AAExE,cAAM,EACJ,WAAW,UACX,UAAU,SACV,iBAAiB,gBACjB,QAAQ,MAAK,IACX,KAAK,GAAG;AACZ,cAAM,OAAO,QAAQ,UAAU,OAAO;AACtC,cAAM,QAAQ,eAAe,MAAM,SAAS;AAE5C,YAAI,CAAC,QAAQ;AACX,cAAI,YAAY,eAAe,MAAM,KAAK;AAAG;AAC7C,iBAAO,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC;QACvC;AAOD,cAAM,eAAeJ,SAAAA,IAAI,SAAS,MAAM;AACxC,cAAM,aAAa,OAAOA,SAAAA,IAAI,OAAO,IAAI,IAAI;AAC7C,YAAI,iBAAiB,eAAe;AAAQ,yBAAe,YAAY,IAAI,YAAA,QAAA,YAAA,SAAA,UAAW;AAEtF,YAAI,YAAY,WAAW,MAAM,OAAO,cAAc,YAAY,cAAc,UAAU,GAAG;AAC3F;QACD;AAED,eAAO,OACL,MACA,OACA,OACI,CAAC,WAAW,cAAc,YAAY,cAAc,UAAU,IAC9D,CAAC,WAAW,cAAc,YAAY,YAAY,CAAC;MAE3D;AAMA,eAAS,QAAQ,UAAgC,OAAa;AAC5D,iBAAS,IAAI,SAAS,QAAQ,KAAK,OAAO,KAAK;AAC7C,mBAAS,CAAC,IAAI,CAAA;QACf;AACD,eAAO,SAAS,KAAK;MACvB;AAEA,eAAS,eAAe,MAA0B,WAAiB;AACjE,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,QAAQ,KAAK;AAC3C,gBAAM,UAAU,KAAK,CAAC;AACtB,cAAI,aAAa,QAAQ,MAAM;AAAG;QACnC;AACD,eAAO;MACT;AAEA,eAAS,OAAU,OAAY,OAAe,OAAQ;AACpD,iBAAS,IAAI,MAAM,QAAQ,IAAI,OAAO,KAAK;AACzC,gBAAM,CAAC,IAAI,MAAM,IAAI,CAAC;QACvB;AACD,cAAM,KAAK,IAAI;MACjB;AAEA,eAAS,sBAAsB,UAA8B;AAC3D,cAAM,EAAE,OAAM,IAAK;AACnB,YAAI,MAAM;AACV,iBAAS,IAAI,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK;AAC1C,cAAI,SAAS,CAAC,EAAE,SAAS;AAAG;QAC7B;AACD,YAAI,MAAM;AAAQ,mBAAS,SAAS;MACtC;AAEA,eAAS,OAAkC,QAAqB,OAAU;AACxE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAKA,mBAAAA,IAAI,QAAQ,MAAM,CAAC,CAAC;MAC7D;AAEA,eAAS,eAAe,MAA0B,OAAa;AAG7D,YAAI,UAAU;AAAG,iBAAO;AAExB,cAAM,OAAO,KAAK,QAAQ,CAAC;AAI3B,eAAO,KAAK,WAAW;MACzB;AAEA,eAAS,WACP,MACA,OACA,cACA,YACA,cACA,YAAkB;AAGlB,YAAI,UAAU;AAAG,iBAAO;AAExB,cAAM,OAAO,KAAK,QAAQ,CAAC;AAG3B,YAAI,KAAK,WAAW;AAAG,iBAAO;AAI9B,eACE,iBAAiB,KAAK,aAAa,KACnC,eAAe,KAAK,WAAW,KAC/B,iBAAiB,KAAK,aAAa,KACnC,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,IAAI;MAE5D;AAEA,eAAS,mBACP,UACA,KACA,SAMC;AAED,cAAM,EAAE,WAAW,QAAQ,UAAU,MAAM,QAAO,IAAK;AACvD,YAAI,CAAC,QAAQ;AACX,iBAAO,mBACL,UACA,KACA,UAAU,OAAO,GACjB,UAAU,QACV,MACA,MACA,MACA,MACA,IAAI;QAEP;AAED,eAAO,mBACL,UACA,KACA,UAAU,OAAO,GACjB,UAAU,QACV,QACA,SAAS,OAAO,GAChB,SAAS,QACT,MACA,OAAO;MAEX;;;;;;;;;;;;;;;;;;;;;;;;ACvbA,YAAM,qBAAqC,cAAc,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK;AACpF,YAAM,gBAA2B,CAAA;AAEjC,eAAS,cACP,QACA,MACA,QACA,MACA,SACA,QAAe;AAEf,eAAO,EAAE,QAAQ,MAAM,QAAQ,MAAM,SAAS,OAAM;MACtD;AAgBA,eAAS,OACP,KACA,SACA,QACA,SACA,QAAe;AAEf,eAAO;UACL;UACA;UACA;UACA;UACA;;MAEJ;AAMgB,eAAA,UAAU,KAAe,SAAkB;AACzD,eAAO,OAAO,KAAK,SAAS,IAAI,MAAM,KAAK;MAC7C;eAMgB,eACd,QACA,SACA,QAAe;AAEf,eAAO,OAAO,MAAM,eAAe,QAAQ,SAAS,MAAM;MAC5D;AAMM,eAAU,cAAc,MAAe;AAG3C,cAAM,MAAM,IAAIK,WAAAA,WAAW,EAAE,MAAM,KAAK,IAAI,KAAI,CAAE;AAClD,cAAM,EAAE,SAAS,aAAa,IAAG,IAAK;AACtC,cAAM,YAAY,IAAI;AACtB,cAAM,eAAeC,aAAAA,gBAAgB,GAAG;AAExC,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,gBAAM,WAAW,aAAa,CAAC;AAE/B,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kBAAM,UAAU,SAAS,CAAC;AAC1B,kBAAM,SAAS,QAAQ,CAAC;AACxB,gBAAI,SAAwC;AAI5C,gBAAI,QAAQ,WAAW,GAAG;AACxB,oBAAMC,UAAS,YAAY,QAAQ,CAAC,CAAC;AACrC,uBAAS,oBACPA,SACA,QAAQ,CAAC,GACT,QAAQ,CAAC,GACT,QAAQ,WAAW,IAAI,UAAU,QAAQ,CAAC,CAAC,IAAI,EAAE;AAKnD,kBAAI,UAAU;AAAM;YACrB;AAED,kBAAM,EAAE,QAAQ,MAAM,MAAM,SAAS,QAAQ,OAAM,IAAK;AAExDC,uBAAAA,gBAAgB,KAAK,GAAG,QAAQ,QAAQ,MAAM,QAAQ,IAAI;AAC1D,gBAAI,UAAU,WAAW;AAAMC,yBAAAA,iBAAiB,KAAK,QAAQ,OAAO;AACpE,gBAAI;AAAQC,yBAAAA,UAAU,KAAK,QAAQ,IAAI;UACxC;QACF;AAED,eAAO;MACT;AAMM,eAAU,oBACd,QACA,MACA,QACA,MAAY;AAEZ,YAAI,CAAC,OAAO,KAAK;AACf,iBAAO,cAAc,OAAO,QAAQ,MAAM,QAAQ,MAAM,OAAO,SAAS,OAAO,MAAM;QACtF;AAED,cAAM,UAAUC,aAAAA,aAAa,OAAO,KAAK,MAAM,MAAM;AAGrD,YAAI,WAAW;AAAM,iBAAO;AAG5B,YAAI,QAAQ,WAAW;AAAG,iBAAO;AAEjC,eAAO,oBACL,OAAO,QAAQ,QAAQ,CAAC,CAAC,GACzB,QAAQ,CAAC,GACT,QAAQ,CAAC,GACT,QAAQ,WAAW,IAAI,OAAO,IAAI,MAAM,QAAQ,CAAC,CAAC,IAAI,IAAI;MAE9D;ACpKA,eAAS,QAAW,OAAc;AAChC,YAAI,MAAM,QAAQ,KAAK;AAAG,iBAAO;AACjC,eAAO,CAAC,KAAK;MACf;AAac,eAAU,mBACtB,OACA,QAAuB;AAEvB,cAAM,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,MAAM,IAAIC,aAAAA,SAAS,GAAG,EAAE,CAAC;AAC1D,cAAM,MAAM,KAAK,IAAG;AAEpB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,KAAK,CAAC,EAAE,QAAQ,SAAS,GAAG;AAC9B,kBAAM,IAAI,MACR,sBAAsB,CAAC;sEACkD;UAE5E;QACF;AAED,YAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,CAAC;AACnC,iBAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,iBAAO,UAAU,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;QACjC;AACD,eAAO;MACT;AAEA,eAAS,MACP,KACA,QACA,UACA,eAAqB;AAErB,cAAM,EAAE,iBAAiB,gBAAgB,WAAU,IAAK;AAExD,cAAM,QAAQ,gBAAgB;AAC9B,cAAM,WAAW,gBAAgB,IAAI,CAAC,YAA2B,MAAsB;AAKrF,gBAAM,MAAqB;YACzB;YACA;YACA,QAAQ,cAAc;YACtB,SAAS;YACT,QAAQ;;AAKV,gBAAM,YAAY,OAAO,IAAI,QAAQ,GAAG;AAExC,gBAAM,EAAE,QAAQ,SAAS,OAAM,IAAK;AAGpC,cAAI;AAAW,mBAAO,MAAM,IAAIA,aAAAA,SAAS,WAAW,MAAM,GAAG,QAAQ,QAAQ,KAAK;AAMlF,gBAAM,gBACJ,YAAY,SAAY,UAAU,iBAAiB,eAAe,CAAC,IAAI;AACzE,gBAAM,UAAU,WAAW,SAAY,SAAS,aAAa,WAAW,SAAS,CAAC,IAAI;AACtF,iBAAO,eAAe,QAAQ,eAAe,OAAO;QACtD,CAAC;AAED,eAAO,UAAU,KAAK,QAAQ;MAChC;MC/Ec,MAAO,UAAS;QAU5B,YAAY,KAAiB,SAAgB;AAC3C,gBAAM,MAAM,QAAQ,kBAAkBC,WAAAA,aAAa,GAAG,IAAIC,WAAAA,aAAa,GAAG;AAC1E,eAAK,UAAU,IAAI;AACnB,eAAK,OAAO,IAAI;AAChB,eAAK,WAAW,IAAI;AACpB,eAAK,QAAQ,IAAI;AACjB,eAAK,aAAa,IAAI;AACtB,eAAK,aAAa,IAAI;AAEtB,eAAK,UAAU,IAAI;AACnB,cAAI,CAAC,QAAQ,gBAAgB;AAC3B,iBAAK,iBAAiB,IAAI;UAC3B;;QAGH,WAAQ;AACN,iBAAO,KAAK,UAAU,IAAI;;MAE7B;ACLuB,eAAA,UACtB,OACA,QACA,SAA2B;AAE3B,cAAM,OACJ,OAAO,YAAY,WAAW,UAAU,EAAE,gBAAgB,CAAC,CAAC,SAAS,iBAAiB,MAAK;AAC7F,cAAM,OAAO,mBAAmB,OAAO,MAAM;AAC7C,eAAO,IAAI,UAAU,cAAc,IAAI,GAAG,IAAI;MAChD;;;;;", "names": ["url", "sourceIndex", "encode", "decode", "bsFound", "SetArray", "put", "remove", "encode", "TraceMap", "decodedMappings", "GenMapping", "decodedMappings", "source", "maybeAddSegment", "setSourceContent", "setIgnore", "traceSegment", "TraceMap", "toDecodedMap", "toEncodedMap"]}
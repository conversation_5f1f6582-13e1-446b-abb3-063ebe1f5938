{"version": 3, "sources": ["../../../../../chromatic/isChromatic.mjs"], "sourcesContent": ["/* eslint-env browser */\n\nexport default function isChromatic(windowArgument) {\n  const windowToCheck = windowArgument || (typeof window !== 'undefined' && window);\n  return !!(\n    windowToCheck &&\n    (/Chromatic/.test(windowToCheck.navigator.userAgent) ||\n      /chromatic=true/.test(windowToCheck.location.href))\n  );\n}\n"], "mappings": ";;;AAEe,SAAR,YAA6B,gBAAgB;AAClD,QAAM,gBAAgB,kBAAmB,OAAO,WAAW,eAAe;AAC1E,SAAO,CAAC,EACN,kBACC,YAAY,KAAK,cAAc,UAAU,SAAS,KACjD,iBAAiB,KAAK,cAAc,SAAS,IAAI;AAEvD;", "names": []}
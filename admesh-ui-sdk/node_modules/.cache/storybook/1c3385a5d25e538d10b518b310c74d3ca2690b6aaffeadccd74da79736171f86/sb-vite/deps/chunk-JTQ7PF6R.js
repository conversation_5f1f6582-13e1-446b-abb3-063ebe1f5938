import {
  require_baseAssignValue
} from "./chunk-JLPG4W6G.js";
import {
  require_eq
} from "./chunk-4YXVX72G.js";
import {
  __commonJS
} from "./chunk-KEXKKQVW.js";

// node_modules/lodash/_assignValue.js
var require_assignValue = __commonJS({
  "node_modules/lodash/_assignValue.js"(exports, module) {
    var baseAssignValue = require_baseAssignValue();
    var eq = require_eq();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function assignValue(object, key, value) {
      var objValue = object[key];
      if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) || value === void 0 && !(key in object)) {
        baseAssignValue(object, key, value);
      }
    }
    module.exports = assignValue;
  }
});

export {
  require_assignValue
};
//# sourceMappingURL=chunk-JTQ7PF6R.js.map

import {
  renderElement,
  unmountElement
} from "./chunk-V53S43HV.js";
import "./chunk-VNHSZ6UV.js";
import {
  AnchorMdx,
  CodeOrSourceMdx,
  Docs,
  HeadersMdx
} from "./chunk-DJE4DIX4.js";
import "./chunk-BMOEWYDM.js";
import "./chunk-2NOI57PC.js";
import "./chunk-H4EEZRGF.js";
import "./chunk-JLBFQ2EK.js";
import "./chunk-4K3VBXQL.js";
import "./chunk-3EP2NFP4.js";
import "./chunk-E4Q3YXXP.js";
import "./chunk-LZWHA5M5.js";
import "./chunk-GF7VUYY4.js";
import {
  require_react
} from "./chunk-XQWWUXQD.js";
import "./chunk-ZHATCZIL.js";
import "./chunk-DQSSUQZP.js";
import {
  __toESM
} from "./chunk-KEXKKQVW.js";

// node_modules/@storybook/addon-docs/dist/DocsRenderer-PQXLIZUC.mjs
var import_react = __toESM(require_react(), 1);
var defaultComponents = { code: CodeOrSourceMdx, a: AnchorMdx, ...HeadersMdx };
var ErrorBoundary = class extends import_react.Component {
  constructor() {
    super(...arguments);
    this.state = { hasError: false };
  }
  static getDerivedStateFromError() {
    return { hasError: true };
  }
  componentDidCatch(err) {
    let { showException } = this.props;
    showException(err);
  }
  render() {
    let { hasError } = this.state, { children } = this.props;
    return hasError ? null : import_react.default.createElement(import_react.default.Fragment, null, children);
  }
};
var DocsRenderer = class {
  constructor() {
    this.render = async (context, docsParameter, element) => {
      let components = { ...defaultComponents, ...docsParameter == null ? void 0 : docsParameter.components }, TDocs = Docs;
      return new Promise((resolve, reject) => {
        import("./@mdx-js_react.js").then(({ MDXProvider }) => renderElement(import_react.default.createElement(ErrorBoundary, { showException: reject, key: Math.random() }, import_react.default.createElement(MDXProvider, { components }, import_react.default.createElement(TDocs, { context, docsParameter }))), element)).then(() => resolve());
      });
    }, this.unmount = (element) => {
      unmountElement(element);
    };
  }
};
export {
  DocsRenderer,
  defaultComponents
};
//# sourceMappingURL=DocsRenderer-PQXLIZUC-4YCT2HEU.js.map

import {
  require_baseMerge,
  require_createAssigner
} from "./chunk-A5CLDMAE.js";
import {
  __commonJS
} from "./chunk-KEXKKQVW.js";

// node_modules/lodash/mergeWith.js
var require_mergeWith = __commonJS({
  "node_modules/lodash/mergeWith.js"(exports, module) {
    var baseMerge = require_baseMerge();
    var createAssigner = require_createAssigner();
    var mergeWith = createAssigner(function(object, source, srcIndex, customizer) {
      baseMerge(object, source, srcIndex, customizer);
    });
    module.exports = mergeWith;
  }
});

export {
  require_mergeWith
};
//# sourceMappingURL=chunk-WR2HDILT.js.map

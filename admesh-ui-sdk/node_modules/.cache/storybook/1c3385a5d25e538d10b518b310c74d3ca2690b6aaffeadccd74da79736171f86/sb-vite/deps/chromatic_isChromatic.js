import "./chunk-KEXKKQVW.js";

// node_modules/chromatic/isChromatic.mjs
function isChromatic(windowArgument) {
  const windowToCheck = windowArgument || typeof window !== "undefined" && window;
  return !!(windowToCheck && (/Chromatic/.test(windowToCheck.navigator.userAgent) || /chromatic=true/.test(windowToCheck.location.href)));
}
export {
  isChromatic as default
};
//# sourceMappingURL=chromatic_isChromatic.js.map

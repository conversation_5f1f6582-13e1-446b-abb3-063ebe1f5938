{"version": 3, "sources": ["../../../../../@storybook/addon-docs/dist/preview.mjs"], "sourcesContent": ["var excludeTags=Object.entries(globalThis.TAGS_OPTIONS??{}).reduce((acc,entry)=>{let[tag,option]=entry;return option.excludeFromDocsStories&&(acc[tag]=!0),acc},{}),parameters={docs:{renderer:async()=>{let{DocsRenderer}=await import('./DocsRenderer-PQXLIZUC.mjs');return new DocsRenderer},stories:{filter:story=>(story.tags||[]).filter(tag=>excludeTags[tag]).length===0&&!story.parameters.docs?.disable}}};\n\nexport { parameters };\n"], "mappings": ";;;AAAA,IAAI,cAAY,OAAO,QAAQ,WAAW,gBAAc,CAAC,CAAC,EAAE,OAAO,CAAC,KAAI,UAAQ;AAAC,MAAG,CAAC,KAAI,MAAM,IAAE;AAAM,SAAO,OAAO,2BAAyB,IAAI,GAAG,IAAE,OAAI;AAAG,GAAE,CAAC,CAAC;AAAlK,IAAoK,aAAW,EAAC,MAAK,EAAC,UAAS,YAAS;AAAC,MAAG,EAAC,aAAY,IAAE,MAAM,OAAO,qCAA6B;AAAE,SAAO,IAAI;AAAY,GAAE,SAAQ,EAAC,QAAO,WAAK;AAArT;AAAwT,gBAAM,QAAM,CAAC,GAAG,OAAO,SAAK,YAAY,GAAG,CAAC,EAAE,WAAS,KAAG,GAAC,WAAM,WAAW,SAAjB,mBAAuB;AAAA,EAAO,EAAC,EAAC;", "names": []}
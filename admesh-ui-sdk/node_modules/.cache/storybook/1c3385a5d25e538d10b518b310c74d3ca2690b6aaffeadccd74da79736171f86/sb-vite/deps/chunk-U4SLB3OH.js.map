{"version": 3, "sources": ["../../../../../@storybook/react/dist/chunk-XLZBPYSH.mjs"], "sourcesContent": ["import React from 'react';\nimport { defaultDecorateStory } from 'storybook/preview-api';\n\nvar applyDecorators=(storyFn,decorators)=>defaultDecorateStory(context=>React.createElement(storyFn,context),decorators);\n\nexport { applyDecorators };\n"], "mappings": ";;;;;;;;;;;AAAA,mBAAkB;AAClB,yBAAqC;AAErC,IAAI,kBAAgB,CAAC,SAAQ,mBAAa,yCAAqB,aAAS,aAAAA,QAAM,cAAc,SAAQ,OAAO,GAAE,UAAU;", "names": ["React"]}
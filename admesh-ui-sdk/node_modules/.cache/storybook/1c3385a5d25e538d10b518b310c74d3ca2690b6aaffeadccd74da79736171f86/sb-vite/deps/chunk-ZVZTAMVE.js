import {
  require_upperFirst
} from "./chunk-NA3I52LO.js";
import {
  require_createCompounder
} from "./chunk-EQURAP73.js";
import {
  __commonJS
} from "./chunk-KEXKKQVW.js";

// node_modules/lodash/startCase.js
var require_startCase = __commonJS({
  "node_modules/lodash/startCase.js"(exports, module) {
    var createCompounder = require_createCompounder();
    var upperFirst = require_upperFirst();
    var startCase = createCompounder(function(result, word, index) {
      return result + (index ? " " : "") + upperFirst(word);
    });
    module.exports = startCase;
  }
});

export {
  require_startCase
};
//# sourceMappingURL=chunk-ZVZTAMVE.js.map

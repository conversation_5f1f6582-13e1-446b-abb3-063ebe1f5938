import {
  require_sourcemap_codec_umd
} from "./chunk-SKR5WGJM.js";
import {
  __commonJS
} from "./chunk-KEXKKQVW.js";

// node_modules/@jridgewell/resolve-uri/dist/resolve-uri.umd.js
var require_resolve_uri_umd = __commonJS({
  "node_modules/@jridgewell/resolve-uri/dist/resolve-uri.umd.js"(exports, module) {
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? module.exports = factory() : typeof define === "function" && define.amd ? define(factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, global.resolveURI = factory());
    })(exports, function() {
      "use strict";
      const schemeRegex = /^[\w+.-]+:\/\//;
      const urlRegex = /^([\w+.-]+:)\/\/([^@/#?]*@)?([^:/#?]*)(:\d+)?(\/[^#?]*)?(\?[^#]*)?(#.*)?/;
      const fileRegex = /^file:(?:\/\/((?![a-z]:)[^/#?]*)?)?(\/?[^#?]*)(\?[^#]*)?(#.*)?/i;
      function isAbsoluteUrl(input) {
        return schemeRegex.test(input);
      }
      function isSchemeRelativeUrl(input) {
        return input.startsWith("//");
      }
      function isAbsolutePath(input) {
        return input.startsWith("/");
      }
      function isFileUrl(input) {
        return input.startsWith("file:");
      }
      function isRelative(input) {
        return /^[.?#]/.test(input);
      }
      function parseAbsoluteUrl(input) {
        const match = urlRegex.exec(input);
        return makeUrl(match[1], match[2] || "", match[3], match[4] || "", match[5] || "/", match[6] || "", match[7] || "");
      }
      function parseFileUrl(input) {
        const match = fileRegex.exec(input);
        const path = match[2];
        return makeUrl("file:", "", match[1] || "", "", isAbsolutePath(path) ? path : "/" + path, match[3] || "", match[4] || "");
      }
      function makeUrl(scheme, user, host, port, path, query, hash) {
        return {
          scheme,
          user,
          host,
          port,
          path,
          query,
          hash,
          type: 7
        };
      }
      function parseUrl(input) {
        if (isSchemeRelativeUrl(input)) {
          const url2 = parseAbsoluteUrl("http:" + input);
          url2.scheme = "";
          url2.type = 6;
          return url2;
        }
        if (isAbsolutePath(input)) {
          const url2 = parseAbsoluteUrl("http://foo.com" + input);
          url2.scheme = "";
          url2.host = "";
          url2.type = 5;
          return url2;
        }
        if (isFileUrl(input))
          return parseFileUrl(input);
        if (isAbsoluteUrl(input))
          return parseAbsoluteUrl(input);
        const url = parseAbsoluteUrl("http://foo.com/" + input);
        url.scheme = "";
        url.host = "";
        url.type = input ? input.startsWith("?") ? 3 : input.startsWith("#") ? 2 : 4 : 1;
        return url;
      }
      function stripPathFilename(path) {
        if (path.endsWith("/.."))
          return path;
        const index = path.lastIndexOf("/");
        return path.slice(0, index + 1);
      }
      function mergePaths(url, base) {
        normalizePath(base, base.type);
        if (url.path === "/") {
          url.path = base.path;
        } else {
          url.path = stripPathFilename(base.path) + url.path;
        }
      }
      function normalizePath(url, type) {
        const rel = type <= 4;
        const pieces = url.path.split("/");
        let pointer = 1;
        let positive = 0;
        let addTrailingSlash = false;
        for (let i = 1; i < pieces.length; i++) {
          const piece = pieces[i];
          if (!piece) {
            addTrailingSlash = true;
            continue;
          }
          addTrailingSlash = false;
          if (piece === ".")
            continue;
          if (piece === "..") {
            if (positive) {
              addTrailingSlash = true;
              positive--;
              pointer--;
            } else if (rel) {
              pieces[pointer++] = piece;
            }
            continue;
          }
          pieces[pointer++] = piece;
          positive++;
        }
        let path = "";
        for (let i = 1; i < pointer; i++) {
          path += "/" + pieces[i];
        }
        if (!path || addTrailingSlash && !path.endsWith("/..")) {
          path += "/";
        }
        url.path = path;
      }
      function resolve(input, base) {
        if (!input && !base)
          return "";
        const url = parseUrl(input);
        let inputType = url.type;
        if (base && inputType !== 7) {
          const baseUrl = parseUrl(base);
          const baseType = baseUrl.type;
          switch (inputType) {
            case 1:
              url.hash = baseUrl.hash;
            // fall through
            case 2:
              url.query = baseUrl.query;
            // fall through
            case 3:
            case 4:
              mergePaths(url, baseUrl);
            // fall through
            case 5:
              url.user = baseUrl.user;
              url.host = baseUrl.host;
              url.port = baseUrl.port;
            // fall through
            case 6:
              url.scheme = baseUrl.scheme;
          }
          if (baseType > inputType)
            inputType = baseType;
        }
        normalizePath(url, inputType);
        const queryHash = url.query + url.hash;
        switch (inputType) {
          // This is impossible, because of the empty checks at the start of the function.
          // case UrlType.Empty:
          case 2:
          case 3:
            return queryHash;
          case 4: {
            const path = url.path.slice(1);
            if (!path)
              return queryHash || ".";
            if (isRelative(base || input) && !isRelative(path)) {
              return "./" + path + queryHash;
            }
            return path + queryHash;
          }
          case 5:
            return url.path + queryHash;
          default:
            return url.scheme + "//" + url.user + url.host + url.port + url.path + queryHash;
        }
      }
      return resolve;
    });
  }
});

// node_modules/@jridgewell/trace-mapping/dist/trace-mapping.umd.js
var require_trace_mapping_umd = __commonJS({
  "node_modules/@jridgewell/trace-mapping/dist/trace-mapping.umd.js"(exports, module) {
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? factory(exports, require_sourcemap_codec_umd(), require_resolve_uri_umd()) : typeof define === "function" && define.amd ? define(["exports", "@jridgewell/sourcemap-codec", "@jridgewell/resolve-uri"], factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, factory(global.traceMapping = {}, global.sourcemapCodec, global.resolveURI));
    })(exports, function(exports2, sourcemapCodec, resolveUri) {
      "use strict";
      function resolve(input, base) {
        if (base && !base.endsWith("/"))
          base += "/";
        return resolveUri(input, base);
      }
      function stripFilename(path) {
        if (!path)
          return "";
        const index = path.lastIndexOf("/");
        return path.slice(0, index + 1);
      }
      const COLUMN = 0;
      const SOURCES_INDEX = 1;
      const SOURCE_LINE = 2;
      const SOURCE_COLUMN = 3;
      const NAMES_INDEX = 4;
      const REV_GENERATED_LINE = 1;
      const REV_GENERATED_COLUMN = 2;
      function maybeSort(mappings, owned) {
        const unsortedIndex = nextUnsortedSegmentLine(mappings, 0);
        if (unsortedIndex === mappings.length)
          return mappings;
        if (!owned)
          mappings = mappings.slice();
        for (let i = unsortedIndex; i < mappings.length; i = nextUnsortedSegmentLine(mappings, i + 1)) {
          mappings[i] = sortSegments(mappings[i], owned);
        }
        return mappings;
      }
      function nextUnsortedSegmentLine(mappings, start) {
        for (let i = start; i < mappings.length; i++) {
          if (!isSorted(mappings[i]))
            return i;
        }
        return mappings.length;
      }
      function isSorted(line) {
        for (let j = 1; j < line.length; j++) {
          if (line[j][COLUMN] < line[j - 1][COLUMN]) {
            return false;
          }
        }
        return true;
      }
      function sortSegments(line, owned) {
        if (!owned)
          line = line.slice();
        return line.sort(sortComparator);
      }
      function sortComparator(a, b) {
        return a[COLUMN] - b[COLUMN];
      }
      let found = false;
      function binarySearch(haystack, needle, low, high) {
        while (low <= high) {
          const mid = low + (high - low >> 1);
          const cmp = haystack[mid][COLUMN] - needle;
          if (cmp === 0) {
            found = true;
            return mid;
          }
          if (cmp < 0) {
            low = mid + 1;
          } else {
            high = mid - 1;
          }
        }
        found = false;
        return low - 1;
      }
      function upperBound(haystack, needle, index) {
        for (let i = index + 1; i < haystack.length; index = i++) {
          if (haystack[i][COLUMN] !== needle)
            break;
        }
        return index;
      }
      function lowerBound(haystack, needle, index) {
        for (let i = index - 1; i >= 0; index = i--) {
          if (haystack[i][COLUMN] !== needle)
            break;
        }
        return index;
      }
      function memoizedState() {
        return {
          lastKey: -1,
          lastNeedle: -1,
          lastIndex: -1
        };
      }
      function memoizedBinarySearch(haystack, needle, state, key) {
        const { lastKey, lastNeedle, lastIndex } = state;
        let low = 0;
        let high = haystack.length - 1;
        if (key === lastKey) {
          if (needle === lastNeedle) {
            found = lastIndex !== -1 && haystack[lastIndex][COLUMN] === needle;
            return lastIndex;
          }
          if (needle >= lastNeedle) {
            low = lastIndex === -1 ? 0 : lastIndex;
          } else {
            high = lastIndex;
          }
        }
        state.lastKey = key;
        state.lastNeedle = needle;
        return state.lastIndex = binarySearch(haystack, needle, low, high);
      }
      function buildBySources(decoded, memos) {
        const sources = memos.map(buildNullArray);
        for (let i = 0; i < decoded.length; i++) {
          const line = decoded[i];
          for (let j = 0; j < line.length; j++) {
            const seg = line[j];
            if (seg.length === 1)
              continue;
            const sourceIndex2 = seg[SOURCES_INDEX];
            const sourceLine = seg[SOURCE_LINE];
            const sourceColumn = seg[SOURCE_COLUMN];
            const originalSource = sources[sourceIndex2];
            const originalLine = originalSource[sourceLine] || (originalSource[sourceLine] = []);
            const memo = memos[sourceIndex2];
            let index = upperBound(originalLine, sourceColumn, memoizedBinarySearch(originalLine, sourceColumn, memo, sourceLine));
            memo.lastIndex = ++index;
            insert(originalLine, index, [sourceColumn, i, seg[COLUMN]]);
          }
        }
        return sources;
      }
      function insert(array, index, value) {
        for (let i = array.length; i > index; i--) {
          array[i] = array[i - 1];
        }
        array[index] = value;
      }
      function buildNullArray() {
        return { __proto__: null };
      }
      const AnyMap = function(map, mapUrl) {
        const parsed = parse(map);
        if (!("sections" in parsed)) {
          return new TraceMap(parsed, mapUrl);
        }
        const mappings = [];
        const sources = [];
        const sourcesContent = [];
        const names = [];
        const ignoreList = [];
        recurse(parsed, mapUrl, mappings, sources, sourcesContent, names, ignoreList, 0, 0, Infinity, Infinity);
        const joined = {
          version: 3,
          file: parsed.file,
          names,
          sources,
          sourcesContent,
          mappings,
          ignoreList
        };
        return presortedDecodedMap(joined);
      };
      function parse(map) {
        return typeof map === "string" ? JSON.parse(map) : map;
      }
      function recurse(input, mapUrl, mappings, sources, sourcesContent, names, ignoreList, lineOffset, columnOffset, stopLine, stopColumn) {
        const { sections } = input;
        for (let i = 0; i < sections.length; i++) {
          const { map, offset } = sections[i];
          let sl = stopLine;
          let sc = stopColumn;
          if (i + 1 < sections.length) {
            const nextOffset = sections[i + 1].offset;
            sl = Math.min(stopLine, lineOffset + nextOffset.line);
            if (sl === stopLine) {
              sc = Math.min(stopColumn, columnOffset + nextOffset.column);
            } else if (sl < stopLine) {
              sc = columnOffset + nextOffset.column;
            }
          }
          addSection(map, mapUrl, mappings, sources, sourcesContent, names, ignoreList, lineOffset + offset.line, columnOffset + offset.column, sl, sc);
        }
      }
      function addSection(input, mapUrl, mappings, sources, sourcesContent, names, ignoreList, lineOffset, columnOffset, stopLine, stopColumn) {
        const parsed = parse(input);
        if ("sections" in parsed)
          return recurse(...arguments);
        const map = new TraceMap(parsed, mapUrl);
        const sourcesOffset = sources.length;
        const namesOffset = names.length;
        const decoded = decodedMappings(map);
        const { resolvedSources, sourcesContent: contents, ignoreList: ignores } = map;
        append(sources, resolvedSources);
        append(names, map.names);
        if (contents)
          append(sourcesContent, contents);
        else
          for (let i = 0; i < resolvedSources.length; i++)
            sourcesContent.push(null);
        if (ignores)
          for (let i = 0; i < ignores.length; i++)
            ignoreList.push(ignores[i] + sourcesOffset);
        for (let i = 0; i < decoded.length; i++) {
          const lineI = lineOffset + i;
          if (lineI > stopLine)
            return;
          const out = getLine(mappings, lineI);
          const cOffset = i === 0 ? columnOffset : 0;
          const line = decoded[i];
          for (let j = 0; j < line.length; j++) {
            const seg = line[j];
            const column = cOffset + seg[COLUMN];
            if (lineI === stopLine && column >= stopColumn)
              return;
            if (seg.length === 1) {
              out.push([column]);
              continue;
            }
            const sourcesIndex = sourcesOffset + seg[SOURCES_INDEX];
            const sourceLine = seg[SOURCE_LINE];
            const sourceColumn = seg[SOURCE_COLUMN];
            out.push(seg.length === 4 ? [column, sourcesIndex, sourceLine, sourceColumn] : [column, sourcesIndex, sourceLine, sourceColumn, namesOffset + seg[NAMES_INDEX]]);
          }
        }
      }
      function append(arr, other) {
        for (let i = 0; i < other.length; i++)
          arr.push(other[i]);
      }
      function getLine(arr, index) {
        for (let i = arr.length; i <= index; i++)
          arr[i] = [];
        return arr[index];
      }
      const LINE_GTR_ZERO = "`line` must be greater than 0 (lines start at line 1)";
      const COL_GTR_EQ_ZERO = "`column` must be greater than or equal to 0 (columns start at column 0)";
      const LEAST_UPPER_BOUND = -1;
      const GREATEST_LOWER_BOUND = 1;
      class TraceMap {
        constructor(map, mapUrl) {
          const isString = typeof map === "string";
          if (!isString && map._decodedMemo)
            return map;
          const parsed = isString ? JSON.parse(map) : map;
          const { version, file, names, sourceRoot, sources, sourcesContent } = parsed;
          this.version = version;
          this.file = file;
          this.names = names || [];
          this.sourceRoot = sourceRoot;
          this.sources = sources;
          this.sourcesContent = sourcesContent;
          this.ignoreList = parsed.ignoreList || parsed.x_google_ignoreList || void 0;
          const from = resolve(sourceRoot || "", stripFilename(mapUrl));
          this.resolvedSources = sources.map((s) => resolve(s || "", from));
          const { mappings } = parsed;
          if (typeof mappings === "string") {
            this._encoded = mappings;
            this._decoded = void 0;
          } else {
            this._encoded = void 0;
            this._decoded = maybeSort(mappings, isString);
          }
          this._decodedMemo = memoizedState();
          this._bySources = void 0;
          this._bySourceMemos = void 0;
        }
      }
      function cast(map) {
        return map;
      }
      function encodedMappings(map) {
        var _a;
        var _b;
        return (_a = (_b = cast(map))._encoded) !== null && _a !== void 0 ? _a : _b._encoded = sourcemapCodec.encode(cast(map)._decoded);
      }
      function decodedMappings(map) {
        var _a;
        return (_a = cast(map))._decoded || (_a._decoded = sourcemapCodec.decode(cast(map)._encoded));
      }
      function traceSegment(map, line, column) {
        const decoded = decodedMappings(map);
        if (line >= decoded.length)
          return null;
        const segments = decoded[line];
        const index = traceSegmentInternal(segments, cast(map)._decodedMemo, line, column, GREATEST_LOWER_BOUND);
        return index === -1 ? null : segments[index];
      }
      function originalPositionFor(map, needle) {
        let { line, column, bias } = needle;
        line--;
        if (line < 0)
          throw new Error(LINE_GTR_ZERO);
        if (column < 0)
          throw new Error(COL_GTR_EQ_ZERO);
        const decoded = decodedMappings(map);
        if (line >= decoded.length)
          return OMapping(null, null, null, null);
        const segments = decoded[line];
        const index = traceSegmentInternal(segments, cast(map)._decodedMemo, line, column, bias || GREATEST_LOWER_BOUND);
        if (index === -1)
          return OMapping(null, null, null, null);
        const segment = segments[index];
        if (segment.length === 1)
          return OMapping(null, null, null, null);
        const { names, resolvedSources } = map;
        return OMapping(resolvedSources[segment[SOURCES_INDEX]], segment[SOURCE_LINE] + 1, segment[SOURCE_COLUMN], segment.length === 5 ? names[segment[NAMES_INDEX]] : null);
      }
      function generatedPositionFor(map, needle) {
        const { source, line, column, bias } = needle;
        return generatedPosition(map, source, line, column, bias || GREATEST_LOWER_BOUND, false);
      }
      function allGeneratedPositionsFor(map, needle) {
        const { source, line, column, bias } = needle;
        return generatedPosition(map, source, line, column, bias || LEAST_UPPER_BOUND, true);
      }
      function eachMapping(map, cb) {
        const decoded = decodedMappings(map);
        const { names, resolvedSources } = map;
        for (let i = 0; i < decoded.length; i++) {
          const line = decoded[i];
          for (let j = 0; j < line.length; j++) {
            const seg = line[j];
            const generatedLine = i + 1;
            const generatedColumn = seg[0];
            let source = null;
            let originalLine = null;
            let originalColumn = null;
            let name = null;
            if (seg.length !== 1) {
              source = resolvedSources[seg[1]];
              originalLine = seg[2] + 1;
              originalColumn = seg[3];
            }
            if (seg.length === 5)
              name = names[seg[4]];
            cb({
              generatedLine,
              generatedColumn,
              source,
              originalLine,
              originalColumn,
              name
            });
          }
        }
      }
      function sourceIndex(map, source) {
        const { sources, resolvedSources } = map;
        let index = sources.indexOf(source);
        if (index === -1)
          index = resolvedSources.indexOf(source);
        return index;
      }
      function sourceContentFor(map, source) {
        const { sourcesContent } = map;
        if (sourcesContent == null)
          return null;
        const index = sourceIndex(map, source);
        return index === -1 ? null : sourcesContent[index];
      }
      function isIgnored(map, source) {
        const { ignoreList } = map;
        if (ignoreList == null)
          return false;
        const index = sourceIndex(map, source);
        return index === -1 ? false : ignoreList.includes(index);
      }
      function presortedDecodedMap(map, mapUrl) {
        const tracer = new TraceMap(clone(map, []), mapUrl);
        cast(tracer)._decoded = map.mappings;
        return tracer;
      }
      function decodedMap(map) {
        return clone(map, decodedMappings(map));
      }
      function encodedMap(map) {
        return clone(map, encodedMappings(map));
      }
      function clone(map, mappings) {
        return {
          version: map.version,
          file: map.file,
          names: map.names,
          sourceRoot: map.sourceRoot,
          sources: map.sources,
          sourcesContent: map.sourcesContent,
          mappings,
          ignoreList: map.ignoreList || map.x_google_ignoreList
        };
      }
      function OMapping(source, line, column, name) {
        return { source, line, column, name };
      }
      function GMapping(line, column) {
        return { line, column };
      }
      function traceSegmentInternal(segments, memo, line, column, bias) {
        let index = memoizedBinarySearch(segments, column, memo, line);
        if (found) {
          index = (bias === LEAST_UPPER_BOUND ? upperBound : lowerBound)(segments, column, index);
        } else if (bias === LEAST_UPPER_BOUND)
          index++;
        if (index === -1 || index === segments.length)
          return -1;
        return index;
      }
      function sliceGeneratedPositions(segments, memo, line, column, bias) {
        let min = traceSegmentInternal(segments, memo, line, column, GREATEST_LOWER_BOUND);
        if (!found && bias === LEAST_UPPER_BOUND)
          min++;
        if (min === -1 || min === segments.length)
          return [];
        const matchedColumn = found ? column : segments[min][COLUMN];
        if (!found)
          min = lowerBound(segments, matchedColumn, min);
        const max = upperBound(segments, matchedColumn, min);
        const result = [];
        for (; min <= max; min++) {
          const segment = segments[min];
          result.push(GMapping(segment[REV_GENERATED_LINE] + 1, segment[REV_GENERATED_COLUMN]));
        }
        return result;
      }
      function generatedPosition(map, source, line, column, bias, all) {
        var _a;
        line--;
        if (line < 0)
          throw new Error(LINE_GTR_ZERO);
        if (column < 0)
          throw new Error(COL_GTR_EQ_ZERO);
        const { sources, resolvedSources } = map;
        let sourceIndex2 = sources.indexOf(source);
        if (sourceIndex2 === -1)
          sourceIndex2 = resolvedSources.indexOf(source);
        if (sourceIndex2 === -1)
          return all ? [] : GMapping(null, null);
        const generated = (_a = cast(map))._bySources || (_a._bySources = buildBySources(decodedMappings(map), cast(map)._bySourceMemos = sources.map(memoizedState)));
        const segments = generated[sourceIndex2][line];
        if (segments == null)
          return all ? [] : GMapping(null, null);
        const memo = cast(map)._bySourceMemos[sourceIndex2];
        if (all)
          return sliceGeneratedPositions(segments, memo, line, column, bias);
        const index = traceSegmentInternal(segments, memo, line, column, bias);
        if (index === -1)
          return GMapping(null, null);
        const segment = segments[index];
        return GMapping(segment[REV_GENERATED_LINE] + 1, segment[REV_GENERATED_COLUMN]);
      }
      exports2.AnyMap = AnyMap;
      exports2.GREATEST_LOWER_BOUND = GREATEST_LOWER_BOUND;
      exports2.LEAST_UPPER_BOUND = LEAST_UPPER_BOUND;
      exports2.TraceMap = TraceMap;
      exports2.allGeneratedPositionsFor = allGeneratedPositionsFor;
      exports2.decodedMap = decodedMap;
      exports2.decodedMappings = decodedMappings;
      exports2.eachMapping = eachMapping;
      exports2.encodedMap = encodedMap;
      exports2.encodedMappings = encodedMappings;
      exports2.generatedPositionFor = generatedPositionFor;
      exports2.isIgnored = isIgnored;
      exports2.originalPositionFor = originalPositionFor;
      exports2.presortedDecodedMap = presortedDecodedMap;
      exports2.sourceContentFor = sourceContentFor;
      exports2.traceSegment = traceSegment;
    });
  }
});

// node_modules/@jridgewell/set-array/dist/set-array.umd.js
var require_set_array_umd = __commonJS({
  "node_modules/@jridgewell/set-array/dist/set-array.umd.js"(exports, module) {
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? factory(exports) : typeof define === "function" && define.amd ? define(["exports"], factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, factory(global.setArray = {}));
    })(exports, function(exports2) {
      "use strict";
      class SetArray {
        constructor() {
          this._indexes = { __proto__: null };
          this.array = [];
        }
      }
      function cast(set) {
        return set;
      }
      function get(setarr, key) {
        return cast(setarr)._indexes[key];
      }
      function put(setarr, key) {
        const index = get(setarr, key);
        if (index !== void 0)
          return index;
        const { array, _indexes: indexes } = cast(setarr);
        const length = array.push(key);
        return indexes[key] = length - 1;
      }
      function pop(setarr) {
        const { array, _indexes: indexes } = cast(setarr);
        if (array.length === 0)
          return;
        const last = array.pop();
        indexes[last] = void 0;
      }
      function remove(setarr, key) {
        const index = get(setarr, key);
        if (index === void 0)
          return;
        const { array, _indexes: indexes } = cast(setarr);
        for (let i = index + 1; i < array.length; i++) {
          const k = array[i];
          array[i - 1] = k;
          indexes[k]--;
        }
        indexes[key] = void 0;
        array.pop();
      }
      exports2.SetArray = SetArray;
      exports2.get = get;
      exports2.pop = pop;
      exports2.put = put;
      exports2.remove = remove;
      Object.defineProperty(exports2, "__esModule", { value: true });
    });
  }
});

// node_modules/@jridgewell/gen-mapping/dist/gen-mapping.umd.js
var require_gen_mapping_umd = __commonJS({
  "node_modules/@jridgewell/gen-mapping/dist/gen-mapping.umd.js"(exports, module) {
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? factory(exports, require_set_array_umd(), require_sourcemap_codec_umd(), require_trace_mapping_umd()) : typeof define === "function" && define.amd ? define(["exports", "@jridgewell/set-array", "@jridgewell/sourcemap-codec", "@jridgewell/trace-mapping"], factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, factory(global.genMapping = {}, global.setArray, global.sourcemapCodec, global.traceMapping));
    })(exports, function(exports2, setArray, sourcemapCodec, traceMapping) {
      "use strict";
      const COLUMN = 0;
      const SOURCES_INDEX = 1;
      const SOURCE_LINE = 2;
      const SOURCE_COLUMN = 3;
      const NAMES_INDEX = 4;
      const NO_NAME = -1;
      class GenMapping {
        constructor({ file, sourceRoot } = {}) {
          this._names = new setArray.SetArray();
          this._sources = new setArray.SetArray();
          this._sourcesContent = [];
          this._mappings = [];
          this.file = file;
          this.sourceRoot = sourceRoot;
          this._ignoreList = new setArray.SetArray();
        }
      }
      function cast(map) {
        return map;
      }
      function addSegment(map, genLine, genColumn, source, sourceLine, sourceColumn, name, content) {
        return addSegmentInternal(false, map, genLine, genColumn, source, sourceLine, sourceColumn, name, content);
      }
      function addMapping(map, mapping) {
        return addMappingInternal(false, map, mapping);
      }
      const maybeAddSegment = (map, genLine, genColumn, source, sourceLine, sourceColumn, name, content) => {
        return addSegmentInternal(true, map, genLine, genColumn, source, sourceLine, sourceColumn, name, content);
      };
      const maybeAddMapping = (map, mapping) => {
        return addMappingInternal(true, map, mapping);
      };
      function setSourceContent(map, source, content) {
        const { _sources: sources, _sourcesContent: sourcesContent } = cast(map);
        const index = setArray.put(sources, source);
        sourcesContent[index] = content;
      }
      function setIgnore(map, source, ignore = true) {
        const { _sources: sources, _sourcesContent: sourcesContent, _ignoreList: ignoreList } = cast(map);
        const index = setArray.put(sources, source);
        if (index === sourcesContent.length)
          sourcesContent[index] = null;
        if (ignore)
          setArray.put(ignoreList, index);
        else
          setArray.remove(ignoreList, index);
      }
      function toDecodedMap(map) {
        const { _mappings: mappings, _sources: sources, _sourcesContent: sourcesContent, _names: names, _ignoreList: ignoreList } = cast(map);
        removeEmptyFinalLines(mappings);
        return {
          version: 3,
          file: map.file || void 0,
          names: names.array,
          sourceRoot: map.sourceRoot || void 0,
          sources: sources.array,
          sourcesContent,
          mappings,
          ignoreList: ignoreList.array
        };
      }
      function toEncodedMap(map) {
        const decoded = toDecodedMap(map);
        return Object.assign(Object.assign({}, decoded), { mappings: sourcemapCodec.encode(decoded.mappings) });
      }
      function fromMap(input) {
        const map = new traceMapping.TraceMap(input);
        const gen = new GenMapping({ file: map.file, sourceRoot: map.sourceRoot });
        putAll(cast(gen)._names, map.names);
        putAll(cast(gen)._sources, map.sources);
        cast(gen)._sourcesContent = map.sourcesContent || map.sources.map(() => null);
        cast(gen)._mappings = traceMapping.decodedMappings(map);
        if (map.ignoreList)
          putAll(cast(gen)._ignoreList, map.ignoreList);
        return gen;
      }
      function allMappings(map) {
        const out = [];
        const { _mappings: mappings, _sources: sources, _names: names } = cast(map);
        for (let i = 0; i < mappings.length; i++) {
          const line = mappings[i];
          for (let j = 0; j < line.length; j++) {
            const seg = line[j];
            const generated = { line: i + 1, column: seg[COLUMN] };
            let source = void 0;
            let original = void 0;
            let name = void 0;
            if (seg.length !== 1) {
              source = sources.array[seg[SOURCES_INDEX]];
              original = { line: seg[SOURCE_LINE] + 1, column: seg[SOURCE_COLUMN] };
              if (seg.length === 5)
                name = names.array[seg[NAMES_INDEX]];
            }
            out.push({ generated, source, original, name });
          }
        }
        return out;
      }
      function addSegmentInternal(skipable, map, genLine, genColumn, source, sourceLine, sourceColumn, name, content) {
        const { _mappings: mappings, _sources: sources, _sourcesContent: sourcesContent, _names: names } = cast(map);
        const line = getLine(mappings, genLine);
        const index = getColumnIndex(line, genColumn);
        if (!source) {
          if (skipable && skipSourceless(line, index))
            return;
          return insert(line, index, [genColumn]);
        }
        const sourcesIndex = setArray.put(sources, source);
        const namesIndex = name ? setArray.put(names, name) : NO_NAME;
        if (sourcesIndex === sourcesContent.length)
          sourcesContent[sourcesIndex] = content !== null && content !== void 0 ? content : null;
        if (skipable && skipSource(line, index, sourcesIndex, sourceLine, sourceColumn, namesIndex)) {
          return;
        }
        return insert(line, index, name ? [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex] : [genColumn, sourcesIndex, sourceLine, sourceColumn]);
      }
      function getLine(mappings, index) {
        for (let i = mappings.length; i <= index; i++) {
          mappings[i] = [];
        }
        return mappings[index];
      }
      function getColumnIndex(line, genColumn) {
        let index = line.length;
        for (let i = index - 1; i >= 0; index = i--) {
          const current = line[i];
          if (genColumn >= current[COLUMN])
            break;
        }
        return index;
      }
      function insert(array, index, value) {
        for (let i = array.length; i > index; i--) {
          array[i] = array[i - 1];
        }
        array[index] = value;
      }
      function removeEmptyFinalLines(mappings) {
        const { length } = mappings;
        let len = length;
        for (let i = len - 1; i >= 0; len = i, i--) {
          if (mappings[i].length > 0)
            break;
        }
        if (len < length)
          mappings.length = len;
      }
      function putAll(setarr, array) {
        for (let i = 0; i < array.length; i++)
          setArray.put(setarr, array[i]);
      }
      function skipSourceless(line, index) {
        if (index === 0)
          return true;
        const prev = line[index - 1];
        return prev.length === 1;
      }
      function skipSource(line, index, sourcesIndex, sourceLine, sourceColumn, namesIndex) {
        if (index === 0)
          return false;
        const prev = line[index - 1];
        if (prev.length === 1)
          return false;
        return sourcesIndex === prev[SOURCES_INDEX] && sourceLine === prev[SOURCE_LINE] && sourceColumn === prev[SOURCE_COLUMN] && namesIndex === (prev.length === 5 ? prev[NAMES_INDEX] : NO_NAME);
      }
      function addMappingInternal(skipable, map, mapping) {
        const { generated, source, original, name, content } = mapping;
        if (!source) {
          return addSegmentInternal(skipable, map, generated.line - 1, generated.column, null, null, null, null, null);
        }
        return addSegmentInternal(skipable, map, generated.line - 1, generated.column, source, original.line - 1, original.column, name, content);
      }
      exports2.GenMapping = GenMapping;
      exports2.addMapping = addMapping;
      exports2.addSegment = addSegment;
      exports2.allMappings = allMappings;
      exports2.fromMap = fromMap;
      exports2.maybeAddMapping = maybeAddMapping;
      exports2.maybeAddSegment = maybeAddSegment;
      exports2.setIgnore = setIgnore;
      exports2.setSourceContent = setSourceContent;
      exports2.toDecodedMap = toDecodedMap;
      exports2.toEncodedMap = toEncodedMap;
      Object.defineProperty(exports2, "__esModule", { value: true });
    });
  }
});

// node_modules/@ampproject/remapping/dist/remapping.umd.js
var require_remapping_umd = __commonJS({
  "node_modules/@ampproject/remapping/dist/remapping.umd.js"(exports, module) {
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? module.exports = factory(require_trace_mapping_umd(), require_gen_mapping_umd()) : typeof define === "function" && define.amd ? define(["@jridgewell/trace-mapping", "@jridgewell/gen-mapping"], factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, global.remapping = factory(global.traceMapping, global.genMapping));
    })(exports, function(traceMapping, genMapping) {
      "use strict";
      const SOURCELESS_MAPPING = SegmentObject("", -1, -1, "", null, false);
      const EMPTY_SOURCES = [];
      function SegmentObject(source, line, column, name, content, ignore) {
        return { source, line, column, name, content, ignore };
      }
      function Source(map, sources, source, content, ignore) {
        return {
          map,
          sources,
          source,
          content,
          ignore
        };
      }
      function MapSource(map, sources) {
        return Source(map, sources, "", null, false);
      }
      function OriginalSource(source, content, ignore) {
        return Source(null, EMPTY_SOURCES, source, content, ignore);
      }
      function traceMappings(tree) {
        const gen = new genMapping.GenMapping({ file: tree.map.file });
        const { sources: rootSources, map } = tree;
        const rootNames = map.names;
        const rootMappings = traceMapping.decodedMappings(map);
        for (let i = 0; i < rootMappings.length; i++) {
          const segments = rootMappings[i];
          for (let j = 0; j < segments.length; j++) {
            const segment = segments[j];
            const genCol = segment[0];
            let traced = SOURCELESS_MAPPING;
            if (segment.length !== 1) {
              const source2 = rootSources[segment[1]];
              traced = originalPositionFor(source2, segment[2], segment[3], segment.length === 5 ? rootNames[segment[4]] : "");
              if (traced == null)
                continue;
            }
            const { column, line, name, content, source, ignore } = traced;
            genMapping.maybeAddSegment(gen, i, genCol, source, line, column, name);
            if (source && content != null)
              genMapping.setSourceContent(gen, source, content);
            if (ignore)
              genMapping.setIgnore(gen, source, true);
          }
        }
        return gen;
      }
      function originalPositionFor(source, line, column, name) {
        if (!source.map) {
          return SegmentObject(source.source, line, column, name, source.content, source.ignore);
        }
        const segment = traceMapping.traceSegment(source.map, line, column);
        if (segment == null)
          return null;
        if (segment.length === 1)
          return SOURCELESS_MAPPING;
        return originalPositionFor(source.sources[segment[1]], segment[2], segment[3], segment.length === 5 ? source.map.names[segment[4]] : name);
      }
      function asArray(value) {
        if (Array.isArray(value))
          return value;
        return [value];
      }
      function buildSourceMapTree(input, loader) {
        const maps = asArray(input).map((m) => new traceMapping.TraceMap(m, ""));
        const map = maps.pop();
        for (let i = 0; i < maps.length; i++) {
          if (maps[i].sources.length > 1) {
            throw new Error(`Transformation map ${i} must have exactly one source file.
Did you specify these with the most recent transformation maps first?`);
          }
        }
        let tree = build(map, loader, "", 0);
        for (let i = maps.length - 1; i >= 0; i--) {
          tree = MapSource(maps[i], [tree]);
        }
        return tree;
      }
      function build(map, loader, importer, importerDepth) {
        const { resolvedSources, sourcesContent, ignoreList } = map;
        const depth = importerDepth + 1;
        const children = resolvedSources.map((sourceFile, i) => {
          const ctx = {
            importer,
            depth,
            source: sourceFile || "",
            content: void 0,
            ignore: void 0
          };
          const sourceMap = loader(ctx.source, ctx);
          const { source, content, ignore } = ctx;
          if (sourceMap)
            return build(new traceMapping.TraceMap(sourceMap, source), loader, source, depth);
          const sourceContent = content !== void 0 ? content : sourcesContent ? sourcesContent[i] : null;
          const ignored = ignore !== void 0 ? ignore : ignoreList ? ignoreList.includes(i) : false;
          return OriginalSource(source, sourceContent, ignored);
        });
        return MapSource(map, children);
      }
      class SourceMap {
        constructor(map, options) {
          const out = options.decodedMappings ? genMapping.toDecodedMap(map) : genMapping.toEncodedMap(map);
          this.version = out.version;
          this.file = out.file;
          this.mappings = out.mappings;
          this.names = out.names;
          this.ignoreList = out.ignoreList;
          this.sourceRoot = out.sourceRoot;
          this.sources = out.sources;
          if (!options.excludeContent) {
            this.sourcesContent = out.sourcesContent;
          }
        }
        toString() {
          return JSON.stringify(this);
        }
      }
      function remapping(input, loader, options) {
        const opts = typeof options === "object" ? options : { excludeContent: !!options, decodedMappings: false };
        const tree = buildSourceMapTree(input, loader);
        return new SourceMap(traceMappings(tree), opts);
      }
      return remapping;
    });
  }
});
export default require_remapping_umd();
//# sourceMappingURL=@ampproject_remapping.js.map

import {
  MarkupIcon,
  __commonJS,
  __toESM as __toESM2,
  debounce2,
  getControlId
} from "./chunk-BMOEWYDM.js";
import {
  B3,
  D7,
  X3,
  xr
} from "./chunk-2NOI57PC.js";
import "./chunk-H4EEZRGF.js";
import "./chunk-4K3VBXQL.js";
import "./chunk-3EP2NFP4.js";
import "./chunk-E4Q3YXXP.js";
import "./chunk-GF7VUYY4.js";
import {
  require_react
} from "./chunk-XQWWUXQD.js";
import {
  __toESM
} from "./chunk-KEXKKQVW.js";

// node_modules/@storybook/addon-docs/dist/Color-AVL7NMMY.mjs
var import_react = __toESM(require_react(), 1);
var require_color_name = __commonJS({ "../../node_modules/color-name/index.js"(exports, module) {
  module.exports = { aliceblue: [240, 248, 255], antiquewhite: [250, 235, 215], aqua: [0, 255, 255], aquamarine: [127, 255, 212], azure: [240, 255, 255], beige: [245, 245, 220], bisque: [255, 228, 196], black: [0, 0, 0], blanchedalmond: [255, 235, 205], blue: [0, 0, 255], blueviolet: [138, 43, 226], brown: [165, 42, 42], burlywood: [222, 184, 135], cadetblue: [95, 158, 160], chartreuse: [127, 255, 0], chocolate: [210, 105, 30], coral: [255, 127, 80], cornflowerblue: [100, 149, 237], cornsilk: [255, 248, 220], crimson: [220, 20, 60], cyan: [0, 255, 255], darkblue: [0, 0, 139], darkcyan: [0, 139, 139], darkgoldenrod: [184, 134, 11], darkgray: [169, 169, 169], darkgreen: [0, 100, 0], darkgrey: [169, 169, 169], darkkhaki: [189, 183, 107], darkmagenta: [139, 0, 139], darkolivegreen: [85, 107, 47], darkorange: [255, 140, 0], darkorchid: [153, 50, 204], darkred: [139, 0, 0], darksalmon: [233, 150, 122], darkseagreen: [143, 188, 143], darkslateblue: [72, 61, 139], darkslategray: [47, 79, 79], darkslategrey: [47, 79, 79], darkturquoise: [0, 206, 209], darkviolet: [148, 0, 211], deeppink: [255, 20, 147], deepskyblue: [0, 191, 255], dimgray: [105, 105, 105], dimgrey: [105, 105, 105], dodgerblue: [30, 144, 255], firebrick: [178, 34, 34], floralwhite: [255, 250, 240], forestgreen: [34, 139, 34], fuchsia: [255, 0, 255], gainsboro: [220, 220, 220], ghostwhite: [248, 248, 255], gold: [255, 215, 0], goldenrod: [218, 165, 32], gray: [128, 128, 128], green: [0, 128, 0], greenyellow: [173, 255, 47], grey: [128, 128, 128], honeydew: [240, 255, 240], hotpink: [255, 105, 180], indianred: [205, 92, 92], indigo: [75, 0, 130], ivory: [255, 255, 240], khaki: [240, 230, 140], lavender: [230, 230, 250], lavenderblush: [255, 240, 245], lawngreen: [124, 252, 0], lemonchiffon: [255, 250, 205], lightblue: [173, 216, 230], lightcoral: [240, 128, 128], lightcyan: [224, 255, 255], lightgoldenrodyellow: [250, 250, 210], lightgray: [211, 211, 211], lightgreen: [144, 238, 144], lightgrey: [211, 211, 211], lightpink: [255, 182, 193], lightsalmon: [255, 160, 122], lightseagreen: [32, 178, 170], lightskyblue: [135, 206, 250], lightslategray: [119, 136, 153], lightslategrey: [119, 136, 153], lightsteelblue: [176, 196, 222], lightyellow: [255, 255, 224], lime: [0, 255, 0], limegreen: [50, 205, 50], linen: [250, 240, 230], magenta: [255, 0, 255], maroon: [128, 0, 0], mediumaquamarine: [102, 205, 170], mediumblue: [0, 0, 205], mediumorchid: [186, 85, 211], mediumpurple: [147, 112, 219], mediumseagreen: [60, 179, 113], mediumslateblue: [123, 104, 238], mediumspringgreen: [0, 250, 154], mediumturquoise: [72, 209, 204], mediumvioletred: [199, 21, 133], midnightblue: [25, 25, 112], mintcream: [245, 255, 250], mistyrose: [255, 228, 225], moccasin: [255, 228, 181], navajowhite: [255, 222, 173], navy: [0, 0, 128], oldlace: [253, 245, 230], olive: [128, 128, 0], olivedrab: [107, 142, 35], orange: [255, 165, 0], orangered: [255, 69, 0], orchid: [218, 112, 214], palegoldenrod: [238, 232, 170], palegreen: [152, 251, 152], paleturquoise: [175, 238, 238], palevioletred: [219, 112, 147], papayawhip: [255, 239, 213], peachpuff: [255, 218, 185], peru: [205, 133, 63], pink: [255, 192, 203], plum: [221, 160, 221], powderblue: [176, 224, 230], purple: [128, 0, 128], rebeccapurple: [102, 51, 153], red: [255, 0, 0], rosybrown: [188, 143, 143], royalblue: [65, 105, 225], saddlebrown: [139, 69, 19], salmon: [250, 128, 114], sandybrown: [244, 164, 96], seagreen: [46, 139, 87], seashell: [255, 245, 238], sienna: [160, 82, 45], silver: [192, 192, 192], skyblue: [135, 206, 235], slateblue: [106, 90, 205], slategray: [112, 128, 144], slategrey: [112, 128, 144], snow: [255, 250, 250], springgreen: [0, 255, 127], steelblue: [70, 130, 180], tan: [210, 180, 140], teal: [0, 128, 128], thistle: [216, 191, 216], tomato: [255, 99, 71], turquoise: [64, 224, 208], violet: [238, 130, 238], wheat: [245, 222, 179], white: [255, 255, 255], whitesmoke: [245, 245, 245], yellow: [255, 255, 0], yellowgreen: [154, 205, 50] };
} });
var require_conversions = __commonJS({ "../../node_modules/color-convert/conversions.js"(exports, module) {
  var cssKeywords = require_color_name(), reverseKeywords = {};
  for (let key of Object.keys(cssKeywords)) reverseKeywords[cssKeywords[key]] = key;
  var convert2 = { rgb: { channels: 3, labels: "rgb" }, hsl: { channels: 3, labels: "hsl" }, hsv: { channels: 3, labels: "hsv" }, hwb: { channels: 3, labels: "hwb" }, cmyk: { channels: 4, labels: "cmyk" }, xyz: { channels: 3, labels: "xyz" }, lab: { channels: 3, labels: "lab" }, lch: { channels: 3, labels: "lch" }, hex: { channels: 1, labels: ["hex"] }, keyword: { channels: 1, labels: ["keyword"] }, ansi16: { channels: 1, labels: ["ansi16"] }, ansi256: { channels: 1, labels: ["ansi256"] }, hcg: { channels: 3, labels: ["h", "c", "g"] }, apple: { channels: 3, labels: ["r16", "g16", "b16"] }, gray: { channels: 1, labels: ["gray"] } };
  module.exports = convert2;
  for (let model of Object.keys(convert2)) {
    if (!("channels" in convert2[model])) throw new Error("missing channels property: " + model);
    if (!("labels" in convert2[model])) throw new Error("missing channel labels property: " + model);
    if (convert2[model].labels.length !== convert2[model].channels) throw new Error("channel and label counts mismatch: " + model);
    let { channels, labels } = convert2[model];
    delete convert2[model].channels, delete convert2[model].labels, Object.defineProperty(convert2[model], "channels", { value: channels }), Object.defineProperty(convert2[model], "labels", { value: labels });
  }
  convert2.rgb.hsl = function(rgb) {
    let r2 = rgb[0] / 255, g2 = rgb[1] / 255, b2 = rgb[2] / 255, min = Math.min(r2, g2, b2), max = Math.max(r2, g2, b2), delta = max - min, h2, s2;
    max === min ? h2 = 0 : r2 === max ? h2 = (g2 - b2) / delta : g2 === max ? h2 = 2 + (b2 - r2) / delta : b2 === max && (h2 = 4 + (r2 - g2) / delta), h2 = Math.min(h2 * 60, 360), h2 < 0 && (h2 += 360);
    let l2 = (min + max) / 2;
    return max === min ? s2 = 0 : l2 <= 0.5 ? s2 = delta / (max + min) : s2 = delta / (2 - max - min), [h2, s2 * 100, l2 * 100];
  };
  convert2.rgb.hsv = function(rgb) {
    let rdif, gdif, bdif, h2, s2, r2 = rgb[0] / 255, g2 = rgb[1] / 255, b2 = rgb[2] / 255, v2 = Math.max(r2, g2, b2), diff = v2 - Math.min(r2, g2, b2), diffc = function(c2) {
      return (v2 - c2) / 6 / diff + 1 / 2;
    };
    return diff === 0 ? (h2 = 0, s2 = 0) : (s2 = diff / v2, rdif = diffc(r2), gdif = diffc(g2), bdif = diffc(b2), r2 === v2 ? h2 = bdif - gdif : g2 === v2 ? h2 = 1 / 3 + rdif - bdif : b2 === v2 && (h2 = 2 / 3 + gdif - rdif), h2 < 0 ? h2 += 1 : h2 > 1 && (h2 -= 1)), [h2 * 360, s2 * 100, v2 * 100];
  };
  convert2.rgb.hwb = function(rgb) {
    let r2 = rgb[0], g2 = rgb[1], b2 = rgb[2], h2 = convert2.rgb.hsl(rgb)[0], w2 = 1 / 255 * Math.min(r2, Math.min(g2, b2));
    return b2 = 1 - 1 / 255 * Math.max(r2, Math.max(g2, b2)), [h2, w2 * 100, b2 * 100];
  };
  convert2.rgb.cmyk = function(rgb) {
    let r2 = rgb[0] / 255, g2 = rgb[1] / 255, b2 = rgb[2] / 255, k2 = Math.min(1 - r2, 1 - g2, 1 - b2), c2 = (1 - r2 - k2) / (1 - k2) || 0, m2 = (1 - g2 - k2) / (1 - k2) || 0, y2 = (1 - b2 - k2) / (1 - k2) || 0;
    return [c2 * 100, m2 * 100, y2 * 100, k2 * 100];
  };
  function comparativeDistance(x2, y2) {
    return (x2[0] - y2[0]) ** 2 + (x2[1] - y2[1]) ** 2 + (x2[2] - y2[2]) ** 2;
  }
  convert2.rgb.keyword = function(rgb) {
    let reversed = reverseKeywords[rgb];
    if (reversed) return reversed;
    let currentClosestDistance = 1 / 0, currentClosestKeyword;
    for (let keyword of Object.keys(cssKeywords)) {
      let value = cssKeywords[keyword], distance = comparativeDistance(rgb, value);
      distance < currentClosestDistance && (currentClosestDistance = distance, currentClosestKeyword = keyword);
    }
    return currentClosestKeyword;
  };
  convert2.keyword.rgb = function(keyword) {
    return cssKeywords[keyword];
  };
  convert2.rgb.xyz = function(rgb) {
    let r2 = rgb[0] / 255, g2 = rgb[1] / 255, b2 = rgb[2] / 255;
    r2 = r2 > 0.04045 ? ((r2 + 0.055) / 1.055) ** 2.4 : r2 / 12.92, g2 = g2 > 0.04045 ? ((g2 + 0.055) / 1.055) ** 2.4 : g2 / 12.92, b2 = b2 > 0.04045 ? ((b2 + 0.055) / 1.055) ** 2.4 : b2 / 12.92;
    let x2 = r2 * 0.4124 + g2 * 0.3576 + b2 * 0.1805, y2 = r2 * 0.2126 + g2 * 0.7152 + b2 * 0.0722, z2 = r2 * 0.0193 + g2 * 0.1192 + b2 * 0.9505;
    return [x2 * 100, y2 * 100, z2 * 100];
  };
  convert2.rgb.lab = function(rgb) {
    let xyz = convert2.rgb.xyz(rgb), x2 = xyz[0], y2 = xyz[1], z2 = xyz[2];
    x2 /= 95.047, y2 /= 100, z2 /= 108.883, x2 = x2 > 8856e-6 ? x2 ** (1 / 3) : 7.787 * x2 + 16 / 116, y2 = y2 > 8856e-6 ? y2 ** (1 / 3) : 7.787 * y2 + 16 / 116, z2 = z2 > 8856e-6 ? z2 ** (1 / 3) : 7.787 * z2 + 16 / 116;
    let l2 = 116 * y2 - 16, a2 = 500 * (x2 - y2), b2 = 200 * (y2 - z2);
    return [l2, a2, b2];
  };
  convert2.hsl.rgb = function(hsl) {
    let h2 = hsl[0] / 360, s2 = hsl[1] / 100, l2 = hsl[2] / 100, t2, t3, val;
    if (s2 === 0) return val = l2 * 255, [val, val, val];
    l2 < 0.5 ? t2 = l2 * (1 + s2) : t2 = l2 + s2 - l2 * s2;
    let t1 = 2 * l2 - t2, rgb = [0, 0, 0];
    for (let i2 = 0; i2 < 3; i2++) t3 = h2 + 1 / 3 * -(i2 - 1), t3 < 0 && t3++, t3 > 1 && t3--, 6 * t3 < 1 ? val = t1 + (t2 - t1) * 6 * t3 : 2 * t3 < 1 ? val = t2 : 3 * t3 < 2 ? val = t1 + (t2 - t1) * (2 / 3 - t3) * 6 : val = t1, rgb[i2] = val * 255;
    return rgb;
  };
  convert2.hsl.hsv = function(hsl) {
    let h2 = hsl[0], s2 = hsl[1] / 100, l2 = hsl[2] / 100, smin = s2, lmin = Math.max(l2, 0.01);
    l2 *= 2, s2 *= l2 <= 1 ? l2 : 2 - l2, smin *= lmin <= 1 ? lmin : 2 - lmin;
    let v2 = (l2 + s2) / 2, sv = l2 === 0 ? 2 * smin / (lmin + smin) : 2 * s2 / (l2 + s2);
    return [h2, sv * 100, v2 * 100];
  };
  convert2.hsv.rgb = function(hsv) {
    let h2 = hsv[0] / 60, s2 = hsv[1] / 100, v2 = hsv[2] / 100, hi = Math.floor(h2) % 6, f2 = h2 - Math.floor(h2), p2 = 255 * v2 * (1 - s2), q2 = 255 * v2 * (1 - s2 * f2), t2 = 255 * v2 * (1 - s2 * (1 - f2));
    switch (v2 *= 255, hi) {
      case 0:
        return [v2, t2, p2];
      case 1:
        return [q2, v2, p2];
      case 2:
        return [p2, v2, t2];
      case 3:
        return [p2, q2, v2];
      case 4:
        return [t2, p2, v2];
      case 5:
        return [v2, p2, q2];
    }
  };
  convert2.hsv.hsl = function(hsv) {
    let h2 = hsv[0], s2 = hsv[1] / 100, v2 = hsv[2] / 100, vmin = Math.max(v2, 0.01), sl, l2;
    l2 = (2 - s2) * v2;
    let lmin = (2 - s2) * vmin;
    return sl = s2 * vmin, sl /= lmin <= 1 ? lmin : 2 - lmin, sl = sl || 0, l2 /= 2, [h2, sl * 100, l2 * 100];
  };
  convert2.hwb.rgb = function(hwb) {
    let h2 = hwb[0] / 360, wh = hwb[1] / 100, bl = hwb[2] / 100, ratio = wh + bl, f2;
    ratio > 1 && (wh /= ratio, bl /= ratio);
    let i2 = Math.floor(6 * h2), v2 = 1 - bl;
    f2 = 6 * h2 - i2, (i2 & 1) !== 0 && (f2 = 1 - f2);
    let n2 = wh + f2 * (v2 - wh), r2, g2, b2;
    switch (i2) {
      default:
      case 6:
      case 0:
        r2 = v2, g2 = n2, b2 = wh;
        break;
      case 1:
        r2 = n2, g2 = v2, b2 = wh;
        break;
      case 2:
        r2 = wh, g2 = v2, b2 = n2;
        break;
      case 3:
        r2 = wh, g2 = n2, b2 = v2;
        break;
      case 4:
        r2 = n2, g2 = wh, b2 = v2;
        break;
      case 5:
        r2 = v2, g2 = wh, b2 = n2;
        break;
    }
    return [r2 * 255, g2 * 255, b2 * 255];
  };
  convert2.cmyk.rgb = function(cmyk) {
    let c2 = cmyk[0] / 100, m2 = cmyk[1] / 100, y2 = cmyk[2] / 100, k2 = cmyk[3] / 100, r2 = 1 - Math.min(1, c2 * (1 - k2) + k2), g2 = 1 - Math.min(1, m2 * (1 - k2) + k2), b2 = 1 - Math.min(1, y2 * (1 - k2) + k2);
    return [r2 * 255, g2 * 255, b2 * 255];
  };
  convert2.xyz.rgb = function(xyz) {
    let x2 = xyz[0] / 100, y2 = xyz[1] / 100, z2 = xyz[2] / 100, r2, g2, b2;
    return r2 = x2 * 3.2406 + y2 * -1.5372 + z2 * -0.4986, g2 = x2 * -0.9689 + y2 * 1.8758 + z2 * 0.0415, b2 = x2 * 0.0557 + y2 * -0.204 + z2 * 1.057, r2 = r2 > 31308e-7 ? 1.055 * r2 ** (1 / 2.4) - 0.055 : r2 * 12.92, g2 = g2 > 31308e-7 ? 1.055 * g2 ** (1 / 2.4) - 0.055 : g2 * 12.92, b2 = b2 > 31308e-7 ? 1.055 * b2 ** (1 / 2.4) - 0.055 : b2 * 12.92, r2 = Math.min(Math.max(0, r2), 1), g2 = Math.min(Math.max(0, g2), 1), b2 = Math.min(Math.max(0, b2), 1), [r2 * 255, g2 * 255, b2 * 255];
  };
  convert2.xyz.lab = function(xyz) {
    let x2 = xyz[0], y2 = xyz[1], z2 = xyz[2];
    x2 /= 95.047, y2 /= 100, z2 /= 108.883, x2 = x2 > 8856e-6 ? x2 ** (1 / 3) : 7.787 * x2 + 16 / 116, y2 = y2 > 8856e-6 ? y2 ** (1 / 3) : 7.787 * y2 + 16 / 116, z2 = z2 > 8856e-6 ? z2 ** (1 / 3) : 7.787 * z2 + 16 / 116;
    let l2 = 116 * y2 - 16, a2 = 500 * (x2 - y2), b2 = 200 * (y2 - z2);
    return [l2, a2, b2];
  };
  convert2.lab.xyz = function(lab) {
    let l2 = lab[0], a2 = lab[1], b2 = lab[2], x2, y2, z2;
    y2 = (l2 + 16) / 116, x2 = a2 / 500 + y2, z2 = y2 - b2 / 200;
    let y22 = y2 ** 3, x22 = x2 ** 3, z22 = z2 ** 3;
    return y2 = y22 > 8856e-6 ? y22 : (y2 - 16 / 116) / 7.787, x2 = x22 > 8856e-6 ? x22 : (x2 - 16 / 116) / 7.787, z2 = z22 > 8856e-6 ? z22 : (z2 - 16 / 116) / 7.787, x2 *= 95.047, y2 *= 100, z2 *= 108.883, [x2, y2, z2];
  };
  convert2.lab.lch = function(lab) {
    let l2 = lab[0], a2 = lab[1], b2 = lab[2], h2;
    h2 = Math.atan2(b2, a2) * 360 / 2 / Math.PI, h2 < 0 && (h2 += 360);
    let c2 = Math.sqrt(a2 * a2 + b2 * b2);
    return [l2, c2, h2];
  };
  convert2.lch.lab = function(lch) {
    let l2 = lch[0], c2 = lch[1], hr = lch[2] / 360 * 2 * Math.PI, a2 = c2 * Math.cos(hr), b2 = c2 * Math.sin(hr);
    return [l2, a2, b2];
  };
  convert2.rgb.ansi16 = function(args, saturation = null) {
    let [r2, g2, b2] = args, value = saturation === null ? convert2.rgb.hsv(args)[2] : saturation;
    if (value = Math.round(value / 50), value === 0) return 30;
    let ansi = 30 + (Math.round(b2 / 255) << 2 | Math.round(g2 / 255) << 1 | Math.round(r2 / 255));
    return value === 2 && (ansi += 60), ansi;
  };
  convert2.hsv.ansi16 = function(args) {
    return convert2.rgb.ansi16(convert2.hsv.rgb(args), args[2]);
  };
  convert2.rgb.ansi256 = function(args) {
    let r2 = args[0], g2 = args[1], b2 = args[2];
    return r2 === g2 && g2 === b2 ? r2 < 8 ? 16 : r2 > 248 ? 231 : Math.round((r2 - 8) / 247 * 24) + 232 : 16 + 36 * Math.round(r2 / 255 * 5) + 6 * Math.round(g2 / 255 * 5) + Math.round(b2 / 255 * 5);
  };
  convert2.ansi16.rgb = function(args) {
    let color = args % 10;
    if (color === 0 || color === 7) return args > 50 && (color += 3.5), color = color / 10.5 * 255, [color, color, color];
    let mult = (~~(args > 50) + 1) * 0.5, r2 = (color & 1) * mult * 255, g2 = (color >> 1 & 1) * mult * 255, b2 = (color >> 2 & 1) * mult * 255;
    return [r2, g2, b2];
  };
  convert2.ansi256.rgb = function(args) {
    if (args >= 232) {
      let c2 = (args - 232) * 10 + 8;
      return [c2, c2, c2];
    }
    args -= 16;
    let rem, r2 = Math.floor(args / 36) / 5 * 255, g2 = Math.floor((rem = args % 36) / 6) / 5 * 255, b2 = rem % 6 / 5 * 255;
    return [r2, g2, b2];
  };
  convert2.rgb.hex = function(args) {
    let string = (((Math.round(args[0]) & 255) << 16) + ((Math.round(args[1]) & 255) << 8) + (Math.round(args[2]) & 255)).toString(16).toUpperCase();
    return "000000".substring(string.length) + string;
  };
  convert2.hex.rgb = function(args) {
    let match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);
    if (!match) return [0, 0, 0];
    let colorString = match[0];
    match[0].length === 3 && (colorString = colorString.split("").map((char) => char + char).join(""));
    let integer = parseInt(colorString, 16), r2 = integer >> 16 & 255, g2 = integer >> 8 & 255, b2 = integer & 255;
    return [r2, g2, b2];
  };
  convert2.rgb.hcg = function(rgb) {
    let r2 = rgb[0] / 255, g2 = rgb[1] / 255, b2 = rgb[2] / 255, max = Math.max(Math.max(r2, g2), b2), min = Math.min(Math.min(r2, g2), b2), chroma = max - min, grayscale, hue;
    return chroma < 1 ? grayscale = min / (1 - chroma) : grayscale = 0, chroma <= 0 ? hue = 0 : max === r2 ? hue = (g2 - b2) / chroma % 6 : max === g2 ? hue = 2 + (b2 - r2) / chroma : hue = 4 + (r2 - g2) / chroma, hue /= 6, hue %= 1, [hue * 360, chroma * 100, grayscale * 100];
  };
  convert2.hsl.hcg = function(hsl) {
    let s2 = hsl[1] / 100, l2 = hsl[2] / 100, c2 = l2 < 0.5 ? 2 * s2 * l2 : 2 * s2 * (1 - l2), f2 = 0;
    return c2 < 1 && (f2 = (l2 - 0.5 * c2) / (1 - c2)), [hsl[0], c2 * 100, f2 * 100];
  };
  convert2.hsv.hcg = function(hsv) {
    let s2 = hsv[1] / 100, v2 = hsv[2] / 100, c2 = s2 * v2, f2 = 0;
    return c2 < 1 && (f2 = (v2 - c2) / (1 - c2)), [hsv[0], c2 * 100, f2 * 100];
  };
  convert2.hcg.rgb = function(hcg) {
    let h2 = hcg[0] / 360, c2 = hcg[1] / 100, g2 = hcg[2] / 100;
    if (c2 === 0) return [g2 * 255, g2 * 255, g2 * 255];
    let pure = [0, 0, 0], hi = h2 % 1 * 6, v2 = hi % 1, w2 = 1 - v2, mg = 0;
    switch (Math.floor(hi)) {
      case 0:
        pure[0] = 1, pure[1] = v2, pure[2] = 0;
        break;
      case 1:
        pure[0] = w2, pure[1] = 1, pure[2] = 0;
        break;
      case 2:
        pure[0] = 0, pure[1] = 1, pure[2] = v2;
        break;
      case 3:
        pure[0] = 0, pure[1] = w2, pure[2] = 1;
        break;
      case 4:
        pure[0] = v2, pure[1] = 0, pure[2] = 1;
        break;
      default:
        pure[0] = 1, pure[1] = 0, pure[2] = w2;
    }
    return mg = (1 - c2) * g2, [(c2 * pure[0] + mg) * 255, (c2 * pure[1] + mg) * 255, (c2 * pure[2] + mg) * 255];
  };
  convert2.hcg.hsv = function(hcg) {
    let c2 = hcg[1] / 100, g2 = hcg[2] / 100, v2 = c2 + g2 * (1 - c2), f2 = 0;
    return v2 > 0 && (f2 = c2 / v2), [hcg[0], f2 * 100, v2 * 100];
  };
  convert2.hcg.hsl = function(hcg) {
    let c2 = hcg[1] / 100, l2 = hcg[2] / 100 * (1 - c2) + 0.5 * c2, s2 = 0;
    return l2 > 0 && l2 < 0.5 ? s2 = c2 / (2 * l2) : l2 >= 0.5 && l2 < 1 && (s2 = c2 / (2 * (1 - l2))), [hcg[0], s2 * 100, l2 * 100];
  };
  convert2.hcg.hwb = function(hcg) {
    let c2 = hcg[1] / 100, g2 = hcg[2] / 100, v2 = c2 + g2 * (1 - c2);
    return [hcg[0], (v2 - c2) * 100, (1 - v2) * 100];
  };
  convert2.hwb.hcg = function(hwb) {
    let w2 = hwb[1] / 100, v2 = 1 - hwb[2] / 100, c2 = v2 - w2, g2 = 0;
    return c2 < 1 && (g2 = (v2 - c2) / (1 - c2)), [hwb[0], c2 * 100, g2 * 100];
  };
  convert2.apple.rgb = function(apple) {
    return [apple[0] / 65535 * 255, apple[1] / 65535 * 255, apple[2] / 65535 * 255];
  };
  convert2.rgb.apple = function(rgb) {
    return [rgb[0] / 255 * 65535, rgb[1] / 255 * 65535, rgb[2] / 255 * 65535];
  };
  convert2.gray.rgb = function(args) {
    return [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];
  };
  convert2.gray.hsl = function(args) {
    return [0, 0, args[0]];
  };
  convert2.gray.hsv = convert2.gray.hsl;
  convert2.gray.hwb = function(gray) {
    return [0, 100, gray[0]];
  };
  convert2.gray.cmyk = function(gray) {
    return [0, 0, 0, gray[0]];
  };
  convert2.gray.lab = function(gray) {
    return [gray[0], 0, 0];
  };
  convert2.gray.hex = function(gray) {
    let val = Math.round(gray[0] / 100 * 255) & 255, string = ((val << 16) + (val << 8) + val).toString(16).toUpperCase();
    return "000000".substring(string.length) + string;
  };
  convert2.rgb.gray = function(rgb) {
    return [(rgb[0] + rgb[1] + rgb[2]) / 3 / 255 * 100];
  };
} });
var require_route = __commonJS({ "../../node_modules/color-convert/route.js"(exports, module) {
  var conversions = require_conversions();
  function buildGraph() {
    let graph = {}, models = Object.keys(conversions);
    for (let len = models.length, i2 = 0; i2 < len; i2++) graph[models[i2]] = { distance: -1, parent: null };
    return graph;
  }
  function deriveBFS(fromModel) {
    let graph = buildGraph(), queue = [fromModel];
    for (graph[fromModel].distance = 0; queue.length; ) {
      let current = queue.pop(), adjacents = Object.keys(conversions[current]);
      for (let len = adjacents.length, i2 = 0; i2 < len; i2++) {
        let adjacent = adjacents[i2], node = graph[adjacent];
        node.distance === -1 && (node.distance = graph[current].distance + 1, node.parent = current, queue.unshift(adjacent));
      }
    }
    return graph;
  }
  function link(from, to) {
    return function(args) {
      return to(from(args));
    };
  }
  function wrapConversion(toModel, graph) {
    let path = [graph[toModel].parent, toModel], fn = conversions[graph[toModel].parent][toModel], cur = graph[toModel].parent;
    for (; graph[cur].parent; ) path.unshift(graph[cur].parent), fn = link(conversions[graph[cur].parent][cur], fn), cur = graph[cur].parent;
    return fn.conversion = path, fn;
  }
  module.exports = function(fromModel) {
    let graph = deriveBFS(fromModel), conversion = {}, models = Object.keys(graph);
    for (let len = models.length, i2 = 0; i2 < len; i2++) {
      let toModel = models[i2];
      graph[toModel].parent !== null && (conversion[toModel] = wrapConversion(toModel, graph));
    }
    return conversion;
  };
} });
var require_color_convert = __commonJS({ "../../node_modules/color-convert/index.js"(exports, module) {
  var conversions = require_conversions(), route = require_route(), convert2 = {}, models = Object.keys(conversions);
  function wrapRaw(fn) {
    let wrappedFn = function(...args) {
      let arg0 = args[0];
      return arg0 == null ? arg0 : (arg0.length > 1 && (args = arg0), fn(args));
    };
    return "conversion" in fn && (wrappedFn.conversion = fn.conversion), wrappedFn;
  }
  function wrapRounded(fn) {
    let wrappedFn = function(...args) {
      let arg0 = args[0];
      if (arg0 == null) return arg0;
      arg0.length > 1 && (args = arg0);
      let result = fn(args);
      if (typeof result == "object") for (let len = result.length, i2 = 0; i2 < len; i2++) result[i2] = Math.round(result[i2]);
      return result;
    };
    return "conversion" in fn && (wrappedFn.conversion = fn.conversion), wrappedFn;
  }
  models.forEach((fromModel) => {
    convert2[fromModel] = {}, Object.defineProperty(convert2[fromModel], "channels", { value: conversions[fromModel].channels }), Object.defineProperty(convert2[fromModel], "labels", { value: conversions[fromModel].labels });
    let routes = route(fromModel);
    Object.keys(routes).forEach((toModel) => {
      let fn = routes[toModel];
      convert2[fromModel][toModel] = wrapRounded(fn), convert2[fromModel][toModel].raw = wrapRaw(fn);
    });
  });
  module.exports = convert2;
} });
var import_color_convert = __toESM2(require_color_convert());
function u() {
  return (u = Object.assign || function(e2) {
    for (var r2 = 1; r2 < arguments.length; r2++) {
      var t2 = arguments[r2];
      for (var n2 in t2) Object.prototype.hasOwnProperty.call(t2, n2) && (e2[n2] = t2[n2]);
    }
    return e2;
  }).apply(this, arguments);
}
function c(e2, r2) {
  if (e2 == null) return {};
  var t2, n2, o2 = {}, a2 = Object.keys(e2);
  for (n2 = 0; n2 < a2.length; n2++) r2.indexOf(t2 = a2[n2]) >= 0 || (o2[t2] = e2[t2]);
  return o2;
}
function i(e2) {
  var t2 = (0, import_react.useRef)(e2), n2 = (0, import_react.useRef)(function(e3) {
    t2.current && t2.current(e3);
  });
  return t2.current = e2, n2.current;
}
var s = function(e2, r2, t2) {
  return r2 === void 0 && (r2 = 0), t2 === void 0 && (t2 = 1), e2 > t2 ? t2 : e2 < r2 ? r2 : e2;
};
var f = function(e2) {
  return "touches" in e2;
};
var v = function(e2) {
  return e2 && e2.ownerDocument.defaultView || self;
};
var d = function(e2, r2, t2) {
  var n2 = e2.getBoundingClientRect(), o2 = f(r2) ? function(e3, r3) {
    for (var t3 = 0; t3 < e3.length; t3++) if (e3[t3].identifier === r3) return e3[t3];
    return e3[0];
  }(r2.touches, t2) : r2;
  return { left: s((o2.pageX - (n2.left + v(e2).pageXOffset)) / n2.width), top: s((o2.pageY - (n2.top + v(e2).pageYOffset)) / n2.height) };
};
var h = function(e2) {
  !f(e2) && e2.preventDefault();
};
var m = import_react.default.memo(function(o2) {
  var a2 = o2.onMove, l2 = o2.onKey, s2 = c(o2, ["onMove", "onKey"]), m2 = (0, import_react.useRef)(null), g2 = i(a2), p2 = i(l2), b2 = (0, import_react.useRef)(null), _2 = (0, import_react.useRef)(false), x2 = (0, import_react.useMemo)(function() {
    var e2 = function(e3) {
      h(e3), (f(e3) ? e3.touches.length > 0 : e3.buttons > 0) && m2.current ? g2(d(m2.current, e3, b2.current)) : t2(false);
    }, r2 = function() {
      return t2(false);
    };
    function t2(t3) {
      var n2 = _2.current, o3 = v(m2.current), a3 = t3 ? o3.addEventListener : o3.removeEventListener;
      a3(n2 ? "touchmove" : "mousemove", e2), a3(n2 ? "touchend" : "mouseup", r2);
    }
    return [function(e3) {
      var r3 = e3.nativeEvent, n2 = m2.current;
      if (n2 && (h(r3), !function(e4, r4) {
        return r4 && !f(e4);
      }(r3, _2.current) && n2)) {
        if (f(r3)) {
          _2.current = true;
          var o3 = r3.changedTouches || [];
          o3.length && (b2.current = o3[0].identifier);
        }
        n2.focus(), g2(d(n2, r3, b2.current)), t2(true);
      }
    }, function(e3) {
      var r3 = e3.which || e3.keyCode;
      r3 < 37 || r3 > 40 || (e3.preventDefault(), p2({ left: r3 === 39 ? 0.05 : r3 === 37 ? -0.05 : 0, top: r3 === 40 ? 0.05 : r3 === 38 ? -0.05 : 0 }));
    }, t2];
  }, [p2, g2]), C2 = x2[0], E2 = x2[1], H2 = x2[2];
  return (0, import_react.useEffect)(function() {
    return H2;
  }, [H2]), import_react.default.createElement("div", u({}, s2, { onTouchStart: C2, onMouseDown: C2, className: "react-colorful__interactive", ref: m2, onKeyDown: E2, tabIndex: 0, role: "slider" }));
});
var g = function(e2) {
  return e2.filter(Boolean).join(" ");
};
var p = function(r2) {
  var t2 = r2.color, n2 = r2.left, o2 = r2.top, a2 = o2 === void 0 ? 0.5 : o2, l2 = g(["react-colorful__pointer", r2.className]);
  return import_react.default.createElement("div", { className: l2, style: { top: 100 * a2 + "%", left: 100 * n2 + "%" } }, import_react.default.createElement("div", { className: "react-colorful__pointer-fill", style: { backgroundColor: t2 } }));
};
var b = function(e2, r2, t2) {
  return r2 === void 0 && (r2 = 0), t2 === void 0 && (t2 = Math.pow(10, r2)), Math.round(t2 * e2) / t2;
};
var _ = { grad: 0.9, turn: 360, rad: 360 / (2 * Math.PI) };
var x = function(e2) {
  return L(C(e2));
};
var C = function(e2) {
  return e2[0] === "#" && (e2 = e2.substring(1)), e2.length < 6 ? { r: parseInt(e2[0] + e2[0], 16), g: parseInt(e2[1] + e2[1], 16), b: parseInt(e2[2] + e2[2], 16), a: e2.length === 4 ? b(parseInt(e2[3] + e2[3], 16) / 255, 2) : 1 } : { r: parseInt(e2.substring(0, 2), 16), g: parseInt(e2.substring(2, 4), 16), b: parseInt(e2.substring(4, 6), 16), a: e2.length === 8 ? b(parseInt(e2.substring(6, 8), 16) / 255, 2) : 1 };
};
var E = function(e2, r2) {
  return r2 === void 0 && (r2 = "deg"), Number(e2) * (_[r2] || 1);
};
var H = function(e2) {
  var r2 = /hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e2);
  return r2 ? N({ h: E(r2[1], r2[2]), s: Number(r2[3]), l: Number(r2[4]), a: r2[5] === void 0 ? 1 : Number(r2[5]) / (r2[6] ? 100 : 1) }) : { h: 0, s: 0, v: 0, a: 1 };
};
var N = function(e2) {
  var r2 = e2.s, t2 = e2.l;
  return { h: e2.h, s: (r2 *= (t2 < 50 ? t2 : 100 - t2) / 100) > 0 ? 2 * r2 / (t2 + r2) * 100 : 0, v: t2 + r2, a: e2.a };
};
var w = function(e2) {
  return K(I(e2));
};
var y = function(e2) {
  var r2 = e2.s, t2 = e2.v, n2 = e2.a, o2 = (200 - r2) * t2 / 100;
  return { h: b(e2.h), s: b(o2 > 0 && o2 < 200 ? r2 * t2 / 100 / (o2 <= 100 ? o2 : 200 - o2) * 100 : 0), l: b(o2 / 2), a: b(n2, 2) };
};
var q = function(e2) {
  var r2 = y(e2);
  return "hsl(" + r2.h + ", " + r2.s + "%, " + r2.l + "%)";
};
var k = function(e2) {
  var r2 = y(e2);
  return "hsla(" + r2.h + ", " + r2.s + "%, " + r2.l + "%, " + r2.a + ")";
};
var I = function(e2) {
  var r2 = e2.h, t2 = e2.s, n2 = e2.v, o2 = e2.a;
  r2 = r2 / 360 * 6, t2 /= 100, n2 /= 100;
  var a2 = Math.floor(r2), l2 = n2 * (1 - t2), u2 = n2 * (1 - (r2 - a2) * t2), c2 = n2 * (1 - (1 - r2 + a2) * t2), i2 = a2 % 6;
  return { r: b(255 * [n2, u2, l2, l2, c2, n2][i2]), g: b(255 * [c2, n2, n2, u2, l2, l2][i2]), b: b(255 * [l2, l2, c2, n2, n2, u2][i2]), a: b(o2, 2) };
};
var z = function(e2) {
  var r2 = /rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e2);
  return r2 ? L({ r: Number(r2[1]) / (r2[2] ? 100 / 255 : 1), g: Number(r2[3]) / (r2[4] ? 100 / 255 : 1), b: Number(r2[5]) / (r2[6] ? 100 / 255 : 1), a: r2[7] === void 0 ? 1 : Number(r2[7]) / (r2[8] ? 100 : 1) }) : { h: 0, s: 0, v: 0, a: 1 };
};
var D = function(e2) {
  var r2 = e2.toString(16);
  return r2.length < 2 ? "0" + r2 : r2;
};
var K = function(e2) {
  var r2 = e2.r, t2 = e2.g, n2 = e2.b, o2 = e2.a, a2 = o2 < 1 ? D(b(255 * o2)) : "";
  return "#" + D(r2) + D(t2) + D(n2) + a2;
};
var L = function(e2) {
  var r2 = e2.r, t2 = e2.g, n2 = e2.b, o2 = e2.a, a2 = Math.max(r2, t2, n2), l2 = a2 - Math.min(r2, t2, n2), u2 = l2 ? a2 === r2 ? (t2 - n2) / l2 : a2 === t2 ? 2 + (n2 - r2) / l2 : 4 + (r2 - t2) / l2 : 0;
  return { h: b(60 * (u2 < 0 ? u2 + 6 : u2)), s: b(a2 ? l2 / a2 * 100 : 0), v: b(a2 / 255 * 100), a: o2 };
};
var S = import_react.default.memo(function(r2) {
  var t2 = r2.hue, n2 = r2.onChange, o2 = g(["react-colorful__hue", r2.className]);
  return import_react.default.createElement("div", { className: o2 }, import_react.default.createElement(m, { onMove: function(e2) {
    n2({ h: 360 * e2.left });
  }, onKey: function(e2) {
    n2({ h: s(t2 + 360 * e2.left, 0, 360) });
  }, "aria-label": "Hue", "aria-valuenow": b(t2), "aria-valuemax": "360", "aria-valuemin": "0" }, import_react.default.createElement(p, { className: "react-colorful__hue-pointer", left: t2 / 360, color: q({ h: t2, s: 100, v: 100, a: 1 }) })));
});
var T = import_react.default.memo(function(r2) {
  var t2 = r2.hsva, n2 = r2.onChange, o2 = { backgroundColor: q({ h: t2.h, s: 100, v: 100, a: 1 }) };
  return import_react.default.createElement("div", { className: "react-colorful__saturation", style: o2 }, import_react.default.createElement(m, { onMove: function(e2) {
    n2({ s: 100 * e2.left, v: 100 - 100 * e2.top });
  }, onKey: function(e2) {
    n2({ s: s(t2.s + 100 * e2.left, 0, 100), v: s(t2.v - 100 * e2.top, 0, 100) });
  }, "aria-label": "Color", "aria-valuetext": "Saturation " + b(t2.s) + "%, Brightness " + b(t2.v) + "%" }, import_react.default.createElement(p, { className: "react-colorful__saturation-pointer", top: 1 - t2.v / 100, left: t2.s / 100, color: q(t2) })));
});
var F = function(e2, r2) {
  if (e2 === r2) return true;
  for (var t2 in e2) if (e2[t2] !== r2[t2]) return false;
  return true;
};
var P = function(e2, r2) {
  return e2.replace(/\s/g, "") === r2.replace(/\s/g, "");
};
var X = function(e2, r2) {
  return e2.toLowerCase() === r2.toLowerCase() || F(C(e2), C(r2));
};
function Y(e2, t2, l2) {
  var u2 = i(l2), c2 = (0, import_react.useState)(function() {
    return e2.toHsva(t2);
  }), s2 = c2[0], f2 = c2[1], v2 = (0, import_react.useRef)({ color: t2, hsva: s2 });
  (0, import_react.useEffect)(function() {
    if (!e2.equal(t2, v2.current.color)) {
      var r2 = e2.toHsva(t2);
      v2.current = { hsva: r2, color: t2 }, f2(r2);
    }
  }, [t2, e2]), (0, import_react.useEffect)(function() {
    var r2;
    F(s2, v2.current.hsva) || e2.equal(r2 = e2.fromHsva(s2), v2.current.color) || (v2.current = { hsva: s2, color: r2 }, u2(r2));
  }, [s2, e2, u2]);
  var d2 = (0, import_react.useCallback)(function(e3) {
    f2(function(r2) {
      return Object.assign({}, r2, e3);
    });
  }, []);
  return [s2, d2];
}
var V = typeof window < "u" ? import_react.useLayoutEffect : import_react.useEffect;
var $ = function() {
  return typeof __webpack_nonce__ < "u" ? __webpack_nonce__ : void 0;
};
var J = /* @__PURE__ */ new Map();
var Q = function(e2) {
  V(function() {
    var r2 = e2.current ? e2.current.ownerDocument : document;
    if (r2 !== void 0 && !J.has(r2)) {
      var t2 = r2.createElement("style");
      t2.innerHTML = `.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}`, J.set(r2, t2);
      var n2 = $();
      n2 && t2.setAttribute("nonce", n2), r2.head.appendChild(t2);
    }
  }, []);
};
var U = function(t2) {
  var n2 = t2.className, o2 = t2.colorModel, a2 = t2.color, l2 = a2 === void 0 ? o2.defaultColor : a2, i2 = t2.onChange, s2 = c(t2, ["className", "colorModel", "color", "onChange"]), f2 = (0, import_react.useRef)(null);
  Q(f2);
  var v2 = Y(o2, l2, i2), d2 = v2[0], h2 = v2[1], m2 = g(["react-colorful", n2]);
  return import_react.default.createElement("div", u({}, s2, { ref: f2, className: m2 }), import_react.default.createElement(T, { hsva: d2, onChange: h2 }), import_react.default.createElement(S, { hue: d2.h, onChange: h2, className: "react-colorful__last-control" }));
};
var W = { defaultColor: "000", toHsva: x, fromHsva: function(e2) {
  return w({ h: e2.h, s: e2.s, v: e2.v, a: 1 });
}, equal: X };
var Z = function(r2) {
  return import_react.default.createElement(U, u({}, r2, { colorModel: W }));
};
var ee = function(r2) {
  var t2 = r2.className, n2 = r2.hsva, o2 = r2.onChange, a2 = { backgroundImage: "linear-gradient(90deg, " + k(Object.assign({}, n2, { a: 0 })) + ", " + k(Object.assign({}, n2, { a: 1 })) + ")" }, l2 = g(["react-colorful__alpha", t2]), u2 = b(100 * n2.a);
  return import_react.default.createElement("div", { className: l2 }, import_react.default.createElement("div", { className: "react-colorful__alpha-gradient", style: a2 }), import_react.default.createElement(m, { onMove: function(e2) {
    o2({ a: e2.left });
  }, onKey: function(e2) {
    o2({ a: s(n2.a + e2.left) });
  }, "aria-label": "Alpha", "aria-valuetext": u2 + "%", "aria-valuenow": u2, "aria-valuemin": "0", "aria-valuemax": "100" }, import_react.default.createElement(p, { className: "react-colorful__alpha-pointer", left: n2.a, color: k(n2) })));
};
var re = function(t2) {
  var n2 = t2.className, o2 = t2.colorModel, a2 = t2.color, l2 = a2 === void 0 ? o2.defaultColor : a2, i2 = t2.onChange, s2 = c(t2, ["className", "colorModel", "color", "onChange"]), f2 = (0, import_react.useRef)(null);
  Q(f2);
  var v2 = Y(o2, l2, i2), d2 = v2[0], h2 = v2[1], m2 = g(["react-colorful", n2]);
  return import_react.default.createElement("div", u({}, s2, { ref: f2, className: m2 }), import_react.default.createElement(T, { hsva: d2, onChange: h2 }), import_react.default.createElement(S, { hue: d2.h, onChange: h2 }), import_react.default.createElement(ee, { hsva: d2, onChange: h2, className: "react-colorful__last-control" }));
};
var le = { defaultColor: "hsla(0, 0%, 0%, 1)", toHsva: H, fromHsva: k, equal: P };
var ue = function(r2) {
  return import_react.default.createElement(re, u({}, r2, { colorModel: le }));
};
var Ee = { defaultColor: "rgba(0, 0, 0, 1)", toHsva: z, fromHsva: function(e2) {
  var r2 = I(e2);
  return "rgba(" + r2.r + ", " + r2.g + ", " + r2.b + ", " + r2.a + ")";
}, equal: P };
var He = function(r2) {
  return import_react.default.createElement(re, u({}, r2, { colorModel: Ee }));
};
var Wrapper = xr.div({ position: "relative", maxWidth: 250, '&[aria-readonly="true"]': { opacity: 0.5 } });
var PickerTooltip = xr(B3)({ position: "absolute", zIndex: 1, top: 4, left: 4, "[aria-readonly=true] &": { cursor: "not-allowed" } });
var TooltipContent = xr.div({ width: 200, margin: 5, ".react-colorful__saturation": { borderRadius: "4px 4px 0 0" }, ".react-colorful__hue": { boxShadow: "inset 0 0 0 1px rgb(0 0 0 / 5%)" }, ".react-colorful__last-control": { borderRadius: "0 0 4px 4px" } });
var Note = xr(X3)(({ theme }) => ({ fontFamily: theme.typography.fonts.base }));
var Swatches = xr.div({ display: "grid", gridTemplateColumns: "repeat(9, 16px)", gap: 6, padding: 3, marginTop: 5, width: 200 });
var SwatchColor = xr.div(({ theme, active }) => ({ width: 16, height: 16, boxShadow: active ? `${theme.appBorderColor} 0 0 0 1px inset, ${theme.textMutedColor}50 0 0 0 4px` : `${theme.appBorderColor} 0 0 0 1px inset`, borderRadius: theme.appBorderRadius }));
var swatchBackground = `url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')`;
var Swatch = ({ value, style, ...props }) => {
  let backgroundImage = `linear-gradient(${value}, ${value}), ${swatchBackground}, linear-gradient(#fff, #fff)`;
  return import_react.default.createElement(SwatchColor, { ...props, style: { ...style, backgroundImage } });
};
var Input = xr(D7.Input)(({ theme, readOnly }) => ({ width: "100%", paddingLeft: 30, paddingRight: 30, boxSizing: "border-box", fontFamily: theme.typography.fonts.base }));
var ToggleIcon = xr(MarkupIcon)(({ theme }) => ({ position: "absolute", zIndex: 1, top: 6, right: 7, width: 20, height: 20, padding: 4, boxSizing: "border-box", cursor: "pointer", color: theme.input.color }));
var ColorSpace = ((ColorSpace2) => (ColorSpace2.RGB = "rgb", ColorSpace2.HSL = "hsl", ColorSpace2.HEX = "hex", ColorSpace2))(ColorSpace || {});
var COLOR_SPACES = Object.values(ColorSpace);
var COLOR_REGEXP = /\(([0-9]+),\s*([0-9]+)%?,\s*([0-9]+)%?,?\s*([0-9.]+)?\)/;
var RGB_REGEXP = /^\s*rgba?\(([0-9]+),\s*([0-9]+),\s*([0-9]+),?\s*([0-9.]+)?\)\s*$/i;
var HSL_REGEXP = /^\s*hsla?\(([0-9]+),\s*([0-9]+)%,\s*([0-9]+)%,?\s*([0-9.]+)?\)\s*$/i;
var HEX_REGEXP = /^\s*#?([0-9a-f]{3}|[0-9a-f]{6})\s*$/i;
var SHORTHEX_REGEXP = /^\s*#?([0-9a-f]{3})\s*$/i;
var ColorPicker = { hex: Z, rgb: He, hsl: ue };
var fallbackColor = { hex: "transparent", rgb: "rgba(0, 0, 0, 0)", hsl: "hsla(0, 0%, 0%, 0)" };
var stringToArgs = (value) => {
  let match = value == null ? void 0 : value.match(COLOR_REGEXP);
  if (!match) return [0, 0, 0, 1];
  let [, x2, y2, z2, a2 = 1] = match;
  return [x2, y2, z2, a2].map(Number);
};
var parseRgb = (value) => {
  let [r2, g2, b2, a2] = stringToArgs(value), [h2, s2, l2] = import_color_convert.default.rgb.hsl([r2, g2, b2]) || [0, 0, 0];
  return { valid: true, value, keyword: import_color_convert.default.rgb.keyword([r2, g2, b2]), colorSpace: "rgb", rgb: value, hsl: `hsla(${h2}, ${s2}%, ${l2}%, ${a2})`, hex: `#${import_color_convert.default.rgb.hex([r2, g2, b2]).toLowerCase()}` };
};
var parseHsl = (value) => {
  let [h2, s2, l2, a2] = stringToArgs(value), [r2, g2, b2] = import_color_convert.default.hsl.rgb([h2, s2, l2]) || [0, 0, 0];
  return { valid: true, value, keyword: import_color_convert.default.hsl.keyword([h2, s2, l2]), colorSpace: "hsl", rgb: `rgba(${r2}, ${g2}, ${b2}, ${a2})`, hsl: value, hex: `#${import_color_convert.default.hsl.hex([h2, s2, l2]).toLowerCase()}` };
};
var parseHexOrKeyword = (value) => {
  let plain = value.replace("#", ""), rgb = import_color_convert.default.keyword.rgb(plain) || import_color_convert.default.hex.rgb(plain), hsl = import_color_convert.default.rgb.hsl(rgb), mapped = value;
  /[^#a-f0-9]/i.test(value) ? mapped = plain : HEX_REGEXP.test(value) && (mapped = `#${plain}`);
  let valid = true;
  if (mapped.startsWith("#")) valid = HEX_REGEXP.test(mapped);
  else try {
    import_color_convert.default.keyword.hex(mapped);
  } catch {
    valid = false;
  }
  return { valid, value: mapped, keyword: import_color_convert.default.rgb.keyword(rgb), colorSpace: "hex", rgb: `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, 1)`, hsl: `hsla(${hsl[0]}, ${hsl[1]}%, ${hsl[2]}%, 1)`, hex: mapped };
};
var parseValue = (value) => {
  if (value) return RGB_REGEXP.test(value) ? parseRgb(value) : HSL_REGEXP.test(value) ? parseHsl(value) : parseHexOrKeyword(value);
};
var getRealValue = (value, color, colorSpace) => {
  if (!value || !(color == null ? void 0 : color.valid)) return fallbackColor[colorSpace];
  if (colorSpace !== "hex") return (color == null ? void 0 : color[colorSpace]) || fallbackColor[colorSpace];
  if (!color.hex.startsWith("#")) try {
    return `#${import_color_convert.default.keyword.hex(color.hex)}`;
  } catch {
    return fallbackColor.hex;
  }
  let short = color.hex.match(SHORTHEX_REGEXP);
  if (!short) return HEX_REGEXP.test(color.hex) ? color.hex : fallbackColor.hex;
  let [r2, g2, b2] = short[1].split("");
  return `#${r2}${r2}${g2}${g2}${b2}${b2}`;
};
var useColorInput = (initialValue, onChange) => {
  let [value, setValue] = (0, import_react.useState)(initialValue || ""), [color, setColor] = (0, import_react.useState)(() => parseValue(value)), [colorSpace, setColorSpace] = (0, import_react.useState)((color == null ? void 0 : color.colorSpace) || "hex");
  (0, import_react.useEffect)(() => {
    let nextValue = initialValue || "", nextColor = parseValue(nextValue);
    setValue(nextValue), setColor(nextColor), setColorSpace((nextColor == null ? void 0 : nextColor.colorSpace) || "hex");
  }, [initialValue]);
  let realValue = (0, import_react.useMemo)(() => getRealValue(value, color, colorSpace).toLowerCase(), [value, color, colorSpace]), updateValue = (0, import_react.useCallback)((update) => {
    let parsed = parseValue(update), v2 = (parsed == null ? void 0 : parsed.value) || update || "";
    setValue(v2), v2 === "" && (setColor(void 0), onChange(void 0)), parsed && (setColor(parsed), setColorSpace(parsed.colorSpace), onChange(parsed.value));
  }, [onChange]), cycleColorSpace = (0, import_react.useCallback)(() => {
    let nextIndex = (COLOR_SPACES.indexOf(colorSpace) + 1) % COLOR_SPACES.length, nextSpace = COLOR_SPACES[nextIndex];
    setColorSpace(nextSpace);
    let updatedValue = (color == null ? void 0 : color[nextSpace]) || "";
    setValue(updatedValue), onChange(updatedValue);
  }, [color, colorSpace, onChange]);
  return { value, realValue, updateValue, color, colorSpace, cycleColorSpace };
};
var id = (value) => value.replace(/\s*/, "").toLowerCase();
var usePresets = (presetColors, currentColor, colorSpace) => {
  let [selectedColors, setSelectedColors] = (0, import_react.useState)((currentColor == null ? void 0 : currentColor.valid) ? [currentColor] : []);
  (0, import_react.useEffect)(() => {
    currentColor === void 0 && setSelectedColors([]);
  }, [currentColor]);
  let presets = (0, import_react.useMemo)(() => (presetColors || []).map((preset) => typeof preset == "string" ? parseValue(preset) : preset.title ? { ...parseValue(preset.color), keyword: preset.title } : parseValue(preset.color)).concat(selectedColors).filter(Boolean).slice(-27), [presetColors, selectedColors]), addPreset = (0, import_react.useCallback)((color) => {
    (color == null ? void 0 : color.valid) && (presets.some((preset) => preset && preset[colorSpace] && id(preset[colorSpace] || "") === id(color[colorSpace] || "")) || setSelectedColors((arr) => arr.concat(color)));
  }, [colorSpace, presets]);
  return { presets, addPreset };
};
var ColorControl = ({ name, value: initialValue, onChange, onFocus, onBlur, presetColors, startOpen = false, argType }) => {
  var _a;
  let debouncedOnChange = (0, import_react.useCallback)(debounce2(onChange, 200), [onChange]), { value, realValue, updateValue, color, colorSpace, cycleColorSpace } = useColorInput(initialValue, debouncedOnChange), { presets, addPreset } = usePresets(presetColors ?? [], color, colorSpace), Picker = ColorPicker[colorSpace], readonly = !!((_a = argType == null ? void 0 : argType.table) == null ? void 0 : _a.readonly);
  return import_react.default.createElement(Wrapper, { "aria-readonly": readonly }, import_react.default.createElement(PickerTooltip, { startOpen, trigger: readonly ? null : void 0, closeOnOutsideClick: true, onVisibleChange: () => color && addPreset(color), tooltip: import_react.default.createElement(TooltipContent, null, import_react.default.createElement(Picker, { color: realValue === "transparent" ? "#000000" : realValue, onChange: updateValue, onFocus, onBlur }), presets.length > 0 && import_react.default.createElement(Swatches, null, presets.map((preset, index) => import_react.default.createElement(B3, { key: `${(preset == null ? void 0 : preset.value) || index}-${index}`, hasChrome: false, tooltip: import_react.default.createElement(Note, { note: (preset == null ? void 0 : preset.keyword) || (preset == null ? void 0 : preset.value) || "" }) }, import_react.default.createElement(Swatch, { value: (preset == null ? void 0 : preset[colorSpace]) || "", active: !!(color && preset && preset[colorSpace] && id(preset[colorSpace] || "") === id(color[colorSpace])), onClick: () => preset && updateValue(preset.value || "") }))))) }, import_react.default.createElement(Swatch, { value: realValue, style: { margin: 4 } })), import_react.default.createElement(Input, { id: getControlId(name), value, onChange: (e2) => updateValue(e2.target.value), onFocus: (e2) => e2.target.select(), readOnly: readonly, placeholder: "Choose color..." }), value ? import_react.default.createElement(ToggleIcon, { onClick: cycleColorSpace }) : null);
};
var Color_default = ColorControl;
export {
  ColorControl,
  Color_default as default
};
//# sourceMappingURL=Color-AVL7NMMY-SP63Y3CP.js.map

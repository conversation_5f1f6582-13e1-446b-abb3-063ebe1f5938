import {
  require_baseFor
} from "./chunk-4O4FP57Y.js";
import {
  require_keys
} from "./chunk-NKCLXYHL.js";
import {
  __commonJS
} from "./chunk-KEXKKQVW.js";

// node_modules/lodash/_baseForOwn.js
var require_baseForOwn = __commonJS({
  "node_modules/lodash/_baseForOwn.js"(exports, module) {
    var baseFor = require_baseFor();
    var keys = require_keys();
    function baseForOwn(object, iteratee) {
      return object && baseFor(object, iteratee, keys);
    }
    module.exports = baseForOwn;
  }
});

export {
  require_baseForOwn
};
//# sourceMappingURL=chunk-X7NWIE4X.js.map

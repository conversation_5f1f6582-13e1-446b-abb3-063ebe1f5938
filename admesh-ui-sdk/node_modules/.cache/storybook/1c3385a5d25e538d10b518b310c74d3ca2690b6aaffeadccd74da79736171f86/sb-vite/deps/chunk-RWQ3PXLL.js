import {
  require_baseIsEqual
} from "./chunk-INDQLR2O.js";
import {
  __commonJS
} from "./chunk-KEXKKQVW.js";

// node_modules/lodash/isEqual.js
var require_isEqual = __commonJS({
  "node_modules/lodash/isEqual.js"(exports, module) {
    var baseIsEqual = require_baseIsEqual();
    function isEqual(value, other) {
      return baseIsEqual(value, other);
    }
    module.exports = isEqual;
  }
});

export {
  require_isEqual
};
//# sourceMappingURL=chunk-RWQ3PXLL.js.map

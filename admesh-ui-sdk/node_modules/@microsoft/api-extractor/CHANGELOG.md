# Change Log - @microsoft/api-extractor

This log was last generated on <PERSON><PERSON>, 13 May 2025 02:09:20 GMT and should not be manually modified.

## 7.52.8
<PERSON><PERSON>, 13 May 2025 02:09:20 GMT

### Patches

- Fixes API extractor error handling when changed APIs are encountered and the "--local" flag is not specified

## 7.52.7
Thu, 01 May 2025 15:11:33 GMT

### Patches

- Fix an issue where default exports were sometimes trimmed incorrectly in .api.md files when using `reportVariants` (GitHub #4775)

## 7.52.6
Thu, 01 May 2025 00:11:12 GMT

_Version update only_

## 7.52.5
Mon, 21 Apr 2025 22:24:25 GMT

_Version update only_

## 7.52.4
Thu, 17 Apr 2025 00:11:21 GMT

### Patches

- Update documentation for `extends`

## 7.52.3
Fri, 04 Apr 2025 18:34:35 GMT

### Patches

- Add support for customizing which TSDoc tags appear in API reports

## 7.52.2
Tu<PERSON>, 25 Mar 2025 15:11:15 GMT

_Version update only_

## 7.52.1
<PERSON><PERSON>, 11 Mar 2025 02:12:34 GMT

_Version update only_

## 7.52.0
<PERSON><PERSON>, 11 Mar 2025 00:11:25 GMT

### Minor changes

- Upgrade the bundled compiler engine to TypeScript 5.8.2

## 7.51.1
Sat, 01 Mar 2025 05:00:09 GMT

### Patches

- Include triple-slash references marked with `preserve="true"` from files that only contain re-exports. There was a behavior change in TypeScript 5.5, where only triple-slash references that are explicitly marked with `preserve="true"` are emitted into declaration files. This change adds support for placing these references in files that only contain re-exports, like the API entrypoint file.

## 7.51.0
Thu, 27 Feb 2025 01:10:39 GMT

### Minor changes

- Add a `docModel.releaseTagsToTrim` property to `api-extractor.json` to specify which release tags should be trimmed when the doc model is produced.

## 7.50.1
Sat, 22 Feb 2025 01:11:11 GMT

### Patches

- Upgrade the bundled compiler engine to TypeScript 5.7.3

## 7.50.0
Wed, 12 Feb 2025 01:10:52 GMT

### Minor changes

- Update merge behavior for derived configurations to allow overriding array properties

## 7.49.2
Thu, 30 Jan 2025 01:11:42 GMT

_Version update only_

## 7.49.1
Thu, 09 Jan 2025 01:10:10 GMT

_Version update only_

## 7.49.0
Tue, 07 Jan 2025 22:17:32 GMT

### Minor changes

- Upgrade the bundled compiler engine to TypeScript 5.7.2

## 7.48.1
Sat, 14 Dec 2024 01:11:07 GMT

_Version update only_

## 7.48.0
Sat, 23 Nov 2024 01:18:55 GMT

### Minor changes

- Update TSDoc dependencies.

## 7.47.12
Fri, 22 Nov 2024 01:10:43 GMT

_Version update only_

## 7.47.11
Thu, 17 Oct 2024 08:35:06 GMT

_Version update only_

## 7.47.10
Tue, 15 Oct 2024 00:12:31 GMT

### Patches

- Fix a compatibility issue with usage of `getModeForUsageLocation` in TypeScript 5.6

## 7.47.9
Fri, 13 Sep 2024 00:11:42 GMT

_Version update only_

## 7.47.8
Tue, 10 Sep 2024 20:08:11 GMT

_Version update only_

## 7.47.7
Wed, 21 Aug 2024 05:43:04 GMT

_Version update only_

## 7.47.6
Mon, 12 Aug 2024 22:16:04 GMT

_Version update only_

## 7.47.5
Fri, 02 Aug 2024 17:26:42 GMT

_Version update only_

## 7.47.4
Sat, 27 Jul 2024 00:10:27 GMT

### Patches

- Include CHANGELOG.md in published releases again

## 7.47.3
Wed, 24 Jul 2024 00:12:14 GMT

### Patches

- Fix an edge case when discarding the file extension from the "reportFileName" setting and improve its documentation

## 7.47.2
Wed, 17 Jul 2024 06:55:09 GMT

_Version update only_

## 7.47.1
Tue, 16 Jul 2024 00:36:22 GMT

_Version update only_

## 7.47.0
Mon, 03 Jun 2024 23:43:15 GMT

### Minor changes

- Add support for re-exporting modules using syntax such as `export * as ns from './file'` (GitHub #2780)

## 7.46.2
Thu, 30 May 2024 00:13:05 GMT

### Patches

- Include missing `type` modifiers on type-only exports.

## 7.46.1
Wed, 29 May 2024 02:03:50 GMT

_Version update only_

## 7.46.0
Wed, 29 May 2024 00:10:52 GMT

### Minor changes

- Bump TSDoc dependencies.

## 7.45.1
Tue, 28 May 2024 15:10:09 GMT

_Version update only_

## 7.45.0
Tue, 28 May 2024 00:09:47 GMT

### Minor changes

- Improve support for resolving the `tsdoc-metadata.json` to include the folder referenced by a `types` field in an `"exports"` field and an `"typesVersions"` field in addition to `"types"`, `"typings"`, and `"tsdocMetadata"` fields.

## 7.44.1
Sat, 25 May 2024 04:54:07 GMT

_Version update only_

## 7.44.0
Fri, 24 May 2024 00:15:08 GMT

### Minor changes

- Add support for "variants" of API reports which include or exclude items by release tag

## 7.43.8
Thu, 23 May 2024 02:26:56 GMT

_Version update only_

## 7.43.7
Thu, 16 May 2024 15:10:22 GMT

_Version update only_

## 7.43.6
Wed, 15 May 2024 23:42:58 GMT

_Version update only_

## 7.43.5
Wed, 15 May 2024 06:04:17 GMT

_Version update only_

## 7.43.4
Fri, 10 May 2024 05:33:33 GMT

_Version update only_

## 7.43.3
Wed, 08 May 2024 22:23:50 GMT

_Version update only_

## 7.43.2
Mon, 06 May 2024 15:11:04 GMT

_Version update only_

## 7.43.1
Wed, 10 Apr 2024 15:10:09 GMT

_Version update only_

## 7.43.0
Tue, 19 Mar 2024 15:10:18 GMT

### Minor changes

- Upgrade the bundled compiler engine to TypeScript 5.4.2

## 7.42.3
Sun, 03 Mar 2024 20:58:12 GMT

_Version update only_

## 7.42.2
Sat, 02 Mar 2024 02:22:23 GMT

_Version update only_

## 7.42.1
Fri, 01 Mar 2024 01:10:08 GMT

_Version update only_

## 7.42.0
Thu, 29 Feb 2024 07:11:45 GMT

### Minor changes

- Add glob support in `bundledPackages`

### Patches

- Don't mark items documented with {@inheritDoc} references to package-external items as "undocumented"

## 7.41.1
Wed, 28 Feb 2024 16:09:27 GMT

_Version update only_

## 7.41.0
Sat, 24 Feb 2024 23:02:51 GMT

### Minor changes

- Replace const enums with conventional enums to allow for compatibility with JavaScript consumers.

## 7.40.6
Wed, 21 Feb 2024 21:45:28 GMT

### Patches

- Replace the dependency on the `colors` package with `Colorize` from `@rushstack/terminal`.

## 7.40.5
Wed, 21 Feb 2024 08:55:47 GMT

### Patches

- Fix an issue where imports were trimmed from external packages based when generating .d.ts rollups

## 7.40.4
Tue, 20 Feb 2024 21:45:10 GMT

_Version update only_

## 7.40.3
Mon, 19 Feb 2024 21:54:27 GMT

_Version update only_

## 7.40.2
Sat, 17 Feb 2024 06:24:34 GMT

### Patches

- Fix broken link to API documentation

## 7.40.1
Thu, 08 Feb 2024 01:09:21 GMT

_Version update only_

## 7.40.0
Wed, 07 Feb 2024 01:11:18 GMT

### Minor changes

- Classify arrow functions as `function` kind in the doc model export.

## 7.39.5
Mon, 05 Feb 2024 23:46:52 GMT

_Version update only_

## 7.39.4
Thu, 25 Jan 2024 01:09:30 GMT

_Version update only_

## 7.39.3
Tue, 23 Jan 2024 20:12:57 GMT

_Version update only_

## 7.39.2
Tue, 23 Jan 2024 16:15:05 GMT

_Version update only_

## 7.39.1
Wed, 03 Jan 2024 00:31:18 GMT

_Version update only_

## 7.39.0
Wed, 20 Dec 2023 01:09:45 GMT

### Minor changes

- Update API Extractor to support TypeScript 5.3.3

## 7.38.5
Thu, 07 Dec 2023 03:44:13 GMT

_Version update only_

## 7.38.4
Tue, 05 Dec 2023 01:10:16 GMT

### Patches

- Don't export trimmed namespace members during rollup (#2791)

## 7.38.3
Fri, 10 Nov 2023 18:02:04 GMT

### Patches

- Fix an issue where "ae-undocumented" was incorrectly reported for private members

## 7.38.2
Wed, 01 Nov 2023 23:11:35 GMT

### Patches

- Fix line endings in published package.

## 7.38.1
Mon, 30 Oct 2023 23:36:38 GMT

_Version update only_

## 7.38.0
Sun, 01 Oct 2023 02:56:29 GMT

### Minor changes

- Add a new message "ae-undocumented" to support logging of undocumented API items

## 7.37.3
Sat, 30 Sep 2023 00:20:51 GMT

### Patches

- Don't strip out @alpha items when generating API reports.

## 7.37.2
Thu, 28 Sep 2023 20:53:16 GMT

_Version update only_

## 7.37.1
Tue, 26 Sep 2023 09:30:33 GMT

### Patches

- Update type-only imports to include the type modifier.

## 7.37.0
Fri, 15 Sep 2023 00:36:58 GMT

### Minor changes

- Update @types/node from 14 to 18

## 7.36.4
Tue, 08 Aug 2023 07:10:39 GMT

_Version update only_

## 7.36.3
Wed, 19 Jul 2023 00:20:31 GMT

### Patches

- Updated semver dependency

## 7.36.2
Wed, 12 Jul 2023 15:20:39 GMT

### Patches

- Add api-extractor support for .d.mts and .d.cts files

## 7.36.1
Thu, 06 Jul 2023 00:16:19 GMT

_Version update only_

## 7.36.0
Mon, 19 Jun 2023 22:40:21 GMT

### Minor changes

- Use the `IRigConfig` interface in the `IExtractorConfigLoadForFolderOptions` object insteacd of the `RigConfig` class.

## 7.35.4
Thu, 15 Jun 2023 00:21:01 GMT

_Version update only_

## 7.35.3
Tue, 13 Jun 2023 01:49:01 GMT

_Version update only_

## 7.35.2
Wed, 07 Jun 2023 22:45:16 GMT

_Version update only_

## 7.35.1
Mon, 29 May 2023 15:21:15 GMT

_Version update only_

## 7.35.0
Mon, 22 May 2023 06:34:32 GMT

### Minor changes

- Upgrade the TypeScript dependency to ~5.0.4

## 7.34.9
Fri, 12 May 2023 00:23:05 GMT

_Version update only_

## 7.34.8
Thu, 04 May 2023 00:20:28 GMT

_Version update only_

## 7.34.7
Mon, 01 May 2023 15:23:20 GMT

_Version update only_

## 7.34.6
Sat, 29 Apr 2023 00:23:03 GMT

_Version update only_

## 7.34.5
Thu, 27 Apr 2023 17:18:42 GMT

_Version update only_

## 7.34.4
Fri, 10 Feb 2023 01:18:50 GMT

_Version update only_

## 7.34.3
Sun, 05 Feb 2023 03:02:02 GMT

_Version update only_

## 7.34.2
Wed, 01 Feb 2023 02:16:34 GMT

_Version update only_

## 7.34.1
Mon, 30 Jan 2023 16:22:30 GMT

_Version update only_

## 7.34.0
Wed, 25 Jan 2023 07:26:55 GMT

### Minor changes

- Add new .api.json field `isAbstract` to track `abstract` modifier in ApiClass, ApiMethod, and ApiProperty via ApiAbstractMixin (GitHub #3661)

## 7.33.8
Wed, 18 Jan 2023 22:44:12 GMT

### Patches

- Use ts.getCheckFlags to fix TS 5.0

## 7.33.7
Fri, 09 Dec 2022 16:18:28 GMT

_Version update only_

## 7.33.6
Tue, 08 Nov 2022 01:20:55 GMT

_Version update only_

## 7.33.5
Wed, 26 Oct 2022 00:16:16 GMT

### Patches

- Update the @microsoft/tsdoc dependency version to 0.14.2.

## 7.33.4
Mon, 17 Oct 2022 22:14:21 GMT

_Version update only_

## 7.33.3
Mon, 17 Oct 2022 15:16:00 GMT

### Patches

- Fix a regression where the "fileUrlPath" property would contain a malformed path when API Extractor is run on Windows.

## 7.33.2
Fri, 14 Oct 2022 15:26:31 GMT

### Patches

- Fix references from computed properties #3629

## 7.33.1
Thu, 13 Oct 2022 00:20:15 GMT

_Version update only_

## 7.33.0
Tue, 11 Oct 2022 23:49:12 GMT

### Minor changes

- Extract the original source file path for relevant API items and add a new projectFolderUrl setting to the api-extractor.json config that allows one to specify what URL their project folder can be found at.

## 7.32.1
Mon, 10 Oct 2022 15:23:44 GMT

_Version update only_

## 7.32.0
Thu, 29 Sep 2022 07:13:06 GMT

### Minor changes

- Update parser to TypeScript 4.8.

## 7.31.2
Wed, 21 Sep 2022 20:21:10 GMT

_Version update only_

## 7.31.1
Thu, 15 Sep 2022 00:18:51 GMT

_Version update only_

## 7.31.0
Tue, 13 Sep 2022 00:16:55 GMT

### Minor changes

- Fix an issue where aliased classes sometimes had incorrect canonical references in *.api.json (GitHub  #3593)

## 7.30.1
Mon, 12 Sep 2022 22:27:48 GMT

### Patches

- Fix a recent regression where items exported from both the entry point and from an exported namespace appeared only once in the API doc model (GitHub #3619)

## 7.30.0
Fri, 02 Sep 2022 17:48:42 GMT

### Minor changes

- Add new "apiReport.includeForgottenExports" and "docModel.includeForgottenExports" properties to control whether forgotten exports are included in the API report and doc model files.
- Fix incorrect declaration references for symbols not exported from the package's entry point.

## 7.29.5
Wed, 24 Aug 2022 03:01:22 GMT

_Version update only_

## 7.29.4
Wed, 24 Aug 2022 00:14:38 GMT

### Patches

- Remove use of LegacyAdapters.sortStable

## 7.29.3
Fri, 19 Aug 2022 00:17:19 GMT

_Version update only_

## 7.29.2
Wed, 10 Aug 2022 09:52:12 GMT

### Patches

- Fix incorrect declaration references for local symbols within namespaces

## 7.29.1
Wed, 10 Aug 2022 08:12:16 GMT

### Patches

- Fix a regression where .api.json excerpts were sometimes missing tokens (GitHub #3561), and generally improve the quality of excerpt generation

## 7.29.0
Wed, 03 Aug 2022 18:40:35 GMT

### Minor changes

- Upgrade TypeScript dependency to 4.7

## 7.28.7
Mon, 01 Aug 2022 02:45:32 GMT

_Version update only_

## 7.28.6
Thu, 21 Jul 2022 23:30:27 GMT

_Version update only_

## 7.28.5
Thu, 21 Jul 2022 00:16:14 GMT

_Version update only_

## 7.28.4
Fri, 08 Jul 2022 15:17:46 GMT

### Patches

- Update api-extractor-template.json to "testMode" and "enumMemberOrder" comment sections.

## 7.28.3
Mon, 04 Jul 2022 15:15:13 GMT

### Patches

- Make enumMemberOrder configuration field optional

## 7.28.2
Thu, 30 Jun 2022 04:48:53 GMT

### Patches

- Improve logic that determines whether an API item is readonly

## 7.28.1
Tue, 28 Jun 2022 22:47:13 GMT

_Version update only_

## 7.28.0
Tue, 28 Jun 2022 00:23:32 GMT

### Minor changes

- Add support for the "ignoreMissingEntryPoint" ExtractorConfig option to allow for loading an ExtractorConfig before the target project is built.

## 7.27.1
Mon, 27 Jun 2022 18:43:09 GMT

_Version update only_

## 7.27.0
Sat, 25 Jun 2022 21:00:40 GMT

### Minor changes

- API Extractor now populates an initializerTokenRange field for ApiProperty and ApiVariable items.

## 7.26.1
Sat, 25 Jun 2022 01:54:29 GMT

_Version update only_

## 7.26.0
Fri, 24 Jun 2022 07:16:47 GMT

### Minor changes

- Include new configuration option for preserving enum member order

## 7.25.3
Thu, 23 Jun 2022 22:14:24 GMT

_Version update only_

## 7.25.2
Fri, 17 Jun 2022 09:17:54 GMT

_Version update only_

## 7.25.1
Fri, 17 Jun 2022 00:16:18 GMT

_Version update only_

## 7.25.0
Tue, 07 Jun 2022 09:37:04 GMT

### Minor changes

- Add an "isReadonly" field to the doc model to indicate whether a property or variable is readonly
- Add an "isProtected" field to the doc model to indicate protected class members

## 7.24.2
Wed, 25 May 2022 22:25:07 GMT

### Patches

- Fix an issue where API Extractor would fail to run on a project where `"moduleResolution"` is set to `"Node16"` in `tsconfig.json`

## 7.24.1
Thu, 19 May 2022 15:13:20 GMT

### Patches

- Fix a recent regression that produced an error "Cannot read properties of undefined" (GitHub #3423)

## 7.24.0
Sat, 14 May 2022 03:01:27 GMT

### Minor changes

- Throw an error early if API Extractor will attempt to process non-.d.ts files
- Generate API doc model nodes for setters without getters

### Patches

- Address edge case in excerptBuilder token range logic

## 7.23.2
Tue, 10 May 2022 01:20:43 GMT

_Version update only_

## 7.23.1
Wed, 04 May 2022 23:29:13 GMT

### Patches

- Update the global variable analyzer to add support for changes to the TypeScript internals coming in v4.7

## 7.23.0
Sat, 23 Apr 2022 02:13:06 GMT

### Minor changes

- Update to TypeScript 4.6

## 7.22.2
Fri, 15 Apr 2022 00:12:36 GMT

_Version update only_

## 7.22.1
Wed, 13 Apr 2022 15:12:40 GMT

_Version update only_

## 7.22.0
Tue, 12 Apr 2022 23:29:34 GMT

### Minor changes

- Add an alphaTrimmedFilePath option that adds support for generating a DTS rollup that inclues @alpha, @beta, and @public members.

## 7.21.3
Tue, 12 Apr 2022 02:58:32 GMT

### Patches

- Update TSDoc dependencies.

## 7.21.2
Sat, 09 Apr 2022 19:07:47 GMT

### Patches

- Fix ambient modules bug caused by #3321.

## 7.21.1
Sat, 09 Apr 2022 02:24:26 GMT

### Patches

- Rename the "master" branch to "main".

## 7.21.0
Fri, 08 Apr 2022 20:05:59 GMT

### Minor changes

- Add support for projects that use tsconfig.json "baseUrl" and "paths" settings to remap imports of local files (GitHub #3291)

## 7.20.1
Wed, 06 Apr 2022 22:35:23 GMT

### Patches

- Fix an issue where .api.json excerpt text included extra whitespace (GitHub #3316)

## 7.20.0
Thu, 31 Mar 2022 02:06:05 GMT

### Minor changes

- Updated api-extractor to extract whether a parameter is optional.

## 7.19.5
Tue, 15 Mar 2022 19:15:53 GMT

_Version update only_

## 7.19.4
Wed, 05 Jan 2022 16:07:47 GMT

_Version update only_

## 7.19.3
Mon, 27 Dec 2021 16:10:40 GMT

_Version update only_

## 7.19.2
Thu, 09 Dec 2021 20:34:41 GMT

_Version update only_

## 7.19.1
Thu, 09 Dec 2021 00:21:54 GMT

_Version update only_

## 7.19.0
Wed, 08 Dec 2021 16:14:05 GMT

### Minor changes

- Update to TypeScript 4.5

## 7.18.21
Mon, 06 Dec 2021 16:08:33 GMT

_Version update only_

## 7.18.20
Fri, 03 Dec 2021 03:05:22 GMT

_Version update only_

## 7.18.19
Sat, 06 Nov 2021 00:09:13 GMT

_Version update only_

## 7.18.18
Fri, 05 Nov 2021 15:09:18 GMT

_Version update only_

## 7.18.17
Wed, 27 Oct 2021 00:08:15 GMT

### Patches

- Update the package.json repository field to include the directory property.

## 7.18.16
Wed, 13 Oct 2021 15:09:54 GMT

_Version update only_

## 7.18.15
Fri, 08 Oct 2021 08:08:34 GMT

_Version update only_

## 7.18.14
Thu, 07 Oct 2021 07:13:35 GMT

_Version update only_

## 7.18.13
Tue, 05 Oct 2021 15:08:38 GMT

_Version update only_

## 7.18.12
Mon, 04 Oct 2021 15:10:18 GMT

_Version update only_

## 7.18.11
Fri, 24 Sep 2021 00:09:29 GMT

_Version update only_

## 7.18.10
Thu, 23 Sep 2021 00:10:40 GMT

### Patches

- Upgrade the `@types/node` dependency to version to version 12.

## 7.18.9
Tue, 14 Sep 2021 01:17:04 GMT

_Version update only_

## 7.18.8
Mon, 13 Sep 2021 15:07:05 GMT

_Version update only_

## 7.18.7
Fri, 27 Aug 2021 00:07:25 GMT

_Version update only_

## 7.18.6
Fri, 20 Aug 2021 15:08:10 GMT

_Version update only_

## 7.18.5
Wed, 11 Aug 2021 00:07:21 GMT

_Version update only_

## 7.18.4
Wed, 14 Jul 2021 15:06:29 GMT

### Patches

- Fix an issue where the .d.ts rollup sometimes used "default" as an identifier name causing a syntax error (GitHub #2804)

## 7.18.3
Tue, 13 Jul 2021 23:00:33 GMT

### Patches

- Revert a workaround for TypeScript issue #44422 which was fixed in 4.3.3

## 7.18.2
Mon, 12 Jul 2021 23:08:26 GMT

_Version update only_

## 7.18.1
Thu, 08 Jul 2021 23:41:16 GMT

### Patches

- Fix a recent regression that reported "Internal Error: indentDocComment cannot be nested" (GitHub #2797)

## 7.18.0
Thu, 08 Jul 2021 06:00:48 GMT

### Minor changes

- Add support for import() type expressions (GitHub #1050) -- Thank you @javier-garcia-meteologica and @adventure-yunfei for solving this difficult problem!
- Improve formatting of declarations in .d.ts rollup and .api.md files, fixing some indentation issues

## 7.17.1
Thu, 01 Jul 2021 15:08:27 GMT

_Version update only_

## 7.17.0
Wed, 30 Jun 2021 15:06:54 GMT

### Minor changes

- Added support for "import * as module from './local/module';" (GitHub #1029) -- Big thanks to @adventure-yunfei, @mckn, @rbuckton, and @octogonz who all helped with this difficult PR!

### Patches

- Include /// directives in API report

## 7.16.1
Fri, 04 Jun 2021 19:59:53 GMT

_Version update only_

## 7.16.0
Fri, 04 Jun 2021 15:08:20 GMT

### Minor changes

- Upgrade the bundled compiler engine to TypeScript 4.3

## 7.15.2
Wed, 19 May 2021 00:11:39 GMT

_Version update only_

## 7.15.1
Mon, 03 May 2021 15:10:29 GMT

_Version update only_

## 7.15.0
Thu, 29 Apr 2021 23:26:50 GMT

### Minor changes

- Upgrade the bundled compiler engine to TypeScript 4.2

## 7.14.0
Tue, 20 Apr 2021 04:59:51 GMT

### Minor changes

- Projects can now define custom tags using a tsdoc.json file

## 7.13.5
Mon, 12 Apr 2021 15:10:28 GMT

_Version update only_

## 7.13.4
Thu, 08 Apr 2021 06:05:31 GMT

_Version update only_

## 7.13.3
Tue, 06 Apr 2021 15:14:22 GMT

_Version update only_

## 7.13.2
Thu, 04 Mar 2021 01:11:31 GMT

_Version update only_

## 7.13.1
Fri, 05 Feb 2021 16:10:42 GMT

_Version update only_

## 7.13.0
Wed, 13 Jan 2021 01:11:06 GMT

### Minor changes

- Upgrade the bundled compiler engine to TypeScript 4.1

## 7.12.1
Thu, 10 Dec 2020 23:25:49 GMT

### Patches

- Upgrade to TSDoc 0.12.24

## 7.12.0
Wed, 18 Nov 2020 08:19:54 GMT

### Minor changes

- The "isOptional" .api.json field is now applied to both methods and properties

## 7.11.5
Wed, 18 Nov 2020 06:21:57 GMT

### Patches

- Update .api.json file format to store a new field "isOptional" for documenting optional properties

## 7.11.4
Wed, 11 Nov 2020 01:08:58 GMT

_Version update only_

## 7.11.3
Tue, 10 Nov 2020 23:13:12 GMT

_Version update only_

## 7.11.2
Fri, 30 Oct 2020 06:38:38 GMT

_Version update only_

## 7.11.1
Fri, 30 Oct 2020 00:10:14 GMT

_Version update only_

## 7.11.0
Thu, 29 Oct 2020 06:14:19 GMT

### Minor changes

- Upgrade the bundled compiler engine to TypeScript 4.0

## 7.10.6
Wed, 28 Oct 2020 01:18:03 GMT

_Version update only_

## 7.10.5
Tue, 27 Oct 2020 15:10:13 GMT

_Version update only_

## 7.10.4
Tue, 06 Oct 2020 00:24:06 GMT

_Version update only_

## 7.10.3
Mon, 05 Oct 2020 22:36:57 GMT

_Version update only_

## 7.10.2
Mon, 05 Oct 2020 15:10:42 GMT

_Version update only_

## 7.10.1
Wed, 30 Sep 2020 18:39:17 GMT

### Patches

- Update to build with @rushstack/heft-node-rig

## 7.10.0
Wed, 30 Sep 2020 06:53:53 GMT

### Minor changes

- API Extractor now supports the config/rig.json system, as defined by @rushstack/rig-package
- Add IExtractorConfigPrepareOptions.projectFolderLookupToken
- Upgrade compiler; the API now requires TypeScript 3.9 or newer

### Patches

- Fix an InternalError reported when a declaration referred to itself using "tyepof"
- Update README.md

## 7.9.22
Tue, 22 Sep 2020 05:45:56 GMT

_Version update only_

## 7.9.21
Tue, 22 Sep 2020 01:45:31 GMT

_Version update only_

## 7.9.20
Tue, 22 Sep 2020 00:08:53 GMT

_Version update only_

## 7.9.19
Sat, 19 Sep 2020 04:37:26 GMT

_Version update only_

## 7.9.18
Sat, 19 Sep 2020 03:33:06 GMT

_Version update only_

## 7.9.17
Fri, 18 Sep 2020 22:57:24 GMT

_Version update only_

## 7.9.16
Fri, 18 Sep 2020 21:49:54 GMT

_Version update only_

## 7.9.15
Sun, 13 Sep 2020 01:53:20 GMT

_Version update only_

## 7.9.14
Fri, 11 Sep 2020 02:13:35 GMT

_Version update only_

## 7.9.13
Mon, 07 Sep 2020 07:37:37 GMT

_Version update only_

## 7.9.12
Sat, 05 Sep 2020 18:56:34 GMT

_Version update only_

## 7.9.11
Thu, 27 Aug 2020 11:27:06 GMT

_Version update only_

## 7.9.10
Mon, 24 Aug 2020 07:35:20 GMT

_Version update only_

## 7.9.9
Sat, 22 Aug 2020 05:55:42 GMT

_Version update only_

## 7.9.8
Fri, 21 Aug 2020 01:21:18 GMT

_Version update only_

## 7.9.7
Thu, 20 Aug 2020 15:13:53 GMT

_Version update only_

## 7.9.6
Tue, 18 Aug 2020 23:59:42 GMT

_Version update only_

## 7.9.5
Mon, 17 Aug 2020 04:53:23 GMT

_Version update only_

## 7.9.4
Wed, 12 Aug 2020 00:10:05 GMT

### Patches

- Updated project to build with Heft

## 7.9.3
Wed, 05 Aug 2020 18:27:32 GMT

_Version update only_

## 7.9.2
Thu, 09 Jul 2020 04:58:36 GMT

### Patches

- Fix an issue with handling of "export { default } from 'package';" (GitHub #2014)

## 7.9.1
Fri, 03 Jul 2020 15:09:04 GMT

_Version update only_

## 7.9.0
Fri, 03 Jul 2020 05:46:41 GMT

### Minor changes

- Add support for ECMAScript private fields (new in TypeScript 3.8)
- Add support for "import type" imports (new in TypeScript 3.8)
- Upgrade the bundled compiler engine to TypeScript 3.9

### Patches

- Fix an issue where chained compiler errors were not formatted correctly
- Log the TypeScript bundled compiler version, and warn if it is outdated

## 7.8.15
Thu, 25 Jun 2020 06:43:35 GMT

_Version update only_

## 7.8.14
Wed, 24 Jun 2020 09:50:48 GMT

_Version update only_

## 7.8.13
Wed, 24 Jun 2020 09:04:28 GMT

_Version update only_

## 7.8.12
Mon, 15 Jun 2020 22:17:17 GMT

### Patches

- Fix an issue where documentation hyperlinks were sometimes missing when using the "bundledPackages" feature (GitHub #1933)

## 7.8.11
Wed, 10 Jun 2020 20:48:30 GMT

_Version update only_

## 7.8.10
Mon, 01 Jun 2020 08:34:17 GMT

_Version update only_

## 7.8.9
Sat, 30 May 2020 02:59:54 GMT

_Version update only_

## 7.8.8
Thu, 28 May 2020 05:59:02 GMT

_Version update only_

## 7.8.7
Wed, 27 May 2020 05:15:10 GMT

_Version update only_

## 7.8.6
Tue, 26 May 2020 23:00:25 GMT

_Version update only_

## 7.8.5
Fri, 22 May 2020 15:08:42 GMT

_Version update only_

## 7.8.4
Thu, 21 May 2020 23:09:44 GMT

_Version update only_

## 7.8.3
Thu, 21 May 2020 15:41:59 GMT

_Version update only_

## 7.8.2
Tue, 19 May 2020 15:08:19 GMT

### Patches

- Report an error to indicate that "import()" types are not supported

## 7.8.1
Fri, 15 May 2020 08:10:59 GMT

_Version update only_

## 7.8.0
Wed, 06 May 2020 08:23:45 GMT

### Minor changes

- Version update only

## 7.7.13
Wed, 08 Apr 2020 04:07:33 GMT

_Version update only_

## 7.7.12
Sun, 29 Mar 2020 00:04:12 GMT

### Patches

- Improve analysis of types exposed via global variables (fixes GitHub issues #1765, #1095, and #1316)

## 7.7.11
Sat, 28 Mar 2020 00:37:16 GMT

### Patches

- Upgrade to TSdoc 0.12.19 to fix an issue where `<h1>` wasn't allowed as an HTML tag in a doc comment

## 7.7.10
Wed, 18 Mar 2020 15:07:47 GMT

### Patches

- Upgrade cyclic dependencies

## 7.7.9
Tue, 17 Mar 2020 23:55:58 GMT

### Patches

- Replace dependencies whose NPM scope was renamed from `@microsoft` to `@rushstack`

## 7.7.8
Tue, 28 Jan 2020 02:23:44 GMT

_Version update only_

## 7.7.7
Thu, 23 Jan 2020 01:07:56 GMT

_Version update only_

## 7.7.6
Tue, 21 Jan 2020 21:56:13 GMT

_Version update only_

## 7.7.5
Sun, 19 Jan 2020 02:26:52 GMT

### Patches

- Upgrade Node typings to Node 10

## 7.7.4
Fri, 17 Jan 2020 01:08:23 GMT

_Version update only_

## 7.7.3
Tue, 14 Jan 2020 01:34:15 GMT

### Patches

- Fix an issue where "ae-incompatible-release-tags" was sometimes reported incorectly for property setters (GitHub #1681)

## 7.7.2
Thu, 09 Jan 2020 06:44:12 GMT

### Patches

- Fix an error "Cannot read property 'externalModuleIndicator' of undefined" (GitHub #1652)

## 7.7.1
Wed, 08 Jan 2020 00:11:31 GMT

_Version update only_

## 7.7.0
Tue, 03 Dec 2019 03:17:43 GMT

### Minor changes

- Improve declaration reference syntax to allow linking to overloaded functions/methods
- Fix an issue with TypeScript 3.7, which now emits separate signatures for property getters/setters

## 7.6.2
Sun, 24 Nov 2019 00:54:04 GMT

_Version update only_

## 7.6.1
Wed, 20 Nov 2019 06:14:28 GMT

### Patches

- Fix an issue where the newlineKind setting wasn't being applied correctly

## 7.6.0
Fri, 15 Nov 2019 04:50:50 GMT

### Minor changes

- Make newline type for generated files configurable

## 7.5.6
Mon, 11 Nov 2019 16:07:56 GMT

_Version update only_

## 7.5.5
Wed, 06 Nov 2019 22:44:18 GMT

### Patches

- Add support for TypeScript 3.7

## 7.5.4
Tue, 05 Nov 2019 06:49:28 GMT

### Patches

- Fix an issue where API reports sometimes were ordered differently depending on the version of NodeJS (GitHub #1552)

## 7.5.3
Tue, 05 Nov 2019 01:08:39 GMT

_Version update only_

## 7.5.2
Tue, 22 Oct 2019 06:24:44 GMT

_Version update only_

## 7.5.1
Fri, 18 Oct 2019 15:15:01 GMT

_Version update only_

## 7.5.0
Sun, 06 Oct 2019 00:27:39 GMT

### Minor changes

- Allow separate release tags for overloaded functions and methods
- Add new api-extractor.json config setting "bundledPackages"

## 7.4.7
Fri, 04 Oct 2019 00:15:22 GMT

### Patches

- Fix an issue where IExtractorConfigPrepareOptions.packageJson was ignored (GitHub #1559)

## 7.4.6
Sun, 29 Sep 2019 23:56:29 GMT

### Patches

- Update repository URL

## 7.4.5
Wed, 25 Sep 2019 15:15:31 GMT

_Version update only_

## 7.4.4
Tue, 24 Sep 2019 02:58:49 GMT

_Version update only_

## 7.4.3
Mon, 23 Sep 2019 15:14:55 GMT

_Version update only_

## 7.4.2
Wed, 11 Sep 2019 19:56:23 GMT

### Patches

- Add support for an exported name that conflicts with a global name (GitHub #1350)

## 7.4.1
Tue, 10 Sep 2019 22:32:23 GMT

### Patches

- Update documentation

## 7.4.0
Tue, 10 Sep 2019 20:38:33 GMT

### Minor changes

- Add support for generating declaration references

## 7.3.11
Wed, 04 Sep 2019 18:28:06 GMT

_Version update only_

## 7.3.10
Wed, 04 Sep 2019 15:15:37 GMT

### Patches

- Update TSDoc dependency to 0.12.14

## 7.3.9
Fri, 30 Aug 2019 00:14:32 GMT

### Patches

- Fix a problem where Unicode API names were not handled correctly

## 7.3.8
Mon, 12 Aug 2019 15:15:14 GMT

_Version update only_

## 7.3.7
Thu, 08 Aug 2019 15:14:17 GMT

_Version update only_

## 7.3.6
Thu, 08 Aug 2019 00:49:05 GMT

### Patches

- Fix an issue where a function with only one declaration was assigned an overloadIndex of 0 instead of 1

## 7.3.5
Mon, 05 Aug 2019 22:04:32 GMT

### Patches

- Security updates.

## 7.3.4
Tue, 23 Jul 2019 01:13:01 GMT

### Patches

- ApiItem.name is now quoted when it contains invalid identifier characters, to avoid conflicts with an ECMAScript symbol expression

## 7.3.3
Mon, 22 Jul 2019 19:13:10 GMT

### Patches

- Update to use new api-extractor-model

## 7.3.2
Fri, 12 Jul 2019 19:12:46 GMT

### Patches

- Clarify docs for "--typescript-compiler-folder"

## 7.3.1
Thu, 11 Jul 2019 19:13:08 GMT

### Patches

- Add support for TypeScript 3.5

## 7.3.0
Tue, 09 Jul 2019 19:13:24 GMT

### Minor changes

- Add a "--diagnostics" command-line option to help when troubleshooting problems

## 7.2.3
Mon, 08 Jul 2019 19:12:18 GMT

### Patches

- Fix a problem when analyzing .d.ts files that appear in the same folder as the corresponding .ts file (GitHub #1310)

## 7.2.2
Sat, 29 Jun 2019 02:30:10 GMT

### Patches

- Fix GitHub issue #1304 where "IExtractorInvokeOptions.typescriptCompilerFolder" did not work with TypeScript 3.4

## 7.2.1
Wed, 12 Jun 2019 19:12:33 GMT

_Version update only_

## 7.2.0
Tue, 11 Jun 2019 00:48:06 GMT

### Minor changes

- Generate ApiTypeParameter entries and type alias types

## 7.1.8
Wed, 05 Jun 2019 19:12:34 GMT

### Patches

- Fix an issue where TSDoc index selectors (ApiParameterListMixin.overloadIndex) started from 0, whereas TSDoc requires a nonzero number

## 7.1.7
Tue, 04 Jun 2019 05:51:53 GMT

### Patches

- Upgrade api-extractor-model to remove ApiConstructor.isStatic, since TypeScript constructors cannot be static
- Improve handling of symbolic property and method names.

## 7.1.6
Mon, 27 May 2019 04:13:44 GMT

### Patches

- Fix incorrect path resolution for the "extends" field when loading tsconfig.json

## 7.1.5
Mon, 13 May 2019 02:08:35 GMT

### Patches

- Broaden support for default imports

## 7.1.4
Mon, 06 May 2019 20:46:21 GMT

_Version update only_

## 7.1.3
Mon, 06 May 2019 19:34:54 GMT

### Patches

- Add a new setting "omitTrimmingComments" to prevent extra comments from being emitted in the .d.ts rollup

## 7.1.2
Mon, 06 May 2019 19:11:16 GMT

### Patches

- Fix an issue where ExtractorResult.warningCount was not incremented for messages handled by IExtractorInvokeOptions.messageCallback (GitHub #1258)

## 7.1.1
Tue, 30 Apr 2019 23:08:02 GMT

### Patches

- Fix an issue where API signatures were sometimes truncated in the .api.json file (GitHub #1249)

## 7.1.0
Tue, 16 Apr 2019 11:01:37 GMT

### Minor changes

- Initial stable release of API Extractor 7

## 7.0.42
Fri, 12 Apr 2019 06:13:16 GMT

### Patches

- Fix a regression that prevented certain types of warnings from being reported

## 7.0.41
Thu, 11 Apr 2019 07:14:01 GMT

### Patches

- THIS IS A RELEASE CANDIDATE FOR API-EXTRACTOR 7
- (Breaking change) Rename "mainEntryPointFile" to "mainEntryPointFilePath" so all settings use a consistent naming convention
- (Breaking change) Paths that appear in api-extractor.json are now resolved relative to the config file unless prefixed with the `<projectFolder>` token
- Add a new api-extractor.json setting "tsconfigFilePath" for customizing the tsconfig.json path
- Replace ExtractorConfig.packageJsonFullPath with ExtractorConfig.packageFolder
- Upgrade API Extractor to use TypeScript 3.4 for analysis

## 7.0.40
Tue, 09 Apr 2019 05:31:01 GMT

### Patches

- Improve the "--local" option to automatically create the API report file if it is missing

## 7.0.39
Mon, 08 Apr 2019 19:12:52 GMT

### Patches

- Rename "addToApiReviewFile" setting to "addToApiReportFile"

## 7.0.38
Sat, 06 Apr 2019 02:05:51 GMT

### Patches

- (Breaking change) Removed the ILogger API and renamed ExtractorMessageLogLevel to ExtractorLogLevel
- (Breaking change) Extractor console output is now modeled as ExtractorMessage objects and can be customized/filtered/handled by IExtractorInvokeOptions.messageCallback

## 7.0.37
Fri, 05 Apr 2019 04:16:16 GMT

### Patches

- Introduce "api-extractor init" command-line that helps enable API Extractor for a new project
- (Breaking change) Major redesign of the API used to invoke API Extractor
- (Breaking change) Major redesign of the api-extractor.json config file format
- Add a CompilerState API that allows an optimization where multiple invocations of Extractor can reuse the same TypeScript compiler analysis

## 7.0.36
Wed, 03 Apr 2019 02:58:33 GMT

### Patches

- Fix an issue where .d.ts.map file were sometimes mapped to the wrong location

## 7.0.35
Sat, 30 Mar 2019 22:27:16 GMT

### Patches

- Reintroduce the generated documentation notice for internal constructors
- Add limited support for resolving @inheritDoc references to external packages by postprocessing them in api-documenter

## 7.0.34
Thu, 28 Mar 2019 19:14:27 GMT

### Patches

- Validate `@link` tags and report a warning if the link cannot be resolved

## 7.0.33
Tue, 26 Mar 2019 20:54:18 GMT

### Patches

- Reintroduce support for `@inheritDoc` tags

## 7.0.32
Sat, 23 Mar 2019 03:48:31 GMT

### Patches

- If the TSDoc summary is missing for a class constructor, then automatically generate it
- Reintroduce support for the `@preapproved` TSDoc tag

## 7.0.31
Thu, 21 Mar 2019 04:59:11 GMT

### Patches

- Reintroduce "ae-internal-missing-underscore" warning for API items marked as `@internal` but whose name does not start with an underscore

## 7.0.30
Thu, 21 Mar 2019 01:15:32 GMT

### Patches

- Improve the API review file generation to include imports and support multiple exports

## 7.0.29
Wed, 20 Mar 2019 19:14:49 GMT

### Patches

- API Extractor can now analyze packages whose package.json file is missing the "version" field

## 7.0.28
Mon, 18 Mar 2019 04:28:43 GMT

### Patches

- Rename the "ae-inconsistent-release-tags" warning to "ae-different-release-tags"
- Introduce a new warning "ae-incompatible-release-tags" that checks for API signatures that reference types with incompatible release tags
- Fix an issue where this error was sometimes reported incorrectly: "The messages.extractorMessageReporting table contains an unrecognized identifier ___"

## 7.0.27
Fri, 15 Mar 2019 19:13:25 GMT

### Patches

- (Breaking change) The file extension for API review files has changed from ".api.ts" to "api.md".  For details see https://github.com/microsoft/web-build-tools/issues/1123

## 7.0.26
Wed, 13 Mar 2019 19:13:14 GMT

### Patches

- Refactor code to move the IndentedWriter API from api-extractor-model to api-documenter

## 7.0.25
Wed, 13 Mar 2019 01:14:05 GMT

### Patches

- Upgrade TSDoc

## 7.0.24
Mon, 11 Mar 2019 16:13:36 GMT

### Patches

- Fix an issue where spurious TSDoc warnings were issued because the TSDoc parser was configured improperly
- Move the .api.json related APIs into a new NPM package @microsoft/api-extractor-model

## 7.0.23
Tue, 05 Mar 2019 17:13:11 GMT

### Patches

- Issue a warning when an exported type refers to another local type that is not exported (ae-forgotten-export)
- The export analyzer now correctly handles symbols imported using "import x = require('y');" notation

## 7.0.22
Mon, 04 Mar 2019 17:13:19 GMT

### Patches

- Every error/warning message reported by API Extractor now has an associated message identifier
- Add a new section to api-extractor.json for configuring how errors get reported, with ability to suppress individual errors
- Reintroduce the ability to report issues by writing warnings into the API review file
- Fix an issue where members of type literals were incorrectly being flagged as "(undocumented)"
- Error messages now cite the original .ts source file, if a source map is present. (To enable this, specify `"declarationMap": true` in tsconfig.json.)

## 7.0.21
Wed, 27 Feb 2019 22:13:58 GMT

_Version update only_

## 7.0.20
Wed, 27 Feb 2019 17:13:17 GMT

_Version update only_

## 7.0.19
Mon, 18 Feb 2019 17:13:23 GMT

### Minor changes

- New way to resolve & generate TSDoc metadata file

## 7.0.18
Tue, 12 Feb 2019 17:13:12 GMT

### Patches

- Add a workaround for the issue where .d.ts rollups sometimes define names that conflict with a global symbol (the full solution is tracked by GitHub #1095)

## 7.0.17
Mon, 11 Feb 2019 10:32:37 GMT

### Patches

- Fix an issue where API Extractor neglected to analyze "typeof" expressions
- Fix an issue where declarations inside a namespace were sometimes being incorrectly emitted as top-level exports of the .d.ts rollup

## 7.0.16
Mon, 11 Feb 2019 08:55:57 GMT

### Patches

- Redesign the analyzer so that when an external symbol is reexported by the working package, the local object (AstImport) and external object (AstSymbol) are kept separate
- Fix a number of bugs where external symbols were misinterpreted as being part of the local project
- Eliminate a number of errors involving unusual language constructs, by avoiding analysis of external symbols unless it's really necessary
- Simplify the AstSymbol.nominalAnalysis concept and associated code
- Improve .d.ts rollup trimming to handle reexported symbols correctly

## 7.0.15
Mon, 11 Feb 2019 03:31:55 GMT

### Patches

- The `--debug` parameter now automatically breaks in the debugger when InternalError is thrown

## 7.0.14
Thu, 31 Jan 2019 17:03:49 GMT

### Patches

- Upgrade to TSDoc 0.12.5, which allows `$` in `@param` names
- Add "testMode" option in api-extractor.json to eliminate spurious diffs in test files when the version number gets bumped
- Normalize newlines for excerpt strings in the .api.json file

## 7.0.13
Sat, 19 Jan 2019 03:47:47 GMT

### Patches

- Move the skipLibCheck into the config file.

## 7.0.12
Sat, 19 Jan 2019 01:17:51 GMT

### Patches

- Fix an issue where files using "export=" were incorrectly interpreted as having ambient declarations

## 7.0.11
Fri, 18 Jan 2019 00:52:21 GMT

### Patches

- Add support for circular references between files that use `export * from "____";`

## 7.0.10
Thu, 17 Jan 2019 00:37:54 GMT

### Patches

- Add support for exports of the form `export * from "____";`
- Improve the analyzer to allow a declaration to be exported more than once
- Fix inconsistent newlines in .api.ts files

## 7.0.9
Thu, 10 Jan 2019 01:57:52 GMT

### Patches

- Fix an issue with rolling up default exports (https://github.com/microsoft/web-build-tools/issues/1007)

## 7.0.8
Thu, 20 Dec 2018 17:04:08 GMT

### Patches

- Fix an issue where it was possible to import forgotten declarations from a .d.ts rollup, even though they did not have an explicit "export" modifier

## 7.0.7
Wed, 19 Dec 2018 05:57:33 GMT

### Patches

- Extend ApiModel to support new item kinds: ApiCallSignature, ApiConstructor, ApiConstructSignature, ApiFunction, ApiIndexSignature, ApiTypeAlias, and ApiVariable

## 7.0.6
Fri, 14 Dec 2018 19:43:46 GMT

### Patches

- Update web site URLs

## 7.0.5
Thu, 13 Dec 2018 02:58:10 GMT

### Patches

- Remove unused jju dependency

## 7.0.4
Wed, 12 Dec 2018 17:04:19 GMT

### Patches

- Reintroduce support for "extends" and "implements" heritage clauses
- Redesign the Excerpt API to support multiple subranges (e.g. for a list of "implements" clauses)

## 7.0.3
Fri, 07 Dec 2018 17:04:56 GMT

### Patches

- Added more API documentation

## 7.0.2
Wed, 05 Dec 2018 19:57:03 GMT

### Patches

- fix reexported types from an external package for dts rollup

## 7.0.1
Wed, 05 Dec 2018 17:04:18 GMT

### Patches

- Fix an issue where .d.ts trimming did not properly handle variable declarations (GitHub #976)

## 7.0.0
Thu, 29 Nov 2018 07:02:09 GMT

### Breaking changes

- THIS IS A BETA RELEASE - We are bumping the version to "7.0.0" to simplify dogfooding. This release is not yet ready for general usage.

## 6.3.0
Wed, 28 Nov 2018 19:29:53 GMT

### Minor changes

- Support "extends" field in api-extractor.json config files for easier management of monorepos with many projects

## 6.2.0
Wed, 28 Nov 2018 02:17:11 GMT

### Minor changes

- Introduce a new build output "dist/tsdoc-metdata.json", which completely replaces the old "tsdocFlavor" field in package.json

## 6.1.6
Fri, 16 Nov 2018 21:37:10 GMT

### Patches

- Add support for emitting `/// <reference lib="___" />` directives in .d.ts rollups (GitHub issue #946)

## 6.1.5
Fri, 16 Nov 2018 00:59:00 GMT

### Patches

- Fix an issue where .d.ts trimming did not work for exported variable declarations (GitHub #936)

## 6.1.4
Fri, 09 Nov 2018 23:07:39 GMT

### Patches

- Upgrade to TSDoc 0.21.2, which improves trimming of link text in `@link` tags

## 6.1.3
Wed, 07 Nov 2018 21:04:35 GMT

_Version update only_

## 6.1.2
Mon, 05 Nov 2018 17:04:24 GMT

### Patches

- Upgrade to @microsoft/tsdoc 0.12.0

## 6.1.1
Thu, 01 Nov 2018 19:32:52 GMT

### Patches

- Fix an issue where EcmaScript symbols ("computed property names") were missing from .d.ts rollups

## 6.1.0
Wed, 31 Oct 2018 17:00:54 GMT

### Minor changes

- Added an api to invoke api extractor processor by supplying api extractor json config file.

## 6.0.9
Thu, 25 Oct 2018 23:20:40 GMT

_Version update only_

## 6.0.8
Thu, 25 Oct 2018 08:56:02 GMT

### Patches

- Fix issue where `DocErrorText.text` returned `[object Object]` instead of the text 

## 6.0.7
Wed, 24 Oct 2018 16:03:10 GMT

_Version update only_

## 6.0.6
Thu, 18 Oct 2018 01:32:20 GMT

### Patches

- Fix isAbsolute check for mainDtsRollupPath

## 6.0.5
Wed, 17 Oct 2018 21:04:49 GMT

_Version update only_

## 6.0.4
Wed, 17 Oct 2018 14:43:24 GMT

### Patches

- Fix a regression where namespaces were sometimes incorrectly handled in "conservative" mode
- Update the command line to look for api-extractor.json in both the "./config" folder and the project folder
- Allow type references in namespaces when namespaceSupport=conservative

## 6.0.3
Thu, 11 Oct 2018 23:26:07 GMT

### Patches

- Fix an issue where `import x from "."` was sometimes not processed correctly

## 6.0.2
Tue, 09 Oct 2018 06:58:01 GMT

### Patches

- Fix a regression where API Extractor was sometimes reporting incorrect line numbers

## 6.0.1
Mon, 08 Oct 2018 16:04:27 GMT

_Version update only_

## 6.0.0
Sun, 07 Oct 2018 06:15:56 GMT

### Breaking changes

- (Breaking change) API Extractor 6 introduces support for TSDoc doc comment syntax!  Please see https://api-extractor.com/ for documentation.  To learn more about the TSDoc standard, check out https://github.com/microsoft/tsdoc

## 5.13.1
Fri, 28 Sep 2018 16:05:35 GMT

_Version update only_

## 5.13.0
Wed, 26 Sep 2018 21:39:40 GMT

### Minor changes

- Add new command line option --skip-lib-check

## 5.12.2
Mon, 24 Sep 2018 23:06:40 GMT

### Patches

- Allow doc comments to use TSDoc's "@defaultvalue" tag (but the value is not yet passed to the documentation pipeline)

## 5.12.1
Fri, 21 Sep 2018 16:04:42 GMT

### Patches

- Fix an issue where TypeScript errors are often logged as "[Object object]" instead of the actual error message.

## 5.12.0
Thu, 20 Sep 2018 23:57:21 GMT

### Minor changes

- Add new feature: Support using a different version of the TypeScript compiler.

## 5.11.2
Tue, 18 Sep 2018 21:04:55 GMT

### Patches

- Fix an issue where parameters mentioned in comments were attempting to be analyzed by api-extractor.

## 5.11.1
Thu, 06 Sep 2018 01:25:25 GMT

### Patches

- Update "repository" field in package.json

## 5.11.0
Mon, 03 Sep 2018 16:04:45 GMT

### Minor changes

- Upgrade api-extractor to internally use TypeScript 3.0.

## 5.10.8
Wed, 29 Aug 2018 06:36:50 GMT

_Version update only_

## 5.10.7
Thu, 23 Aug 2018 18:18:53 GMT

### Patches

- Republish all packages in web-build-tools to resolve GitHub issue #782

## 5.10.6
Wed, 22 Aug 2018 20:58:58 GMT

_Version update only_

## 5.10.5
Wed, 22 Aug 2018 16:03:25 GMT

_Version update only_

## 5.10.4
Tue, 21 Aug 2018 16:04:38 GMT

### Patches

- fix namespace name for export statement`

## 5.10.3
Thu, 09 Aug 2018 21:03:22 GMT

_Version update only_

## 5.10.2
Thu, 09 Aug 2018 16:04:24 GMT

### Patches

- Update lodash.

## 5.10.1
Thu, 26 Jul 2018 16:04:17 GMT

_Version update only_

## 5.10.0
Tue, 17 Jul 2018 16:02:52 GMT

### Minor changes

- Add support for new "@eventproperty" AEDoc tag, which indicates that a class/interface property should be documented as an event

## 5.9.1
Tue, 03 Jul 2018 21:03:31 GMT

_Version update only_

## 5.9.0
Sat, 23 Jun 2018 02:21:20 GMT

### Minor changes

- Add new IMarkupHtmlTag API
- AEDoc now allows HTML tags inside doc comments, which can be disabled using a backslash escape

## 5.8.1
Thu, 21 Jun 2018 08:27:29 GMT

_Version update only_

## 5.8.0
Tue, 19 Jun 2018 19:35:11 GMT

### Minor changes

- For namespaceSupport=permissive, allow arbitrary nesting of namespaces

### Patches

- Fix an issue where multi-line type literals sometimes had inconsistent newlines in the *.api.json file

## 5.7.3
Fri, 08 Jun 2018 08:43:52 GMT

_Version update only_

## 5.7.2
Thu, 31 May 2018 01:39:33 GMT

_Version update only_

## 5.7.1
Tue, 15 May 2018 02:26:45 GMT

_Version update only_

## 5.7.0
Tue, 15 May 2018 00:18:10 GMT

### Minor changes

- Add support for new AEDoc tags @sealed, @virtual, and @override

## 5.6.8
Fri, 04 May 2018 00:42:38 GMT

### Patches

- Fix the formatting of a log message.

## 5.6.7
Tue, 01 May 2018 22:03:20 GMT

### Patches

- Fix an issue where the *.d.ts rollup trimming did not trim import statements

## 5.6.6
Fri, 27 Apr 2018 03:04:32 GMT

_Version update only_

## 5.6.5
Thu, 19 Apr 2018 21:25:56 GMT

_Version update only_

## 5.6.4
Thu, 19 Apr 2018 17:02:06 GMT

### Patches

- Fix errors in schema documentation

## 5.6.3
Tue, 03 Apr 2018 16:05:29 GMT

_Version update only_

## 5.6.2
Mon, 02 Apr 2018 16:05:24 GMT

### Patches

- Refactor to use new @microsoft/node-core-library

## 5.6.1
Tue, 27 Mar 2018 01:34:25 GMT

### Patches

- Update build config so API Extractor builds using the latest version of itself

## 5.6.0
Sun, 25 Mar 2018 01:26:19 GMT

### Minor changes

- Improve the api-extractor.json config file so that *.d.ts rollups go in separate folders, and trimming can now be disabled

### Patches

- In preparation for initial release, the "Package Typings" feature was renamed to "DTS Rollup"
- Fix an issue where the @packagedocumentation comment was sometimes getting mixed into the middle of the rollup *.d.ts file

## 5.5.2
Fri, 23 Mar 2018 00:34:53 GMT

### Patches

- Upgrade colors to version ~1.2.1

## 5.5.1
Tue, 20 Mar 2018 02:44:45 GMT

### Patches

- Improve packageTypings generator to trim nested members according to their release tag
- Fix a bug where packageTypings failed to handle merged declarations properly

## 5.5.0
Sat, 17 Mar 2018 02:54:22 GMT

### Minor changes

- Overhaul the packageTypings generator analysis to get ready for the upcoming nested member trimming
- Breaking change: Any projects using the package typings feature must now have a "tsdoc" section in their package.json

### Patches

- Add "--debug" flag for debugging

## 5.4.0
Thu, 15 Mar 2018 20:00:50 GMT

### Minor changes

- Add a new setting validationRules.missingReleaseTags to optionally remove the requirement that every API item should have a release tag
- Add new API "Markup.formatApiItemReference()"

### Patches

- Fix an issue where the automatically generated documentation for class constructors sometimes had a broken hyperlink

## 5.3.9
Thu, 15 Mar 2018 16:05:43 GMT

_Version update only_

## 5.3.8
Mon, 12 Mar 2018 20:36:19 GMT

### Patches

- Locked down some "@types/" dependency versions to avoid upgrade conflicts

## 5.3.7
Tue, 06 Mar 2018 17:04:51 GMT

### Patches

- Add preliminary support for preview and public outputs for packageTypings generator

## 5.3.6
Fri, 02 Mar 2018 01:13:59 GMT

_Version update only_

## 5.3.5
Tue, 27 Feb 2018 22:05:57 GMT

_Version update only_

## 5.3.4
Wed, 21 Feb 2018 22:04:19 GMT

_Version update only_

## 5.3.3
Wed, 21 Feb 2018 03:13:28 GMT

_Version update only_

## 5.3.2
Sat, 17 Feb 2018 02:53:49 GMT

### Patches

- Fix several bugs with the way that imports were being deduplicated by the packageTypings feature

## 5.3.1
Fri, 16 Feb 2018 22:05:23 GMT

_Version update only_

## 5.3.0
Fri, 16 Feb 2018 17:05:11 GMT

### Minor changes

- Fix an issue where the packageTypings feature didn't handle some import/export patterns

### Patches

- Fix an issue where the packageTypings feature sometimes emitted "default" instead of the class name
- Improve the packageTypings feature to support triple-slash references to typings

## 5.2.7
Wed, 07 Feb 2018 17:05:11 GMT

_Version update only_

## 5.2.6
Fri, 26 Jan 2018 22:05:30 GMT

_Version update only_

## 5.2.5
Fri, 26 Jan 2018 17:53:38 GMT

### Patches

- Force a patch bump in case the previous version was an empty package

## 5.2.4
Fri, 26 Jan 2018 00:36:51 GMT

_Version update only_

## 5.2.3
Tue, 23 Jan 2018 17:05:28 GMT

_Version update only_

## 5.2.2
Thu, 18 Jan 2018 03:23:46 GMT

### Patches

- Enable package typings generated by api-extractor

## 5.2.1
Thu, 18 Jan 2018 00:48:06 GMT

_Version update only_

## 5.2.0
Thu, 18 Jan 2018 00:27:23 GMT

### Minor changes

- Improve the packageTypings feature to support abstract classes and "import * as X" imports

## 5.1.3
Wed, 17 Jan 2018 10:49:31 GMT

_Version update only_

## 5.1.2
Fri, 12 Jan 2018 03:35:22 GMT

### Patches

- Add some incremental improvements for the experimental PackageTypingsGenerator feature

## 5.1.1
Thu, 11 Jan 2018 22:31:51 GMT

_Version update only_

## 5.1.0
Wed, 10 Jan 2018 20:40:01 GMT

### Minor changes

- Upgrade to Node 8

### Patches

- Continued progress for the experimental PackageTypingsGenerator

## 5.0.1
Tue, 09 Jan 2018 17:05:51 GMT

### Patches

- Get web-build-tools building with pnpm

## 5.0.0
Sun, 07 Jan 2018 05:12:08 GMT

### Breaking changes

- API Extractor now processes *.d.ts files instead of *.ts files

### Minor changes

- Introduced new tag @packagedocumentation which replaces the earlier approach that used a "packageDescription" variable

## 4.3.7
Fri, 05 Jan 2018 20:26:45 GMT

_Version update only_

## 4.3.6
Fri, 05 Jan 2018 00:48:41 GMT

### Patches

- Update Jest to ~21.2.1

## 4.3.5
Fri, 22 Dec 2017 17:04:46 GMT

### Patches

- Fixed an issue where warnings would cause the api-extractor tool to return a nonzero exit code for a "--local" build; warnings should not fail the build in this scenario

## 4.3.4
Tue, 12 Dec 2017 03:33:26 GMT

_Version update only_

## 4.3.3
Thu, 30 Nov 2017 23:59:09 GMT

_Version update only_

## 4.3.2
Thu, 30 Nov 2017 23:12:21 GMT

_Version update only_

## 4.3.1
Wed, 29 Nov 2017 17:05:37 GMT

_Version update only_

## 4.3.0
Tue, 28 Nov 2017 23:43:55 GMT

### Minor changes

- Add Extractor.processProject() whose return value indicates success

### Patches

- Deprecate Extractor.analyzeProject() API

## 4.2.6
Mon, 13 Nov 2017 17:04:50 GMT

_Version update only_

## 4.2.5
Mon, 06 Nov 2017 17:04:18 GMT

_Version update only_

## 4.2.4
Thu, 02 Nov 2017 16:05:24 GMT

### Patches

- lock the reference version between web build tools projects

## 4.2.3
Wed, 01 Nov 2017 21:06:08 GMT

### Patches

- Upgrade cyclic dependencies

## 4.2.2
Tue, 31 Oct 2017 21:04:04 GMT

_Version update only_

## 4.2.1
Tue, 31 Oct 2017 16:04:55 GMT

_Version update only_

## 4.2.0
Wed, 25 Oct 2017 20:03:59 GMT

### Minor changes

- Improved the way API JSON represents documentation markup; this is a file format change

## 4.1.2
Tue, 24 Oct 2017 18:17:12 GMT

_Version update only_

## 4.1.1
Mon, 23 Oct 2017 21:53:12 GMT

### Patches

- Updated cyclic dependencies

## 4.1.0
Fri, 20 Oct 2017 19:57:12 GMT

### Minor changes

- Add policies.namespaceSupport option to API Extractor config

### Patches

- Fixed an issue where properties were sometimes marked as readonly; a remark is automatically generated for classes with internal constructors

## 4.0.1
Fri, 20 Oct 2017 01:52:54 GMT

### Patches

- Rename ApiExtractor class to Extractor

## 4.0.0
Fri, 20 Oct 2017 01:04:44 GMT

### Breaking changes

- Redesigned interface for invoking API Extractor

## 3.4.2
Thu, 05 Oct 2017 01:05:02 GMT

_Version update only_

## 3.4.1
Fri, 29 Sep 2017 01:03:42 GMT

### Patches

- Removed IMarkupPage.docId

## 3.4.0
Thu, 28 Sep 2017 01:04:28 GMT

### Minor changes

- Skipping two lines in an AEDoc comment now creates a paragraph separator for the generated documentation

### Patches

- The *.api.json "linkDocElement" type now always explicitly specifies the package name, rather than expecting the reader to infer it
- The *.api.json file format now exposes "signature" information for properties, functions, and module variables

## 3.3.0
Fri, 22 Sep 2017 01:04:02 GMT

### Minor changes

- Upgrade to es6

## 3.2.6
Wed, 20 Sep 2017 22:10:17 GMT

_Version update only_

## 3.2.5
Mon, 11 Sep 2017 13:04:55 GMT

### Patches

- The isBeta and deprecatedMessage fields are now inherited in the *.api.json files
- Fix an issue where the *.api.json file was sometimes missing function parameters

## 3.2.4
Fri, 08 Sep 2017 01:28:04 GMT

### Patches

- Deprecate @types/es6-coll ections in favor of built-in typescript typings 'es2015.collection' a nd 'es2015.iterable'

## 3.2.3
Thu, 07 Sep 2017 13:04:35 GMT

### Patches

- Fix incorrect schema/typings for enum members

## 3.2.2
Thu, 07 Sep 2017 00:11:11 GMT

### Patches

-  Add $schema field to all schemas

## 3.2.1
Wed, 06 Sep 2017 13:03:42 GMT

### Patches

- Converted IMarkupDocumentationLink to IMarkupApiLink, which exposes the underlying IApiItemReference rather than assuming a particular "document ID" model

## 3.2.0
Tue, 05 Sep 2017 19:03:56 GMT

### Minor changes

- Add the constructor signature and package name to the exported API signature

## 3.1.0
Sat, 02 Sep 2017 01:04:26 GMT

### Minor changes

- Expanded the api-extractor API to expose interfaces for the *.api.json file fileformat

## 3.0.0
Thu, 31 Aug 2017 18:41:18 GMT

### Breaking changes

- Fix compatibility issues with old releases, by incrementing the major version number

## 2.3.7
Thu, 31 Aug 2017 17:46:25 GMT

### Patches

- Fix issue where node-core-library was not an explicit dependency

## 2.3.6
Wed, 30 Aug 2017 01:04:34 GMT

_Version update only_

## 2.3.5
Thu, 24 Aug 2017 22:44:12 GMT

### Patches

- Update the schema validator.

## 2.3.4
Thu, 24 Aug 2017 01:04:33 GMT

_Version update only_

## 2.3.3
Tue, 22 Aug 2017 13:04:22 GMT

### Patches

- Added "api-documenter" code sample

## 2.3.2
Tue, 15 Aug 2017 01:29:31 GMT

### Patches

- Introduce Span parser for upcoming *.d.ts generator

## 2.3.1
Thu, 27 Jul 2017 01:04:48 GMT

### Patches

- Upgrade to the TS2.4 version of the build tools.

## 2.3.0
Tue, 25 Jul 2017 20:03:31 GMT

### Minor changes

- Upgrade to TypeScript 2.4

## 2.2.0
Wed, 21 Jun 2017 04:19:35 GMT

### Minor changes

- Add two new features: An error is reported if a top-level definition is missing its release tag. The constructor summary will now be autogenerated if omitted.

## 2.0.10
Tue, 20 Jun 2017 01:04:54 GMT

### Patches

- Improve the wording of many error messages
- Fix a bug with parsing of @link tags
- Issue warnings for @internal definitions that are not prefixed with an underscore

## 2.0.9
Sat, 17 Jun 2017 01:02:59 GMT

### Patches

- The unsupported @summary tag is now reported as an error
- Use a cache to speed up package.json lookups

## 2.0.8
Wed, 14 Jun 2017 13:03:40 GMT

### Patches

- Definitions marked as @beta are now included in the *.api.json files for documentation

## 2.0.7
Thu, 08 Jun 2017 05:15:52 GMT

### Patches

- Updated README.md

## 2.0.6
Mon, 15 May 2017 21:59:43 GMT

### Patches

- Added support for Namespace with ApiNamespace

## 2.0.5
Sat, 22 Apr 2017 01:02:03 GMT

### Patches

- Added check for API names that are not supported (only letters and numbers supported)

## 2.0.4
Wed, 19 Apr 2017 20:18:06 GMT

### Patches

- Remove ES6 Promise & @types/es6-promise typings

## 2.0.3
Fri, 14 Apr 2017 17:44:08 GMT

### Patches

- Added collect references ability to detect determine type information of return types and parameter types.

## 2.0.2
Fri, 07 Apr 2017 21:43:16 GMT

### Patches

- Adjusted the version specifier for typescript to ~2.2.2

## 2.0.1
Thu, 06 Apr 2017 01:32:23 GMT

### Patches

- Removed hard coding of @public for ApiPackage

## 2.0.0
Mon, 20 Mar 2017 21:52:20 GMT

### Breaking changes

- Fixing whitespace, also a variable that was shadowing another variable.

## 1.1.19
Mon, 20 Mar 2017 04:20:13 GMT

### Patches

- Reverting change.

## 1.1.18
Mon, 20 Mar 2017 03:50:55 GMT

### Patches

- Reverting previous change, which causes a regression in SPFx yeoman sc enario.

## 1.1.17
Mon, 20 Mar 2017 00:54:03 GMT

### Patches

- Fixing lint whitespace issues.

## 1.1.16
Sun, 19 Mar 2017 19:10:30 GMT

### Patches

- Fixing variable that was shadowing another variable.

## 1.1.15
Wed, 15 Mar 2017 01:32:09 GMT

### Patches

- Locking `@types` packages. Synchronizing version specifiers for dependencies with other `web-build-tools` projects.

## 1.1.14
Sat, 18 Feb 2017 02:32:06 GMT

### Patches

- Seperated the ApiItem initialization into 3 stages: create documentation that doesn't require resolution, then complete initialization by resolving links and inheritdocs. This allows us to ignore harmless cycles like type references"

## 1.1.13
Thu, 16 Feb 2017 22:10:39 GMT

### Patches

- Fixed Api-Extractor error message, changed apostrophe to backtick.

## 1.1.12
Thu, 16 Feb 2017 18:56:57 GMT

### Patches

- Added support for local API definition resolution"

## 1.1.11
Sat, 11 Feb 2017 02:32:35 GMT

### Patches

- Changed dependency for ApiDocumentation to abstract the resolving of API definition references.

## 1.1.10
Fri, 10 Feb 2017 20:01:30 GMT

### Patches

-  Added support to not throw error, instead report error if no type is declared on properties and parameters

## 1.1.9
Tue, 07 Feb 2017 20:37:06 GMT

### Patches

- Fixing issue where undocumented comment was not being emitted.

## 1.1.8
Sat, 04 Feb 2017 02:32:05 GMT

### Patches

- Moved ApiItem references within ApiDocumentation, to ApiItem caller.

## 1.1.7
Thu, 02 Feb 2017 14:05:53 GMT

### Patches

- Refactored ApiDocumentation creation to resolve references method.

## 1.1.6
Wed, 01 Feb 2017 20:09:30 GMT

### Patches

- Added ApiItemKind enum and refactored child classes.

## 1.1.5
Fri, 27 Jan 2017 20:04:15 GMT

### Patches

- Changed name of Analyzer to Extractor, added support for external api json doc loading.

## 1.1.4
Fri, 27 Jan 2017 02:35:10 GMT

### Patches

- Added ExternalApiHelper class to be used in generating api documentation json files for external types.
- Added description for packages implementation.
- Added config folder with file to enable api-extractor on itself. rebuild project on previous build.

## 1.1.3
Tue, 24 Jan 2017 01:36:35 GMT

### Patches

- Json schema was updated to reflect feature additions to linkDocElement. The linkDocElement can now be of type 'code' which refers to an API definition reference.

## 1.1.2
Fri, 20 Jan 2017 01:46:41 GMT

_Version update only_

## 1.1.1
Thu, 19 Jan 2017 20:04:40 GMT

### Patches

- Check for missing JSDoc sequences changed.
- Improved error messages

## 1.1.0
Wed, 18 Jan 2017 20:04:29 GMT

### Minor changes

- Updating API Extractor to work with TypeScript 2.1

## 1.0.2
Mon, 16 Jan 2017 20:04:15 GMT

### Patches

- @link capability for href and API definition references

## 1.0.1
Fri, 13 Jan 2017 06:46:05 GMT

_Version update only_

## 1.0.0
Wed, 11 Jan 2017 14:11:26 GMT

### Breaking changes

- Introducing API Extractor


{"version": 3, "file": "ValidationEnhancer.js", "sourceRoot": "", "sources": ["../../src/enhancers/ValidationEnhancer.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,2CAA6B;AAC7B,+CAAiC;AAGjC,qDAAkD;AAKlD,kEAA+D;AAC/D,wEAA4D;AAC5D,uEAAoE;AAIpE,MAAa,kBAAkB;IACtB,MAAM,CAAC,OAAO,CAAC,SAAoB;QACxC,MAAM,qBAAqB,GAAmB,IAAI,GAAG,EAAa,CAAC;QAEnE,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxC,IACE,CAAC,CACC,MAAM,CAAC,UAAU;gBACjB,SAAS,CAAC,eAAe,CAAC,gCAAgC;gBAC1D,SAAS,CAAC,eAAe,CAAC,+BAA+B,CAC1D,EACD,CAAC;gBACD,SAAS;YACX,CAAC;YAED,IAAI,MAAM,CAAC,SAAS,YAAY,qBAAS,EAAE,CAAC;gBAC1C,+BAA+B;gBAE/B,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;gBAE9C,SAAS,CAAC,2BAA2B,CAAC,CAAC,cAA8B,EAAE,EAAE;oBACvE,kBAAkB,CAAC,gBAAgB,CAAC,SAAS,EAAE,cAAc,EAAE,qBAAqB,CAAC,CAAC;gBACxF,CAAC,CAAC,CAAC;gBAEH,MAAM,cAAc,GAAmB,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;gBAChF,kBAAkB,CAAC,2BAA2B,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;gBAC7F,kBAAkB,CAAC,gCAAgC,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YAC5F,CAAC;iBAAM,IAAI,MAAM,CAAC,SAAS,YAAY,uCAAkB,EAAE,CAAC;gBAC1D,uDAAuD;gBACvD,MAAM,kBAAkB,GAAuB,MAAM,CAAC,SAAS,CAAC;gBAEhE,MAAM,mBAAmB,GACvB,kBAAkB,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;gBAEzD,KAAK,MAAM,wBAAwB,IAAI,mBAAmB,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC1F,IAAI,wBAAwB,YAAY,qBAAS,EAAE,CAAC;wBAClD,MAAM,SAAS,GAAc,wBAAwB,CAAC;wBAEtD,SAAS,CAAC,2BAA2B,CAAC,CAAC,cAA8B,EAAE,EAAE;4BACvE,kBAAkB,CAAC,gBAAgB,CAAC,SAAS,EAAE,cAAc,EAAE,qBAAqB,CAAC,CAAC;wBACxF,CAAC,CAAC,CAAC;wBAEH,MAAM,cAAc,GAAmB,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;wBAEhF,gGAAgG;wBAEhG,kBAAkB,CAAC,gCAAgC,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;oBAC5F,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,2BAA2B,CACxC,SAAoB,EACpB,eAAgC,EAChC,SAAoB,EACpB,cAA8B;QAE9B,IAAI,eAAe,GAAY,KAAK,CAAC;QAErC,IAAI,cAAc,CAAC,sBAAsB,KAAK,gCAAU,CAAC,QAAQ,EAAE,CAAC;YAClE,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;gBAC/B,8EAA8E;gBAC9E,+GAA+G;gBAC/G,EAAE;gBACF,mBAAmB;gBACnB,sBAAsB;gBACtB,EAAE;gBACF,qBAAqB;gBACrB,2BAA2B;gBAC3B,EAAE;gBACF,iGAAiG;gBACjG,eAAe,GAAG,IAAI,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,6GAA6G;gBAC7G,EAAE;gBACF,+EAA+E;gBAC/E,EAAE;gBACF,qBAAqB;gBACrB,yBAAyB;gBACzB,8BAA8B;gBAC9B,MAAM;gBACN,EAAE;gBACF,mBAAmB;gBACnB,qBAAqB;gBACrB,uBAAuB;gBACvB,oEAAoE;gBACpE,MAAM;gBACN,MAAM,oBAAoB,GAAmB,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;gBACtF,IAAI,oBAAoB,CAAC,sBAAsB,GAAG,gCAAU,CAAC,QAAQ,EAAE,CAAC;oBACtE,eAAe,GAAG,IAAI,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACpB,KAAK,MAAM,UAAU,IAAI,eAAe,CAAC,WAAW,EAAE,CAAC;gBACrD,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBAC1B,SAAS,CAAC,aAAa,CAAC,gBAAgB,CACtC,uCAAkB,CAAC,yBAAyB,EAC5C,aAAa,UAAU,yCAAyC;wBAC9D,iDAAiD,EACnD,SAAS,EACT,EAAE,UAAU,EAAE,CACf,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,gCAAgC,CAC7C,SAAoB,EACpB,SAAoB,EACpB,cAA8B;QAE9B,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACzB,oGAAoG;YACpG,mDAAmD;YACnD,OAAO;QACT,CAAC;QAED,6GAA6G;QAC7G,sCAAsC;QACtC,MAAM,2BAA2B,GAAe,cAAc,CAAC,sBAAsB,CAAC;QAEtF,+GAA+G;QAC/G,IAAI,gBAAgB,GAAY,KAAK,CAAC;QAEtC,uFAAuF;QACvF,IAAI,qBAAqB,GAAY,IAAI,CAAC;QAE1C,iEAAiE;QACjE,IAAI,sBAAsB,GAAY,KAAK,CAAC;QAE5C,KAAK,MAAM,cAAc,IAAI,SAAS,CAAC,eAAe,EAAE,CAAC;YACvD,MAAM,eAAe,GAAoB,SAAS,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YACxF,MAAM,mBAAmB,GAAe,eAAe,CAAC,mBAAmB,CAAC;YAE5E,QAAQ,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBACxC,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC;gBACvC,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB;oBAClC,MAAM;gBACR;oBACE,qBAAqB,GAAG,KAAK,CAAC;YAClC,CAAC;YAED,IAAI,mBAAmB,KAAK,2BAA2B,EAAE,CAAC;gBACxD,gBAAgB,GAAG,IAAI,CAAC;YAC1B,CAAC;YAED,IAAI,mBAAmB,KAAK,gCAAU,CAAC,QAAQ,EAAE,CAAC;gBAChD,sBAAsB,GAAG,IAAI,CAAC;YAChC,CAAC;QACH,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC3B,SAAS,CAAC,aAAa,CAAC,gBAAgB,CACtC,uCAAkB,CAAC,oBAAoB,EACvC,kEAAkE,EAClE,SAAS,CACV,CAAC;YACJ,CAAC;YAED,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,SAAS,CAAC,aAAa,CAAC,gBAAgB,CACtC,uCAAkB,CAAC,uBAAuB,EAC1C,2CAA2C,SAAS,CAAC,SAAS,mCAAmC;oBAC/F,yBAAyB,EAC3B,SAAS,CACV,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAC7B,SAAoB,EACpB,cAA8B,EAC9B,qBAAqC;QAErC,MAAM,eAAe,GAAoB,SAAS,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QACxF,MAAM,qBAAqB,GAAe,eAAe,CAAC,mBAAmB,CAAC;QAE9E,KAAK,MAAM,gBAAgB,IAAI,cAAc,CAAC,qBAAqB,EAAE,CAAC;YACpE,IAAI,eAA4C,CAAC;YACjD,IAAI,oBAAgC,CAAC;YACrC,IAAI,SAAiB,CAAC;YAEtB,IAAI,gBAAgB,YAAY,qBAAS,EAAE,CAAC;gBAC1C,kGAAkG;gBAClG,yBAAyB;gBACzB,EAAE;gBACF,kFAAkF;gBAClF,MAAM,UAAU,GAAc,gBAAgB,CAAC,aAAa,CAAC;gBAE7D,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;oBAC1B,SAAS;gBACX,CAAC;gBAED,eAAe,GAAG,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;gBAC9D,SAAS,GAAG,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,WAAW,KAAI,UAAU,CAAC,SAAS,CAAC;gBAEjE,MAAM,kBAAkB,GAAmB,SAAS,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;gBAC3F,oBAAoB,GAAG,kBAAkB,CAAC,sBAAsB,CAAC;YACnE,CAAC;iBAAM,IAAI,gBAAgB,YAAY,uCAAkB,EAAE,CAAC;gBAC1D,eAAe,GAAG,SAAS,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;gBAEpE,0FAA0F;gBAC1F,oBAAoB,GAAG,gCAAU,CAAC,MAAM,CAAC;gBAEzC,SAAS,GAAG,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,WAAW,KAAI,gBAAgB,CAAC,SAAS,CAAC;YACzE,CAAC;iBAAM,CAAC;gBACN,SAAS;YACX,CAAC;YAED,IAAI,eAAe,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;gBAClD,IAAI,gCAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC;oBACxE,SAAS,CAAC,aAAa,CAAC,gBAAgB,CACtC,uCAAkB,CAAC,uBAAuB,EAC1C,eAAe,cAAc,CAAC,SAAS,CAAC,SAAS,GAAG;wBAClD,iBAAiB,gCAAU,CAAC,UAAU,CAAC,qBAAqB,CAAC,GAAG;wBAChE,kCAAkC,SAAS,GAAG;wBAC9C,uBAAuB,gCAAU,CAAC,UAAU,CAAC,oBAAoB,CAAC,EAAE,EACtE,cAAc,CACf,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,kBAAkB,GAAW,IAAI,CAAC,QAAQ,CAC9C,SAAS,CAAC,cAAc,CAAC,oBAAoB,CAAC,QAAQ,CACvD,CAAC;gBAEF,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBACjD,qBAAqB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;oBAE5C,IACE,gBAAgB,YAAY,qBAAS;wBACrC,kBAAkB,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,EACxD,CAAC;wBACD,mGAAmG;wBACnG,oEAAoE;oBACtE,CAAC;yBAAM,CAAC;wBACN,SAAS,CAAC,aAAa,CAAC,gBAAgB,CACtC,uCAAkB,CAAC,eAAe,EAClC,eAAe,SAAS,6CAA6C,kBAAkB,EAAE,EACzF,cAAc,CACf,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,+EAA+E;IAC/E,EAAE;IACF,sDAAsD;IAC9C,MAAM,CAAC,mBAAmB,CAAC,SAAoB;QACrD,IAAI,SAAS,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oCAAoC;QACpC,EAAE;QACF,yBAAyB;QACzB,kCAAkC;QAClC,mCAAmC;QACnC,oBAAoB;QACpB,6CAA6C;QAC7C,qCAAqC;QACrC,MAAM,cAAc,GAAmB,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACpE,IAAI,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;YACzD,MAAM,gBAAgB,GAA4B,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC;YAClF,IAAI,gBAAgB,EAAE,CAAC;gBACrB,KAAK,MAAM,KAAK,IAAI,gBAAgB,CAAC,WAAW,EAAE,EAAE,CAAC;oBACnD,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;wBAC/C,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA3RD,gDA2RC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as path from 'path';\nimport * as ts from 'typescript';\n\nimport type { Collector } from '../collector/Collector';\nimport { AstSymbol } from '../analyzer/AstSymbol';\nimport type { AstDeclaration } from '../analyzer/AstDeclaration';\nimport type { ApiItemMetadata } from '../collector/ApiItemMetadata';\nimport type { SymbolMetadata } from '../collector/SymbolMetadata';\nimport type { CollectorEntity } from '../collector/CollectorEntity';\nimport { ExtractorMessageId } from '../api/ExtractorMessageId';\nimport { ReleaseTag } from '@microsoft/api-extractor-model';\nimport { AstNamespaceImport } from '../analyzer/AstNamespaceImport';\nimport type { IAstModuleExportInfo } from '../analyzer/AstModule';\nimport type { AstEntity } from '../analyzer/AstEntity';\n\nexport class ValidationEnhancer {\n  public static analyze(collector: Collector): void {\n    const alreadyWarnedEntities: Set<AstEntity> = new Set<AstEntity>();\n\n    for (const entity of collector.entities) {\n      if (\n        !(\n          entity.consumable ||\n          collector.extractorConfig.apiReportIncludeForgottenExports ||\n          collector.extractorConfig.docModelIncludeForgottenExports\n        )\n      ) {\n        continue;\n      }\n\n      if (entity.astEntity instanceof AstSymbol) {\n        // A regular exported AstSymbol\n\n        const astSymbol: AstSymbol = entity.astEntity;\n\n        astSymbol.forEachDeclarationRecursive((astDeclaration: AstDeclaration) => {\n          ValidationEnhancer._checkReferences(collector, astDeclaration, alreadyWarnedEntities);\n        });\n\n        const symbolMetadata: SymbolMetadata = collector.fetchSymbolMetadata(astSymbol);\n        ValidationEnhancer._checkForInternalUnderscore(collector, entity, astSymbol, symbolMetadata);\n        ValidationEnhancer._checkForInconsistentReleaseTags(collector, astSymbol, symbolMetadata);\n      } else if (entity.astEntity instanceof AstNamespaceImport) {\n        // A namespace created using \"import * as ___ from ___\"\n        const astNamespaceImport: AstNamespaceImport = entity.astEntity;\n\n        const astModuleExportInfo: IAstModuleExportInfo =\n          astNamespaceImport.fetchAstModuleExportInfo(collector);\n\n        for (const namespaceMemberAstEntity of astModuleExportInfo.exportedLocalEntities.values()) {\n          if (namespaceMemberAstEntity instanceof AstSymbol) {\n            const astSymbol: AstSymbol = namespaceMemberAstEntity;\n\n            astSymbol.forEachDeclarationRecursive((astDeclaration: AstDeclaration) => {\n              ValidationEnhancer._checkReferences(collector, astDeclaration, alreadyWarnedEntities);\n            });\n\n            const symbolMetadata: SymbolMetadata = collector.fetchSymbolMetadata(astSymbol);\n\n            // (Don't apply ValidationEnhancer._checkForInternalUnderscore() for AstNamespaceImport members)\n\n            ValidationEnhancer._checkForInconsistentReleaseTags(collector, astSymbol, symbolMetadata);\n          }\n        }\n      }\n    }\n  }\n\n  private static _checkForInternalUnderscore(\n    collector: Collector,\n    collectorEntity: CollectorEntity,\n    astSymbol: AstSymbol,\n    symbolMetadata: SymbolMetadata\n  ): void {\n    let needsUnderscore: boolean = false;\n\n    if (symbolMetadata.maxEffectiveReleaseTag === ReleaseTag.Internal) {\n      if (!astSymbol.parentAstSymbol) {\n        // If it's marked as @internal and has no parent, then it needs an underscore.\n        // We use maxEffectiveReleaseTag because a merged declaration would NOT need an underscore in a case like this:\n        //\n        //   /** @public */\n        //   export enum X { }\n        //\n        //   /** @internal */\n        //   export namespace X { }\n        //\n        // (The above normally reports an error \"ae-different-release-tags\", but that may be suppressed.)\n        needsUnderscore = true;\n      } else {\n        // If it's marked as @internal and the parent isn't obviously already @internal, then it needs an underscore.\n        //\n        // For example, we WOULD need an underscore for a merged declaration like this:\n        //\n        //   /** @internal */\n        //   export namespace X {\n        //     export interface _Y { }\n        //   }\n        //\n        //   /** @public */\n        //   export class X {\n        //     /** @internal */\n        //     public static _Y(): void { }   // <==== different from parent\n        //   }\n        const parentSymbolMetadata: SymbolMetadata = collector.fetchSymbolMetadata(astSymbol);\n        if (parentSymbolMetadata.maxEffectiveReleaseTag > ReleaseTag.Internal) {\n          needsUnderscore = true;\n        }\n      }\n    }\n\n    if (needsUnderscore) {\n      for (const exportName of collectorEntity.exportNames) {\n        if (exportName[0] !== '_') {\n          collector.messageRouter.addAnalyzerIssue(\n            ExtractorMessageId.InternalMissingUnderscore,\n            `The name \"${exportName}\" should be prefixed with an underscore` +\n              ` because the declaration is marked as @internal`,\n            astSymbol,\n            { exportName }\n          );\n        }\n      }\n    }\n  }\n\n  private static _checkForInconsistentReleaseTags(\n    collector: Collector,\n    astSymbol: AstSymbol,\n    symbolMetadata: SymbolMetadata\n  ): void {\n    if (astSymbol.isExternal) {\n      // For now, don't report errors for external code.  If the developer cares about it, they should run\n      // API Extractor separately on the external project\n      return;\n    }\n\n    // Normally we will expect all release tags to be the same.  Arbitrarily we choose the maxEffectiveReleaseTag\n    // as the thing they should all match.\n    const expectedEffectiveReleaseTag: ReleaseTag = symbolMetadata.maxEffectiveReleaseTag;\n\n    // This is set to true if we find a declaration whose release tag is different from expectedEffectiveReleaseTag\n    let mixedReleaseTags: boolean = false;\n\n    // This is set to false if we find a declaration that is not a function/method overload\n    let onlyFunctionOverloads: boolean = true;\n\n    // This is set to true if we find a declaration that is @internal\n    let anyInternalReleaseTags: boolean = false;\n\n    for (const astDeclaration of astSymbol.astDeclarations) {\n      const apiItemMetadata: ApiItemMetadata = collector.fetchApiItemMetadata(astDeclaration);\n      const effectiveReleaseTag: ReleaseTag = apiItemMetadata.effectiveReleaseTag;\n\n      switch (astDeclaration.declaration.kind) {\n        case ts.SyntaxKind.FunctionDeclaration:\n        case ts.SyntaxKind.MethodDeclaration:\n          break;\n        default:\n          onlyFunctionOverloads = false;\n      }\n\n      if (effectiveReleaseTag !== expectedEffectiveReleaseTag) {\n        mixedReleaseTags = true;\n      }\n\n      if (effectiveReleaseTag === ReleaseTag.Internal) {\n        anyInternalReleaseTags = true;\n      }\n    }\n\n    if (mixedReleaseTags) {\n      if (!onlyFunctionOverloads) {\n        collector.messageRouter.addAnalyzerIssue(\n          ExtractorMessageId.DifferentReleaseTags,\n          'This symbol has another declaration with a different release tag',\n          astSymbol\n        );\n      }\n\n      if (anyInternalReleaseTags) {\n        collector.messageRouter.addAnalyzerIssue(\n          ExtractorMessageId.InternalMixedReleaseTag,\n          `Mixed release tags are not allowed for \"${astSymbol.localName}\" because one of its declarations` +\n            ` is marked as @internal`,\n          astSymbol\n        );\n      }\n    }\n  }\n\n  private static _checkReferences(\n    collector: Collector,\n    astDeclaration: AstDeclaration,\n    alreadyWarnedEntities: Set<AstEntity>\n  ): void {\n    const apiItemMetadata: ApiItemMetadata = collector.fetchApiItemMetadata(astDeclaration);\n    const declarationReleaseTag: ReleaseTag = apiItemMetadata.effectiveReleaseTag;\n\n    for (const referencedEntity of astDeclaration.referencedAstEntities) {\n      let collectorEntity: CollectorEntity | undefined;\n      let referencedReleaseTag: ReleaseTag;\n      let localName: string;\n\n      if (referencedEntity instanceof AstSymbol) {\n        // If this is e.g. a member of a namespace, then we need to be checking the top-level scope to see\n        // whether it's exported.\n        //\n        // TODO: Technically we should also check each of the nested scopes along the way.\n        const rootSymbol: AstSymbol = referencedEntity.rootAstSymbol;\n\n        if (rootSymbol.isExternal) {\n          continue;\n        }\n\n        collectorEntity = collector.tryGetCollectorEntity(rootSymbol);\n        localName = collectorEntity?.nameForEmit || rootSymbol.localName;\n\n        const referencedMetadata: SymbolMetadata = collector.fetchSymbolMetadata(referencedEntity);\n        referencedReleaseTag = referencedMetadata.maxEffectiveReleaseTag;\n      } else if (referencedEntity instanceof AstNamespaceImport) {\n        collectorEntity = collector.tryGetCollectorEntity(referencedEntity);\n\n        // TODO: Currently the \"import * as ___ from ___\" syntax does not yet support doc comments\n        referencedReleaseTag = ReleaseTag.Public;\n\n        localName = collectorEntity?.nameForEmit || referencedEntity.localName;\n      } else {\n        continue;\n      }\n\n      if (collectorEntity && collectorEntity.consumable) {\n        if (ReleaseTag.compare(declarationReleaseTag, referencedReleaseTag) > 0) {\n          collector.messageRouter.addAnalyzerIssue(\n            ExtractorMessageId.IncompatibleReleaseTags,\n            `The symbol \"${astDeclaration.astSymbol.localName}\"` +\n              ` is marked as ${ReleaseTag.getTagName(declarationReleaseTag)},` +\n              ` but its signature references \"${localName}\"` +\n              ` which is marked as ${ReleaseTag.getTagName(referencedReleaseTag)}`,\n            astDeclaration\n          );\n        }\n      } else {\n        const entryPointFilename: string = path.basename(\n          collector.workingPackage.entryPointSourceFile.fileName\n        );\n\n        if (!alreadyWarnedEntities.has(referencedEntity)) {\n          alreadyWarnedEntities.add(referencedEntity);\n\n          if (\n            referencedEntity instanceof AstSymbol &&\n            ValidationEnhancer._isEcmaScriptSymbol(referencedEntity)\n          ) {\n            // The main usage scenario for ECMAScript symbols is to attach private data to a JavaScript object,\n            // so as a special case, we do NOT report them as forgotten exports.\n          } else {\n            collector.messageRouter.addAnalyzerIssue(\n              ExtractorMessageId.ForgottenExport,\n              `The symbol \"${localName}\" needs to be exported by the entry point ${entryPointFilename}`,\n              astDeclaration\n            );\n          }\n        }\n      }\n    }\n  }\n\n  // Detect an AstSymbol that refers to an ECMAScript symbol declaration such as:\n  //\n  // const mySymbol: unique symbol = Symbol('mySymbol');\n  private static _isEcmaScriptSymbol(astSymbol: AstSymbol): boolean {\n    if (astSymbol.astDeclarations.length !== 1) {\n      return false;\n    }\n\n    // We are matching a form like this:\n    //\n    // - VariableDeclaration:\n    //   - Identifier:  pre=[mySymbol]\n    //   - ColonToken:  pre=[:] sep=[ ]\n    //   - TypeOperator:\n    //     - UniqueKeyword:  pre=[unique] sep=[ ]\n    //     - SymbolKeyword:  pre=[symbol]\n    const astDeclaration: AstDeclaration = astSymbol.astDeclarations[0];\n    if (ts.isVariableDeclaration(astDeclaration.declaration)) {\n      const variableTypeNode: ts.TypeNode | undefined = astDeclaration.declaration.type;\n      if (variableTypeNode) {\n        for (const token of variableTypeNode.getChildren()) {\n          if (token.kind === ts.SyntaxKind.SymbolKeyword) {\n            return true;\n          }\n        }\n      }\n    }\n\n    return false;\n  }\n}\n"]}
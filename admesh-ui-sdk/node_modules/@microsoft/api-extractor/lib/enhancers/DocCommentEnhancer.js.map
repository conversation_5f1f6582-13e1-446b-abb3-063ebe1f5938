{"version": 3, "file": "DocCommentEnhancer.js", "sourceRoot": "", "sources": ["../../src/enhancers/DocCommentEnhancer.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+CAAiC;AACjC,wDAA0C;AAG1C,qDAAkD;AAGlD,wEAA4D;AAC5D,kEAA+D;AAC/D,4DAAyD;AACzD,2EAAmE;AAEnE,MAAa,kBAAkB;IAG7B,YAAmB,SAAoB;QACrC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAEM,MAAM,CAAC,OAAO,CAAC,SAAoB;QACxC,MAAM,kBAAkB,GAAuB,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACjF,kBAAkB,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAEM,OAAO;QACZ,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC9C,IAAI,MAAM,CAAC,SAAS,YAAY,qBAAS,EAAE,CAAC;gBAC1C,IACE,MAAM,CAAC,UAAU;oBACjB,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,gCAAgC;oBAChE,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,+BAA+B,EAC/D,CAAC;oBACD,MAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,cAA8B,EAAE,EAAE;wBAC9E,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;oBACvC,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,cAA8B;QACpD,MAAM,QAAQ,GAAoB,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QACvF,IAAI,QAAQ,CAAC,8BAA8B,KAAK,2BAAY,CAAC,OAAO,EAAE,CAAC;YACrE,OAAO;QACT,CAAC;QAED,IAAI,QAAQ,CAAC,8BAA8B,KAAK,2BAAY,CAAC,QAAQ,EAAE,CAAC;YACtE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAC5C,uCAAkB,CAAC,gBAAgB,EACnC,4BAA4B,cAAc,CAAC,SAAS,CAAC,SAAS,iCAAiC,EAC/F,cAAc,CACf,CAAC;YACF,OAAO;QACT,CAAC;QACD,QAAQ,CAAC,8BAA8B,GAAG,2BAAY,CAAC,QAAQ,CAAC;QAEhE,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YACjE,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QACpG,CAAC;QAED,IAAI,CAAC,0BAA0B,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAE1D,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAEpD,QAAQ,CAAC,8BAA8B,GAAG,2BAAY,CAAC,OAAO,CAAC;IACjE,CAAC;IAEO,0BAA0B,CAAC,cAA8B,EAAE,QAAyB;QAC1F,IAAI,cAAc,CAAC,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YAClE,iGAAiG;YACjG,gGAAgG;YAChG,0BAA0B;YAC1B,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;YAE9B,2CAA2C;YAC3C,MAAM,gBAAgB,GAAmB,cAAc,CAAC,MAAO,CAAC;YAEhE,MAAM,aAAa,GAA6B,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,kBAAkB,CAAC;YAEnG,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC3B,QAAQ,CAAC,YAAY,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC;gBACpF,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,sBAAsB,CAAC;oBAC1D,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,mCAAmC,EAAE,CAAC;oBACpF,IAAI,KAAK,CAAC,WAAW,CAAC;wBACpB,aAAa;wBACb,IAAI,EAAE,gBAAgB,CAAC,SAAS,CAAC,SAAS;qBAC3C,CAAC;oBACF,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;iBAC1D,CAAC,CAAC;YACL,CAAC;YAED,MAAM,eAAe,GAAoB,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAC9F,IAAI,eAAe,CAAC,mBAAmB,KAAK,gCAAU,CAAC,QAAQ,EAAE,CAAC;gBAChE,mGAAmG;gBACnG,MAAM,aAAa,GAAoB,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;gBAE9F,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;oBAChC,aAAa,CAAC,YAAY,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC;gBACvE,CAAC;gBAED,IAAI,aAAa,CAAC,YAAY,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;oBAC1D,aAAa,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC;wBAC3D,aAAa;wBACb,QAAQ,EAAE,IAAI,KAAK,CAAC,WAAW,CAAC;4BAC9B,aAAa;4BACb,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO;yBAC5C,CAAC;qBACH,CAAC,CAAC;gBACL,CAAC;gBAED,aAAa,CAAC,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CACxD,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,EAAE;oBACxC,IAAI,KAAK,CAAC,YAAY,CAAC;wBACrB,aAAa;wBACb,IAAI,EACF,mFAAmF;4BACnF,sEAAsE;qBACzE,CAAC;oBACF,IAAI,KAAK,CAAC,WAAW,CAAC;wBACpB,aAAa;wBACb,IAAI,EAAE,gBAAgB,CAAC,SAAS,CAAC,SAAS;qBAC3C,CAAC;oBACF,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;iBAC3D,CAAC,CACH,CAAC;YACJ,CAAC;YACD,OAAO;QACT,CAAC;aAAM,CAAC;YACN,iGAAiG;YACjG,uGAAuG;YACvG,8GAA8G;YAC9G,oDAAoD;YACpD,4GAA4G;YAC5G,6BAA6B;YAC7B,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC1B,IAAI,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC;oBACvF,sGAAsG;oBACtG,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;gBAChC,CAAC;qBAAM,IAAI,QAAQ,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;oBAC/C,IACE,IAAI,CAAC,oCAAoC,CACvC,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,oBAAoB,CACzD,EACD,CAAC;wBACD,kGAAkG;wBAClG,0GAA0G;wBAC1G,mGAAmG;wBACnG,0CAA0C;wBAC1C,4DAA4D;wBAC5D,+CAA+C;wBAC/C,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC/B,CAAC;yBAAM,CAAC;wBACN,wGAAwG;wBACxG,kHAAkH;wBAClH,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;oBAChC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,gHAAgH;oBAChH,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;gBAC/B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,4DAA4D;gBAC5D,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;YAC/B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,cAA8B,EAAE,QAAyB;QACpF,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QACD,IAAI,CAAC,6BAA6B,CAAC,cAAc,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;IAC5E,CAAC;IAEO,6BAA6B,CAAC,cAA8B,EAAE,IAAmB;QACvF,IAAI,IAAI,YAAY,KAAK,CAAC,UAAU,EAAE,CAAC;YACrC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,4FAA4F;gBAC5F,2FAA2F;gBAC3F,kEAAkE;gBAClE,IAAI,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;oBACpE,MAAM,wBAAwB,GAC5B,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBAErE,IAAI,wBAAwB,YAAY,sCAAe,EAAE,CAAC;wBACxD,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAC5C,uCAAkB,CAAC,cAAc,EACjC,6CAA6C,GAAG,wBAAwB,CAAC,MAAM,EAC/E,cAAc,CACf,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,6BAA6B,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CACtB,cAA8B,EAC9B,UAA4B,EAC5B,aAAqC;QAErC,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;YACxC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAC5C,uCAAkB,CAAC,wBAAwB,EAC3C,kGAAkG,EAClG,cAAc,CACf,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,oCAAoC,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACnF,wGAAwG;YACxG,uFAAuF;YACvF,kEAAkE;YAClE,OAAO;QACT,CAAC;QAED,MAAM,wBAAwB,GAC5B,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAEnF,IAAI,wBAAwB,YAAY,sCAAe,EAAE,CAAC;YACxD,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAC5C,uCAAkB,CAAC,6BAA6B,EAChD,mDAAmD,GAAG,wBAAwB,CAAC,MAAM,EACrF,cAAc,CACf,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC;QAE/C,MAAM,kBAAkB,GACtB,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;QAEjE,IAAI,kBAAkB,CAAC,YAAY,EAAE,CAAC;YACpC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,gBAAkC,EAAE,gBAAkC;QAC/F,gBAAgB,CAAC,cAAc,GAAG,gBAAgB,CAAC,cAAc,CAAC;QAClE,gBAAgB,CAAC,YAAY,GAAG,gBAAgB,CAAC,YAAY,CAAC;QAE9D,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAChC,KAAK,MAAM,KAAK,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC5C,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;QACD,KAAK,MAAM,SAAS,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;YACpD,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC;QACD,gBAAgB,CAAC,YAAY,GAAG,gBAAgB,CAAC,YAAY,CAAC;QAE9D,gBAAgB,CAAC,aAAa,GAAG,SAAS,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,oCAAoC,CAC1C,oBAA+D;QAE/D,OAAO,CACL,CAAA,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,WAAW,MAAK,SAAS;YAC/C,oBAAoB,CAAC,WAAW,KAAK,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CACzE,CAAC;IACJ,CAAC;CACF;AA1QD,gDA0QC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as ts from 'typescript';\nimport * as tsdoc from '@microsoft/tsdoc';\n\nimport type { Collector } from '../collector/Collector';\nimport { AstSymbol } from '../analyzer/AstSymbol';\nimport type { AstDeclaration } from '../analyzer/AstDeclaration';\nimport type { ApiItemMetadata } from '../collector/ApiItemMetadata';\nimport { ReleaseTag } from '@microsoft/api-extractor-model';\nimport { ExtractorMessageId } from '../api/ExtractorMessageId';\nimport { VisitorState } from '../collector/VisitorState';\nimport { ResolverFailure } from '../analyzer/AstReferenceResolver';\n\nexport class DocCommentEnhancer {\n  private readonly _collector: Collector;\n\n  public constructor(collector: Collector) {\n    this._collector = collector;\n  }\n\n  public static analyze(collector: Collector): void {\n    const docCommentEnhancer: DocCommentEnhancer = new DocCommentEnhancer(collector);\n    docCommentEnhancer.analyze();\n  }\n\n  public analyze(): void {\n    for (const entity of this._collector.entities) {\n      if (entity.astEntity instanceof AstSymbol) {\n        if (\n          entity.consumable ||\n          this._collector.extractorConfig.apiReportIncludeForgottenExports ||\n          this._collector.extractorConfig.docModelIncludeForgottenExports\n        ) {\n          entity.astEntity.forEachDeclarationRecursive((astDeclaration: AstDeclaration) => {\n            this._analyzeApiItem(astDeclaration);\n          });\n        }\n      }\n    }\n  }\n\n  private _analyzeApiItem(astDeclaration: AstDeclaration): void {\n    const metadata: ApiItemMetadata = this._collector.fetchApiItemMetadata(astDeclaration);\n    if (metadata.docCommentEnhancerVisitorState === VisitorState.Visited) {\n      return;\n    }\n\n    if (metadata.docCommentEnhancerVisitorState === VisitorState.Visiting) {\n      this._collector.messageRouter.addAnalyzerIssue(\n        ExtractorMessageId.CyclicInheritDoc,\n        `The @inheritDoc tag for \"${astDeclaration.astSymbol.localName}\" refers to its own declaration`,\n        astDeclaration\n      );\n      return;\n    }\n    metadata.docCommentEnhancerVisitorState = VisitorState.Visiting;\n\n    if (metadata.tsdocComment && metadata.tsdocComment.inheritDocTag) {\n      this._applyInheritDoc(astDeclaration, metadata.tsdocComment, metadata.tsdocComment.inheritDocTag);\n    }\n\n    this._analyzeNeedsDocumentation(astDeclaration, metadata);\n\n    this._checkForBrokenLinks(astDeclaration, metadata);\n\n    metadata.docCommentEnhancerVisitorState = VisitorState.Visited;\n  }\n\n  private _analyzeNeedsDocumentation(astDeclaration: AstDeclaration, metadata: ApiItemMetadata): void {\n    if (astDeclaration.declaration.kind === ts.SyntaxKind.Constructor) {\n      // Constructors always do pretty much the same thing, so it's annoying to require people to write\n      // descriptions for them.  Instead, if the constructor lacks a TSDoc summary, then API Extractor\n      // will auto-generate one.\n      metadata.undocumented = false;\n\n      // The class that contains this constructor\n      const classDeclaration: AstDeclaration = astDeclaration.parent!;\n\n      const configuration: tsdoc.TSDocConfiguration = this._collector.extractorConfig.tsdocConfiguration;\n\n      if (!metadata.tsdocComment) {\n        metadata.tsdocComment = new tsdoc.DocComment({ configuration });\n      }\n\n      if (!tsdoc.PlainTextEmitter.hasAnyTextContent(metadata.tsdocComment.summarySection)) {\n        metadata.tsdocComment.summarySection.appendNodesInParagraph([\n          new tsdoc.DocPlainText({ configuration, text: 'Constructs a new instance of the ' }),\n          new tsdoc.DocCodeSpan({\n            configuration,\n            code: classDeclaration.astSymbol.localName\n          }),\n          new tsdoc.DocPlainText({ configuration, text: ' class' })\n        ]);\n      }\n\n      const apiItemMetadata: ApiItemMetadata = this._collector.fetchApiItemMetadata(astDeclaration);\n      if (apiItemMetadata.effectiveReleaseTag === ReleaseTag.Internal) {\n        // If the constructor is marked as internal, then add a boilerplate notice for the containing class\n        const classMetadata: ApiItemMetadata = this._collector.fetchApiItemMetadata(classDeclaration);\n\n        if (!classMetadata.tsdocComment) {\n          classMetadata.tsdocComment = new tsdoc.DocComment({ configuration });\n        }\n\n        if (classMetadata.tsdocComment.remarksBlock === undefined) {\n          classMetadata.tsdocComment.remarksBlock = new tsdoc.DocBlock({\n            configuration,\n            blockTag: new tsdoc.DocBlockTag({\n              configuration,\n              tagName: tsdoc.StandardTags.remarks.tagName\n            })\n          });\n        }\n\n        classMetadata.tsdocComment.remarksBlock.content.appendNode(\n          new tsdoc.DocParagraph({ configuration }, [\n            new tsdoc.DocPlainText({\n              configuration,\n              text:\n                `The constructor for this class is marked as internal. Third-party code should not` +\n                ` call the constructor directly or create subclasses that extend the `\n            }),\n            new tsdoc.DocCodeSpan({\n              configuration,\n              code: classDeclaration.astSymbol.localName\n            }),\n            new tsdoc.DocPlainText({ configuration, text: ' class.' })\n          ])\n        );\n      }\n      return;\n    } else {\n      // For non-constructor items, we will determine whether or not the item is documented as follows:\n      // 1. If it contains a summary section with at least 10 characters, then it is considered \"documented\".\n      // 2. If it contains an @inheritDoc tag, then it *may* be considered \"documented\", depending on whether or not\n      //    the tag resolves to a \"documented\" API member.\n      //    - Note: for external members, we cannot currently determine this, so we will consider the \"documented\"\n      //      status to be unknown.\n      if (metadata.tsdocComment) {\n        if (tsdoc.PlainTextEmitter.hasAnyTextContent(metadata.tsdocComment.summarySection, 10)) {\n          // If the API item has a summary comment block (with at least 10 characters), mark it as \"documented\".\n          metadata.undocumented = false;\n        } else if (metadata.tsdocComment.inheritDocTag) {\n          if (\n            this._refersToDeclarationInWorkingPackage(\n              metadata.tsdocComment.inheritDocTag.declarationReference\n            )\n          ) {\n            // If the API item has an `@inheritDoc` comment that points to an API item in the working package,\n            // then the documentation contents should have already been copied from the target via `_applyInheritDoc`.\n            // The continued existence of the tag indicates that the declaration reference was invalid, and not\n            // documentation contents could be copied.\n            // An analyzer issue will have already been logged for this.\n            // We will treat such an API as \"undocumented\".\n            metadata.undocumented = true;\n          } else {\n            // If the API item has an `@inheritDoc` comment that points to an external API item, we cannot currently\n            // determine whether or not the target is \"documented\", so we cannot say definitively that this is \"undocumented\".\n            metadata.undocumented = false;\n          }\n        } else {\n          // If the API item has neither a summary comment block, nor an `@inheritDoc` comment, mark it as \"undocumented\".\n          metadata.undocumented = true;\n        }\n      } else {\n        // If there is no tsdoc comment at all, mark \"undocumented\".\n        metadata.undocumented = true;\n      }\n    }\n  }\n\n  private _checkForBrokenLinks(astDeclaration: AstDeclaration, metadata: ApiItemMetadata): void {\n    if (!metadata.tsdocComment) {\n      return;\n    }\n    this._checkForBrokenLinksRecursive(astDeclaration, metadata.tsdocComment);\n  }\n\n  private _checkForBrokenLinksRecursive(astDeclaration: AstDeclaration, node: tsdoc.DocNode): void {\n    if (node instanceof tsdoc.DocLinkTag) {\n      if (node.codeDestination) {\n        // Is it referring to the working package?  If not, we don't do any link validation, because\n        // AstReferenceResolver doesn't support it yet (but ModelReferenceResolver does of course).\n        // Tracked by:  https://github.com/microsoft/rushstack/issues/1195\n        if (this._refersToDeclarationInWorkingPackage(node.codeDestination)) {\n          const referencedAstDeclaration: AstDeclaration | ResolverFailure =\n            this._collector.astReferenceResolver.resolve(node.codeDestination);\n\n          if (referencedAstDeclaration instanceof ResolverFailure) {\n            this._collector.messageRouter.addAnalyzerIssue(\n              ExtractorMessageId.UnresolvedLink,\n              'The @link reference could not be resolved: ' + referencedAstDeclaration.reason,\n              astDeclaration\n            );\n          }\n        }\n      }\n    }\n    for (const childNode of node.getChildNodes()) {\n      this._checkForBrokenLinksRecursive(astDeclaration, childNode);\n    }\n  }\n\n  /**\n   * Follow an `{@inheritDoc ___}` reference and copy the content that we find in the referenced comment.\n   */\n  private _applyInheritDoc(\n    astDeclaration: AstDeclaration,\n    docComment: tsdoc.DocComment,\n    inheritDocTag: tsdoc.DocInheritDocTag\n  ): void {\n    if (!inheritDocTag.declarationReference) {\n      this._collector.messageRouter.addAnalyzerIssue(\n        ExtractorMessageId.UnresolvedInheritDocBase,\n        'The @inheritDoc tag needs a TSDoc declaration reference; signature matching is not supported yet',\n        astDeclaration\n      );\n      return;\n    }\n\n    if (!this._refersToDeclarationInWorkingPackage(inheritDocTag.declarationReference)) {\n      // The `@inheritDoc` tag is referencing an external package. Skip it, since AstReferenceResolver doesn't\n      // support it yet.  As a workaround, this tag will get handled later by api-documenter.\n      // Tracked by:  https://github.com/microsoft/rushstack/issues/1195\n      return;\n    }\n\n    const referencedAstDeclaration: AstDeclaration | ResolverFailure =\n      this._collector.astReferenceResolver.resolve(inheritDocTag.declarationReference);\n\n    if (referencedAstDeclaration instanceof ResolverFailure) {\n      this._collector.messageRouter.addAnalyzerIssue(\n        ExtractorMessageId.UnresolvedInheritDocReference,\n        'The @inheritDoc reference could not be resolved: ' + referencedAstDeclaration.reason,\n        astDeclaration\n      );\n      return;\n    }\n\n    this._analyzeApiItem(referencedAstDeclaration);\n\n    const referencedMetadata: ApiItemMetadata =\n      this._collector.fetchApiItemMetadata(referencedAstDeclaration);\n\n    if (referencedMetadata.tsdocComment) {\n      this._copyInheritedDocs(docComment, referencedMetadata.tsdocComment);\n    }\n  }\n\n  /**\n   * Copy the content from `sourceDocComment` to `targetDocComment`.\n   */\n  private _copyInheritedDocs(targetDocComment: tsdoc.DocComment, sourceDocComment: tsdoc.DocComment): void {\n    targetDocComment.summarySection = sourceDocComment.summarySection;\n    targetDocComment.remarksBlock = sourceDocComment.remarksBlock;\n\n    targetDocComment.params.clear();\n    for (const param of sourceDocComment.params) {\n      targetDocComment.params.add(param);\n    }\n    for (const typeParam of sourceDocComment.typeParams) {\n      targetDocComment.typeParams.add(typeParam);\n    }\n    targetDocComment.returnsBlock = sourceDocComment.returnsBlock;\n\n    targetDocComment.inheritDocTag = undefined;\n  }\n\n  /**\n   * Determines whether or not the provided declaration reference points to an item in the working package.\n   */\n  private _refersToDeclarationInWorkingPackage(\n    declarationReference: tsdoc.DocDeclarationReference | undefined\n  ): boolean {\n    return (\n      declarationReference?.packageName === undefined ||\n      declarationReference.packageName === this._collector.workingPackage.name\n    );\n  }\n}\n"]}
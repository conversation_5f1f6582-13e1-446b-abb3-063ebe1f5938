import type { Collector } from '../collector/Collector';
export declare class DocCommentEnhancer {
    private readonly _collector;
    constructor(collector: Collector);
    static analyze(collector: Collector): void;
    analyze(): void;
    private _analyzeApiItem;
    private _analyzeNeedsDocumentation;
    private _checkForBrokenLinks;
    private _checkForBrokenLinksRecursive;
    /**
     * Follow an `{@inheritDoc ___}` reference and copy the content that we find in the referenced comment.
     */
    private _applyInheritDoc;
    /**
     * Copy the content from `sourceDocComment` to `targetDocComment`.
     */
    private _copyInheritedDocs;
    /**
     * Determines whether or not the provided declaration reference points to an item in the working package.
     */
    private _refersToDeclarationInWorkingPackage;
}
//# sourceMappingURL=DocCommentEnhancer.d.ts.map
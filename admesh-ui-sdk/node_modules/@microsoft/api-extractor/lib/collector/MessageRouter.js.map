{"version": 3, "file": "MessageRouter.js", "sourceRoot": "", "sources": ["../../src/collector/MessageRouter.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+CAAiC;AAEjC,oEAAmE;AACnE,kDAA+C;AAE/C,+DAA4D;AAE5D,8DAKiC;AACjC,kEAA4F;AAG5F,gEAA6D;AAC7D,8DAA2D;AAwB3D,MAAa,aAAa;IA0CxB,YAAmB,OAA8B;QAzBjD,yEAAyE;QACjE,8BAAyB,GAAgC,IAAI,GAAG,EAA0B,CAAC;QAC3F,yBAAoB,GAAmB;YAC7C,QAAQ,EAAE,qCAAiB,CAAC,IAAI;YAChC,kBAAkB,EAAE,KAAK;SAC1B,CAAC;QACM,0BAAqB,GAAmB;YAC9C,QAAQ,EAAE,qCAAiB,CAAC,IAAI;YAChC,kBAAkB,EAAE,KAAK;SAC1B,CAAC;QACM,sBAAiB,GAAmB,EAAE,QAAQ,EAAE,qCAAiB,CAAC,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC;QAErG,eAAU,GAAW,CAAC,CAAC;QACvB,iBAAY,GAAW,CAAC,CAAC;QAa9B,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAC1D,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,CAAC;QAEhD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,oCAAoC,GAAG,IAAI,GAAG,EAAsC,CAAC;QAC1F,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;QAC1C,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAEtD,8CAA8C;QAC9C,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,eAAe,CAAC;QAClF,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAE/C,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,cAAwC;QACnE,IAAI,cAAc,CAAC,wBAAwB,EAAE,CAAC;YAC5C,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE,CAAC;gBAC5F,MAAM,aAAa,GAAmB,aAAa,CAAC,kBAAkB,CACpE,cAAc,CAAC,wBAAwB,CAAC,SAAS,CAAC,CACnD,CAAC;gBAEF,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC5B,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC;gBAC5C,CAAC;qBAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBACzC,MAAM,IAAI,KAAK,CACb,qFAAqF;wBACnF,sBAAsB,SAAS,0DAA0D,CAC5F,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,cAAc,CAAC,yBAAyB,EAAE,CAAC;YAC7C,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,yBAAyB,CAAC,EAAE,CAAC;gBAC7F,MAAM,aAAa,GAAmB,aAAa,CAAC,kBAAkB,CACpE,cAAc,CAAC,yBAAyB,CAAC,SAAS,CAAC,CACpD,CAAC;gBAEF,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC5B,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC;gBAC7C,CAAC;qBAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBACnC,MAAM,IAAI,KAAK,CACb,sFAAsF;wBACpF,sBAAsB,SAAS,kDAAkD,CACpF,CAAC;gBACJ,CAAC;qBAAM,IAAI,CAAC,2CAAsB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClD,MAAM,IAAI,KAAK,CACb,sFAAsF;wBACpF,gCAAgC,SAAS,8BAA8B,CAC1E,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,cAAc,CAAC,qBAAqB,EAAE,CAAC;YACzC,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,qBAAqB,CAAC,EAAE,CAAC;gBACzF,MAAM,aAAa,GAAmB,aAAa,CAAC,kBAAkB,CACpE,cAAc,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAChD,CAAC;gBAEF,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC5B,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC;gBACzC,CAAC;qBAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBACtC,MAAM,IAAI,KAAK,CACb,kFAAkF;wBAChF,sBAAsB,SAAS,qDAAqD,CACvF,CAAC;gBACJ,CAAC;qBAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;oBACjE,MAAM,IAAI,KAAK,CACb,kFAAkF;wBAChF,gCAAgC,SAAS,8BAA8B,CAC1E,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,IAAiC;QACjE,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,MAAM;YACjC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,IAAI,KAAK;SACrD,CAAC;IACJ,CAAC;IAED,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,UAAyB;QACpD,QAAQ,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC5B,KAAK,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;YACtC,KAAK,EAAE,CAAC,kBAAkB,CAAC,OAAO;gBAChC,OAAO,CAAC,eAAe;QAC3B,CAAC;QAED,MAAM,WAAW,GAAW,EAAE,CAAC,4BAA4B,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAC1F,MAAM,OAAO,GAA6B;YACxC,QAAQ,EAAE,2CAAwB,CAAC,QAAQ;YAC3C,SAAS,EAAE,KAAK,UAAU,CAAC,IAAI,EAAE;YACjC,IAAI,EAAE,WAAW;SAClB,CAAC;QAEF,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;YACpB,6EAA6E;YAC7E,4CAA4C;YAC5C,MAAM,UAAU,GAAkB,UAAU,CAAC,IAAI,CAAC;YAClD,MAAM,cAAc,GAAoB,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;gBAC3E,UAAU;gBACV,GAAG,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;gBAC1B,cAAc,EAAE,IAAI;aACrB,CAAC,CAAC;YACH,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;YACvD,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;YACvD,OAAO,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,mCAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,gBAAgB,CACrB,SAA6B,EAC7B,WAAmB,EACnB,sBAAkD,EAClD,UAAwC;QAExC,IAAI,cAA8B,CAAC;QACnC,IAAI,sBAAsB,YAAY,+BAAc,EAAE,CAAC;YACrD,cAAc,GAAG,sBAAsB,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,cAAc,GAAG,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,gBAAgB,GAAqB,IAAI,CAAC,2BAA2B,CACzE,SAAS,EACT,WAAW,EACX,cAAc,CAAC,WAAW,CAAC,aAAa,EAAE,EAC1C,cAAc,CAAC,WAAW,CAAC,QAAQ,EAAE,EACrC,UAAU,CACX,CAAC;QAEF,IAAI,CAAC,mCAAmC,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACI,gBAAgB,CACrB,aAAkC,EAClC,UAAyB,EACzB,cAA+B;QAE/B,KAAK,MAAM,OAAO,IAAI,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACjD,MAAM,OAAO,GAA6B;gBACxC,QAAQ,EAAE,2CAAwB,CAAC,KAAK;gBACxC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,IAAI,EAAE,OAAO,CAAC,eAAe;aAC9B,CAAC;YAEF,MAAM,cAAc,GAAoB,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;gBAC3E,UAAU;gBACV,GAAG,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG;aAC3B,CAAC,CAAC;YACH,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;YACvD,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;YACvD,OAAO,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;YAE3D,MAAM,gBAAgB,GAAqB,IAAI,mCAAgB,CAAC,OAAO,CAAC,CAAC;YAEzE,IAAI,cAAc,EAAE,CAAC;gBACnB,IAAI,CAAC,mCAAmC,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,8DAA8D;IACvD,MAAM,CAAC,mBAAmB,CAAC,KAAU,EAAE,OAAqC;QACjF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;QAED,MAAM,cAAc,GAAgB,IAAI,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAEpE,OAAO,aAAa,CAAC,oBAAoB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IACnE,CAAC;IAED,8DAA8D;IACtD,MAAM,CAAC,oBAAoB,CAAC,KAAU,EAAE,cAA2B;QACzE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,CAAC,sCAAsC;QACrD,CAAC;QAED,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,SAAS,CAAC;YACf,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ;gBACX,OAAO,KAAK,CAAC;YACf,KAAK,QAAQ;gBACX,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzB,8DAA8D;oBAC9D,MAAM,WAAW,GAAU,EAAE,CAAC;oBAC9B,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;wBAC5B,8DAA8D;wBAC9D,MAAM,iBAAiB,GAAQ,aAAa,CAAC,oBAAoB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;wBAC3F,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;4BACpC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC;oBACD,OAAO,WAAW,CAAC;gBACrB,CAAC;gBAED,MAAM,YAAY,GAAW,EAAE,CAAC;gBAChC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;oBACpD,IAAI,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;wBAC5B,SAAS;oBACX,CAAC;oBAED,8DAA8D;oBAC9D,MAAM,KAAK,GAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;oBAE9B,8DAA8D;oBAC9D,MAAM,eAAe,GAAQ,aAAa,CAAC,oBAAoB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;oBAEvF,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;wBAClC,8DAA8D;wBAC7D,YAAoB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;oBAC/C,CAAC;gBACH,CAAC;gBACD,OAAO,YAAY,CAAC;QACxB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,mCAAmC,CACzC,gBAAkC,EAClC,cAA8B;QAE9B,IAAI,kBAAkB,GACpB,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEhE,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,kBAAkB,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;QACpF,CAAC;QACD,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,2BAA2B,CAChC,SAA6B,EAC7B,WAAmB,EACnB,UAAyB,EACzB,GAAW,EACX,UAAwC;QAExC,MAAM,OAAO,GAA6B;YACxC,QAAQ,EAAE,2CAAwB,CAAC,SAAS;YAC5C,SAAS;YACT,IAAI,EAAE,WAAW;YACjB,UAAU;SACX,CAAC;QAEF,MAAM,cAAc,GAAoB,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;YAC3E,UAAU;YACV,GAAG;SACJ,CAAC,CAAC;QACH,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;QACvD,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;QACvD,OAAO,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;QAE3D,MAAM,gBAAgB,GAAqB,IAAI,mCAAgB,CAAC,OAAO,CAAC,CAAC;QAEzE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACtC,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,oCAAoC,CAAC,cAA8B;QACxE,MAAM,wBAAwB,GAAuB,EAAE,CAAC;QAExD,MAAM,kBAAkB,GACtB,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QACtE,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE,CAAC;YACnD,kEAAkE;YAClE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBAC/B,gEAAgE;gBAChE,MAAM,aAAa,GAAmB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;gBACjF,IAAI,aAAa,CAAC,kBAAkB,EAAE,CAAC;oBACrC,2EAA2E;oBAC3E,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBACjD,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC;gBACnC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,CAAC;QACtD,OAAO,wBAAwB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,sCAAsC;QAC3C,MAAM,wBAAwB,GAAuB,EAAE,CAAC;QAExD,KAAK,MAAM,mBAAmB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChD,kEAAkE;YAClE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;gBACjC,gEAAgE;gBAChE,MAAM,aAAa,GAAmB,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;gBACnF,IAAI,aAAa,CAAC,kBAAkB,EAAE,CAAC;oBACrC,2EAA2E;oBAC3E,wBAAwB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;oBACnD,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC;gBACrC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,CAAC;QACtD,OAAO,wBAAwB,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,iCAAiC;QACtC,MAAM,iBAAiB,GAAuB,EAAE,CAAC;QAEjD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpC,kDAAkD;YAClD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBACrB,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,CAAC;QAE/C,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;YACxC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAEM,QAAQ,CACb,SAA2B,EAC3B,OAAe,EACf,UAAwC;QAExC,IAAI,CAAC,cAAc,CACjB,IAAI,mCAAgB,CAAC;YACnB,QAAQ,EAAE,2CAAwB,CAAC,OAAO;YAC1C,SAAS;YACT,IAAI,EAAE,OAAO;YACb,UAAU;YACV,QAAQ,EAAE,qCAAiB,CAAC,KAAK;SAClC,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,UAAU,CACf,SAA2B,EAC3B,OAAe,EACf,UAAwC;QAExC,IAAI,CAAC,cAAc,CACjB,IAAI,mCAAgB,CAAC;YACnB,QAAQ,EAAE,2CAAwB,CAAC,OAAO;YAC1C,SAAS;YACT,IAAI,EAAE,OAAO;YACb,UAAU;YACV,QAAQ,EAAE,qCAAiB,CAAC,OAAO;SACpC,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,OAAO,CACZ,SAA2B,EAC3B,OAAe,EACf,UAAwC;QAExC,IAAI,CAAC,cAAc,CACjB,IAAI,mCAAgB,CAAC;YACnB,QAAQ,EAAE,2CAAwB,CAAC,OAAO;YAC1C,SAAS;YACT,IAAI,EAAE,OAAO;YACb,UAAU;YACV,QAAQ,EAAE,qCAAiB,CAAC,IAAI;SACjC,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,UAAU,CACf,SAA2B,EAC3B,OAAe,EACf,UAAwC;QAExC,IAAI,CAAC,cAAc,CACjB,IAAI,mCAAgB,CAAC;YACnB,QAAQ,EAAE,2CAAwB,CAAC,OAAO;YAC1C,SAAS;YACT,IAAI,EAAE,OAAO;YACb,UAAU;YACV,QAAQ,EAAE,qCAAiB,CAAC,OAAO;SACpC,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,mBAAmB,CAAC,KAAa;QACtC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IACrD,CAAC;IAEM,mBAAmB;QACxB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC;IAC5D,CAAC;IAEM,aAAa,CAAC,OAAe;QAClC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,UAAU,CAAC,mCAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAyB;QAC9C,uFAAuF;QACvF,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,iFAAiF;QACjF,IAAI,OAAO,CAAC,QAAQ,KAAK,2CAAwB,CAAC,OAAO,EAAE,CAAC;YAC1D,4FAA4F;QAC9F,CAAC;aAAM,CAAC;YACN,MAAM,aAAa,GAAmB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACvE,OAAO,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;QAC5C,CAAC;QAED,uEAAuE;QACvE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,wBAAwB;QACxB,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,qCAAiB,CAAC,KAAK;gBAC1B,EAAE,IAAI,CAAC,UAAU,CAAC;gBAClB,MAAM;YACR,KAAK,qCAAiB,CAAC,OAAO;gBAC5B,EAAE,IAAI,CAAC,YAAY,CAAC;gBACpB,MAAM;QACV,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,8EAA8E;QAC9E,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAEvB,IAAI,OAAO,CAAC,QAAQ,KAAK,qCAAiB,CAAC,IAAI,EAAE,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAI,WAAmB,CAAC;QACxB,IAAI,OAAO,CAAC,QAAQ,KAAK,2CAAwB,CAAC,OAAO,EAAE,CAAC;YAC1D,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9E,CAAC;QAED,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,qCAAiB,CAAC,KAAK;gBAC1B,OAAO,CAAC,KAAK,CAAC,mBAAQ,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC;gBACrD,MAAM;YACR,KAAK,qCAAiB,CAAC,OAAO;gBAC5B,OAAO,CAAC,IAAI,CAAC,mBAAQ,CAAC,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC;gBACzD,MAAM;YACR,KAAK,qCAAiB,CAAC,IAAI;gBACzB,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,qCAAiB,CAAC,OAAO;gBAC5B,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC7B,OAAO,CAAC,GAAG,CAAC,mBAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC1C,CAAC;gBACD,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAAyB;QAClD,MAAM,aAAa,GAA+B,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACxG,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,aAAa,CAAC;QACvB,CAAC;QACD,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,2CAAwB,CAAC,QAAQ;gBACpC,OAAO,IAAI,CAAC,oBAAoB,CAAC;YACnC,KAAK,2CAAwB,CAAC,SAAS;gBACrC,OAAO,IAAI,CAAC,qBAAqB,CAAC;YACpC,KAAK,2CAAwB,CAAC,KAAK;gBACjC,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC,KAAK,2CAAwB,CAAC,OAAO;gBACnC,MAAM,IAAI,iCAAa,CAAC,uEAAuE,CAAC,CAAC;QACrG,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,QAA4B;QACzD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACrB,IAAI,IAAY,CAAC;YACjB,0BAA0B;YAC1B,IAAI,GAAG,wBAAI,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC;YAC/D,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC;YACd,CAAC;YACD,2BAA2B;YAC3B,IAAI,GAAG,wBAAI,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC;YAC/D,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC;YACd,CAAC;YACD,yBAAyB;YACzB,OAAO,wBAAI,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC;;AApmBH,sCAqmBC;AApmBwB,8BAAgB,GACrC,8DAA8D,AADzB,CAC0B", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as ts from 'typescript';\nimport type * as tsdoc from '@microsoft/tsdoc';\nimport { Sort, InternalError } from '@rushstack/node-core-library';\nimport { Colorize } from '@rushstack/terminal';\n\nimport { AstDeclaration } from '../analyzer/AstDeclaration';\nimport type { AstSymbol } from '../analyzer/AstSymbol';\nimport {\n  ExtractorMessage,\n  ExtractorMessageCategory,\n  type IExtractorMessageOptions,\n  type IExtractorMessageProperties\n} from '../api/ExtractorMessage';\nimport { type ExtractorMessageId, allExtractorMessageIds } from '../api/ExtractorMessageId';\nimport type { IExtractorMessagesConfig, IConfigMessageReportingRule } from '../api/IConfigFile';\nimport type { ISourceLocation, SourceMapper } from './SourceMapper';\nimport { ExtractorLogLevel } from '../api/ExtractorLogLevel';\nimport { ConsoleMessageId } from '../api/ConsoleMessageId';\n\ninterface IReportingRule {\n  logLevel: ExtractorLogLevel;\n  addToApiReportFile: boolean;\n}\n\nexport interface IMessageRouterOptions {\n  workingPackageFolder: string | undefined;\n  messageCallback: ((message: ExtractorMessage) => void) | undefined;\n  messagesConfig: IExtractorMessagesConfig;\n  showVerboseMessages: boolean;\n  showDiagnostics: boolean;\n  tsdocConfiguration: tsdoc.TSDocConfiguration;\n  sourceMapper: SourceMapper;\n}\n\nexport interface IBuildJsonDumpObjectOptions {\n  /**\n   * {@link MessageRouter.buildJsonDumpObject} will omit any objects keys with these names.\n   */\n  keyNamesToOmit?: string[];\n}\n\nexport class MessageRouter {\n  public static readonly DIAGNOSTICS_LINE: string =\n    '============================================================';\n\n  private readonly _workingPackageFolder: string | undefined;\n  private readonly _messageCallback: ((message: ExtractorMessage) => void) | undefined;\n\n  // All messages\n  private readonly _messages: ExtractorMessage[];\n\n  // For each AstDeclaration, the messages associated with it.  This is used when addToApiReportFile=true\n  private readonly _associatedMessagesForAstDeclaration: Map<AstDeclaration, ExtractorMessage[]>;\n\n  private readonly _sourceMapper: SourceMapper;\n\n  private readonly _tsdocConfiguration: tsdoc.TSDocConfiguration;\n\n  // Normalized representation of the routing rules from api-extractor.json\n  private _reportingRuleByMessageId: Map<string, IReportingRule> = new Map<string, IReportingRule>();\n  private _compilerDefaultRule: IReportingRule = {\n    logLevel: ExtractorLogLevel.None,\n    addToApiReportFile: false\n  };\n  private _extractorDefaultRule: IReportingRule = {\n    logLevel: ExtractorLogLevel.None,\n    addToApiReportFile: false\n  };\n  private _tsdocDefaultRule: IReportingRule = { logLevel: ExtractorLogLevel.None, addToApiReportFile: false };\n\n  public errorCount: number = 0;\n  public warningCount: number = 0;\n\n  /**\n   * See {@link IExtractorInvokeOptions.showVerboseMessages}\n   */\n  public readonly showVerboseMessages: boolean;\n\n  /**\n   * See {@link IExtractorInvokeOptions.showDiagnostics}\n   */\n  public readonly showDiagnostics: boolean;\n\n  public constructor(options: IMessageRouterOptions) {\n    this._workingPackageFolder = options.workingPackageFolder;\n    this._messageCallback = options.messageCallback;\n\n    this._messages = [];\n    this._associatedMessagesForAstDeclaration = new Map<AstDeclaration, ExtractorMessage[]>();\n    this._sourceMapper = options.sourceMapper;\n    this._tsdocConfiguration = options.tsdocConfiguration;\n\n    // showDiagnostics implies showVerboseMessages\n    this.showVerboseMessages = options.showVerboseMessages || options.showDiagnostics;\n    this.showDiagnostics = options.showDiagnostics;\n\n    this._applyMessagesConfig(options.messagesConfig);\n  }\n\n  /**\n   * Read the api-extractor.json configuration and build up the tables of routing rules.\n   */\n  private _applyMessagesConfig(messagesConfig: IExtractorMessagesConfig): void {\n    if (messagesConfig.compilerMessageReporting) {\n      for (const messageId of Object.getOwnPropertyNames(messagesConfig.compilerMessageReporting)) {\n        const reportingRule: IReportingRule = MessageRouter._getNormalizedRule(\n          messagesConfig.compilerMessageReporting[messageId]\n        );\n\n        if (messageId === 'default') {\n          this._compilerDefaultRule = reportingRule;\n        } else if (!/^TS[0-9]+$/.test(messageId)) {\n          throw new Error(\n            `Error in API Extractor config: The messages.compilerMessageReporting table contains` +\n              ` an invalid entry \"${messageId}\". The identifier format is \"TS\" followed by an integer.`\n          );\n        } else {\n          this._reportingRuleByMessageId.set(messageId, reportingRule);\n        }\n      }\n    }\n\n    if (messagesConfig.extractorMessageReporting) {\n      for (const messageId of Object.getOwnPropertyNames(messagesConfig.extractorMessageReporting)) {\n        const reportingRule: IReportingRule = MessageRouter._getNormalizedRule(\n          messagesConfig.extractorMessageReporting[messageId]\n        );\n\n        if (messageId === 'default') {\n          this._extractorDefaultRule = reportingRule;\n        } else if (!/^ae-/.test(messageId)) {\n          throw new Error(\n            `Error in API Extractor config: The messages.extractorMessageReporting table contains` +\n              ` an invalid entry \"${messageId}\".  The name should begin with the \"ae-\" prefix.`\n          );\n        } else if (!allExtractorMessageIds.has(messageId)) {\n          throw new Error(\n            `Error in API Extractor config: The messages.extractorMessageReporting table contains` +\n              ` an unrecognized identifier \"${messageId}\".  Is it spelled correctly?`\n          );\n        } else {\n          this._reportingRuleByMessageId.set(messageId, reportingRule);\n        }\n      }\n    }\n\n    if (messagesConfig.tsdocMessageReporting) {\n      for (const messageId of Object.getOwnPropertyNames(messagesConfig.tsdocMessageReporting)) {\n        const reportingRule: IReportingRule = MessageRouter._getNormalizedRule(\n          messagesConfig.tsdocMessageReporting[messageId]\n        );\n\n        if (messageId === 'default') {\n          this._tsdocDefaultRule = reportingRule;\n        } else if (!/^tsdoc-/.test(messageId)) {\n          throw new Error(\n            `Error in API Extractor config: The messages.tsdocMessageReporting table contains` +\n              ` an invalid entry \"${messageId}\".  The name should begin with the \"tsdoc-\" prefix.`\n          );\n        } else if (!this._tsdocConfiguration.isKnownMessageId(messageId)) {\n          throw new Error(\n            `Error in API Extractor config: The messages.tsdocMessageReporting table contains` +\n              ` an unrecognized identifier \"${messageId}\".  Is it spelled correctly?`\n          );\n        } else {\n          this._reportingRuleByMessageId.set(messageId, reportingRule);\n        }\n      }\n    }\n  }\n\n  private static _getNormalizedRule(rule: IConfigMessageReportingRule): IReportingRule {\n    return {\n      logLevel: rule.logLevel || 'none',\n      addToApiReportFile: rule.addToApiReportFile || false\n    };\n  }\n\n  public get messages(): ReadonlyArray<ExtractorMessage> {\n    return this._messages;\n  }\n\n  /**\n   * Add a diagnostic message reported by the TypeScript compiler\n   */\n  public addCompilerDiagnostic(diagnostic: ts.Diagnostic): void {\n    switch (diagnostic.category) {\n      case ts.DiagnosticCategory.Suggestion:\n      case ts.DiagnosticCategory.Message:\n        return; // ignore noise\n    }\n\n    const messageText: string = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\\n');\n    const options: IExtractorMessageOptions = {\n      category: ExtractorMessageCategory.Compiler,\n      messageId: `TS${diagnostic.code}`,\n      text: messageText\n    };\n\n    if (diagnostic.file) {\n      // NOTE: Since compiler errors pertain to issues specific to the .d.ts files,\n      // we do not apply source mappings for them.\n      const sourceFile: ts.SourceFile = diagnostic.file;\n      const sourceLocation: ISourceLocation = this._sourceMapper.getSourceLocation({\n        sourceFile,\n        pos: diagnostic.start || 0,\n        useDtsLocation: true\n      });\n      options.sourceFilePath = sourceLocation.sourceFilePath;\n      options.sourceFileLine = sourceLocation.sourceFileLine;\n      options.sourceFileColumn = sourceLocation.sourceFileColumn;\n    }\n\n    this._messages.push(new ExtractorMessage(options));\n  }\n\n  /**\n   * Add a message from the API Extractor analysis\n   */\n  public addAnalyzerIssue(\n    messageId: ExtractorMessageId,\n    messageText: string,\n    astDeclarationOrSymbol: AstDeclaration | AstSymbol,\n    properties?: IExtractorMessageProperties\n  ): void {\n    let astDeclaration: AstDeclaration;\n    if (astDeclarationOrSymbol instanceof AstDeclaration) {\n      astDeclaration = astDeclarationOrSymbol;\n    } else {\n      astDeclaration = astDeclarationOrSymbol.astDeclarations[0];\n    }\n\n    const extractorMessage: ExtractorMessage = this.addAnalyzerIssueForPosition(\n      messageId,\n      messageText,\n      astDeclaration.declaration.getSourceFile(),\n      astDeclaration.declaration.getStart(),\n      properties\n    );\n\n    this._associateMessageWithAstDeclaration(extractorMessage, astDeclaration);\n  }\n\n  /**\n   * Add all messages produced from an invocation of the TSDoc parser, assuming they refer to\n   * code in the specified source file.\n   */\n  public addTsdocMessages(\n    parserContext: tsdoc.ParserContext,\n    sourceFile: ts.SourceFile,\n    astDeclaration?: AstDeclaration\n  ): void {\n    for (const message of parserContext.log.messages) {\n      const options: IExtractorMessageOptions = {\n        category: ExtractorMessageCategory.TSDoc,\n        messageId: message.messageId,\n        text: message.unformattedText\n      };\n\n      const sourceLocation: ISourceLocation = this._sourceMapper.getSourceLocation({\n        sourceFile,\n        pos: message.textRange.pos\n      });\n      options.sourceFilePath = sourceLocation.sourceFilePath;\n      options.sourceFileLine = sourceLocation.sourceFileLine;\n      options.sourceFileColumn = sourceLocation.sourceFileColumn;\n\n      const extractorMessage: ExtractorMessage = new ExtractorMessage(options);\n\n      if (astDeclaration) {\n        this._associateMessageWithAstDeclaration(extractorMessage, astDeclaration);\n      }\n\n      this._messages.push(extractorMessage);\n    }\n  }\n\n  /**\n   * Recursively collects the primitive members (numbers, strings, arrays, etc) into an object that\n   * is JSON serializable.  This is used by the \"--diagnostics\" feature to dump the state of configuration objects.\n   *\n   * @returns a JSON serializable object (possibly including `null` values)\n   *          or `undefined` if the input cannot be represented as JSON\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public static buildJsonDumpObject(input: any, options?: IBuildJsonDumpObjectOptions): any | undefined {\n    if (!options) {\n      options = {};\n    }\n\n    const keyNamesToOmit: Set<string> = new Set(options.keyNamesToOmit);\n\n    return MessageRouter._buildJsonDumpObject(input, keyNamesToOmit);\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private static _buildJsonDumpObject(input: any, keyNamesToOmit: Set<string>): any | undefined {\n    if (input === null || input === undefined) {\n      return null; // JSON uses null instead of undefined\n    }\n\n    switch (typeof input) {\n      case 'boolean':\n      case 'number':\n      case 'string':\n        return input;\n      case 'object':\n        if (Array.isArray(input)) {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          const outputArray: any[] = [];\n          for (const element of input) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const serializedElement: any = MessageRouter._buildJsonDumpObject(element, keyNamesToOmit);\n            if (serializedElement !== undefined) {\n              outputArray.push(serializedElement);\n            }\n          }\n          return outputArray;\n        }\n\n        const outputObject: object = {};\n        for (const key of Object.getOwnPropertyNames(input)) {\n          if (keyNamesToOmit.has(key)) {\n            continue;\n          }\n\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          const value: any = input[key];\n\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          const serializedValue: any = MessageRouter._buildJsonDumpObject(value, keyNamesToOmit);\n\n          if (serializedValue !== undefined) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            (outputObject as any)[key] = serializedValue;\n          }\n        }\n        return outputObject;\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Record this message in  _associatedMessagesForAstDeclaration\n   */\n  private _associateMessageWithAstDeclaration(\n    extractorMessage: ExtractorMessage,\n    astDeclaration: AstDeclaration\n  ): void {\n    let associatedMessages: ExtractorMessage[] | undefined =\n      this._associatedMessagesForAstDeclaration.get(astDeclaration);\n\n    if (!associatedMessages) {\n      associatedMessages = [];\n      this._associatedMessagesForAstDeclaration.set(astDeclaration, associatedMessages);\n    }\n    associatedMessages.push(extractorMessage);\n  }\n\n  /**\n   * Add a message for a location in an arbitrary source file.\n   */\n  public addAnalyzerIssueForPosition(\n    messageId: ExtractorMessageId,\n    messageText: string,\n    sourceFile: ts.SourceFile,\n    pos: number,\n    properties?: IExtractorMessageProperties\n  ): ExtractorMessage {\n    const options: IExtractorMessageOptions = {\n      category: ExtractorMessageCategory.Extractor,\n      messageId,\n      text: messageText,\n      properties\n    };\n\n    const sourceLocation: ISourceLocation = this._sourceMapper.getSourceLocation({\n      sourceFile,\n      pos\n    });\n    options.sourceFilePath = sourceLocation.sourceFilePath;\n    options.sourceFileLine = sourceLocation.sourceFileLine;\n    options.sourceFileColumn = sourceLocation.sourceFileColumn;\n\n    const extractorMessage: ExtractorMessage = new ExtractorMessage(options);\n\n    this._messages.push(extractorMessage);\n    return extractorMessage;\n  }\n\n  /**\n   * This is used when writing the API report file.  It looks up any messages that were configured to get emitted\n   * in the API report file and returns them.  It also records that they were emitted, which suppresses them from\n   * being shown on the console.\n   */\n  public fetchAssociatedMessagesForReviewFile(astDeclaration: AstDeclaration): ExtractorMessage[] {\n    const messagesForApiReportFile: ExtractorMessage[] = [];\n\n    const associatedMessages: ExtractorMessage[] =\n      this._associatedMessagesForAstDeclaration.get(astDeclaration) || [];\n    for (const associatedMessage of associatedMessages) {\n      // Make sure we didn't already report this message for some reason\n      if (!associatedMessage.handled) {\n        // Is this message type configured to go in the API report file?\n        const reportingRule: IReportingRule = this._getRuleForMessage(associatedMessage);\n        if (reportingRule.addToApiReportFile) {\n          // Include it in the result, and record that it went to the API report file\n          messagesForApiReportFile.push(associatedMessage);\n          associatedMessage.handled = true;\n        }\n      }\n    }\n\n    this._sortMessagesForOutput(messagesForApiReportFile);\n    return messagesForApiReportFile;\n  }\n\n  /**\n   * This returns all remaining messages that were flagged with `addToApiReportFile`, but which were not\n   * retreieved using `fetchAssociatedMessagesForReviewFile()`.\n   */\n  public fetchUnassociatedMessagesForReviewFile(): ExtractorMessage[] {\n    const messagesForApiReportFile: ExtractorMessage[] = [];\n\n    for (const unassociatedMessage of this.messages) {\n      // Make sure we didn't already report this message for some reason\n      if (!unassociatedMessage.handled) {\n        // Is this message type configured to go in the API report file?\n        const reportingRule: IReportingRule = this._getRuleForMessage(unassociatedMessage);\n        if (reportingRule.addToApiReportFile) {\n          // Include it in the result, and record that it went to the API report file\n          messagesForApiReportFile.push(unassociatedMessage);\n          unassociatedMessage.handled = true;\n        }\n      }\n    }\n\n    this._sortMessagesForOutput(messagesForApiReportFile);\n    return messagesForApiReportFile;\n  }\n\n  /**\n   * This returns the list of remaining messages that were not already processed by\n   * `fetchAssociatedMessagesForReviewFile()` or `fetchUnassociatedMessagesForReviewFile()`.\n   * These messages will be shown on the console.\n   */\n  public handleRemainingNonConsoleMessages(): void {\n    const messagesForLogger: ExtractorMessage[] = [];\n\n    for (const message of this.messages) {\n      // Make sure we didn't already report this message\n      if (!message.handled) {\n        messagesForLogger.push(message);\n      }\n    }\n\n    this._sortMessagesForOutput(messagesForLogger);\n\n    for (const message of messagesForLogger) {\n      this._handleMessage(message);\n    }\n  }\n\n  public logError(\n    messageId: ConsoleMessageId,\n    message: string,\n    properties?: IExtractorMessageProperties\n  ): void {\n    this._handleMessage(\n      new ExtractorMessage({\n        category: ExtractorMessageCategory.Console,\n        messageId,\n        text: message,\n        properties,\n        logLevel: ExtractorLogLevel.Error\n      })\n    );\n  }\n\n  public logWarning(\n    messageId: ConsoleMessageId,\n    message: string,\n    properties?: IExtractorMessageProperties\n  ): void {\n    this._handleMessage(\n      new ExtractorMessage({\n        category: ExtractorMessageCategory.Console,\n        messageId,\n        text: message,\n        properties,\n        logLevel: ExtractorLogLevel.Warning\n      })\n    );\n  }\n\n  public logInfo(\n    messageId: ConsoleMessageId,\n    message: string,\n    properties?: IExtractorMessageProperties\n  ): void {\n    this._handleMessage(\n      new ExtractorMessage({\n        category: ExtractorMessageCategory.Console,\n        messageId,\n        text: message,\n        properties,\n        logLevel: ExtractorLogLevel.Info\n      })\n    );\n  }\n\n  public logVerbose(\n    messageId: ConsoleMessageId,\n    message: string,\n    properties?: IExtractorMessageProperties\n  ): void {\n    this._handleMessage(\n      new ExtractorMessage({\n        category: ExtractorMessageCategory.Console,\n        messageId,\n        text: message,\n        properties,\n        logLevel: ExtractorLogLevel.Verbose\n      })\n    );\n  }\n\n  public logDiagnosticHeader(title: string): void {\n    this.logDiagnostic(MessageRouter.DIAGNOSTICS_LINE);\n    this.logDiagnostic(`DIAGNOSTIC: ` + title);\n    this.logDiagnostic(MessageRouter.DIAGNOSTICS_LINE);\n  }\n\n  public logDiagnosticFooter(): void {\n    this.logDiagnostic(MessageRouter.DIAGNOSTICS_LINE + '\\n');\n  }\n\n  public logDiagnostic(message: string): void {\n    if (this.showDiagnostics) {\n      this.logVerbose(ConsoleMessageId.Diagnostics, message);\n    }\n  }\n\n  /**\n   * Give the calling application a chance to handle the `ExtractorMessage`, and if not, display it on the console.\n   */\n  private _handleMessage(message: ExtractorMessage): void {\n    // Don't tally messages that were already \"handled\" by writing them into the API report\n    if (message.handled) {\n      return;\n    }\n\n    // Assign the ExtractorMessage.logLevel; the message callback may adjust it below\n    if (message.category === ExtractorMessageCategory.Console) {\n      // Console messages have their category log level assigned via logInfo(), logVerbose(), etc.\n    } else {\n      const reportingRule: IReportingRule = this._getRuleForMessage(message);\n      message.logLevel = reportingRule.logLevel;\n    }\n\n    // If there is a callback, allow it to modify and/or handle the message\n    if (this._messageCallback) {\n      this._messageCallback(message);\n    }\n\n    // Update the statistics\n    switch (message.logLevel) {\n      case ExtractorLogLevel.Error:\n        ++this.errorCount;\n        break;\n      case ExtractorLogLevel.Warning:\n        ++this.warningCount;\n        break;\n    }\n\n    if (message.handled) {\n      return;\n    }\n\n    // The messageCallback did not handle the message, so perform default handling\n    message.handled = true;\n\n    if (message.logLevel === ExtractorLogLevel.None) {\n      return;\n    }\n\n    let messageText: string;\n    if (message.category === ExtractorMessageCategory.Console) {\n      messageText = message.text;\n    } else {\n      messageText = message.formatMessageWithLocation(this._workingPackageFolder);\n    }\n\n    switch (message.logLevel) {\n      case ExtractorLogLevel.Error:\n        console.error(Colorize.red('Error: ' + messageText));\n        break;\n      case ExtractorLogLevel.Warning:\n        console.warn(Colorize.yellow('Warning: ' + messageText));\n        break;\n      case ExtractorLogLevel.Info:\n        console.log(messageText);\n        break;\n      case ExtractorLogLevel.Verbose:\n        if (this.showVerboseMessages) {\n          console.log(Colorize.cyan(messageText));\n        }\n        break;\n      default:\n        throw new Error(`Invalid logLevel value: ${JSON.stringify(message.logLevel)}`);\n    }\n  }\n\n  /**\n   * For a given message, determine the IReportingRule based on the rule tables.\n   */\n  private _getRuleForMessage(message: ExtractorMessage): IReportingRule {\n    const reportingRule: IReportingRule | undefined = this._reportingRuleByMessageId.get(message.messageId);\n    if (reportingRule) {\n      return reportingRule;\n    }\n    switch (message.category) {\n      case ExtractorMessageCategory.Compiler:\n        return this._compilerDefaultRule;\n      case ExtractorMessageCategory.Extractor:\n        return this._extractorDefaultRule;\n      case ExtractorMessageCategory.TSDoc:\n        return this._tsdocDefaultRule;\n      case ExtractorMessageCategory.Console:\n        throw new InternalError('ExtractorMessageCategory.Console is not supported with IReportingRule');\n    }\n  }\n\n  /**\n   * Sorts an array of messages according to a reasonable ordering\n   */\n  private _sortMessagesForOutput(messages: ExtractorMessage[]): void {\n    messages.sort((a, b) => {\n      let diff: number;\n      // First sort by file name\n      diff = Sort.compareByValue(a.sourceFilePath, b.sourceFilePath);\n      if (diff !== 0) {\n        return diff;\n      }\n      // Then sort by line number\n      diff = Sort.compareByValue(a.sourceFileLine, b.sourceFileLine);\n      if (diff !== 0) {\n        return diff;\n      }\n      // Then sort by messageId\n      return Sort.compareByValue(a.messageId, b.messageId);\n    });\n  }\n}\n"]}
{"version": 3, "file": "MessageRouter.d.ts", "sourceRoot": "", "sources": ["../../src/collector/MessageRouter.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,MAAM,YAAY,CAAC;AACjC,OAAO,KAAK,KAAK,KAAK,MAAM,kBAAkB,CAAC;AAI/C,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAC5D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AACvD,OAAO,EACL,gBAAgB,EAGhB,KAAK,2BAA2B,EACjC,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,KAAK,kBAAkB,EAA0B,MAAM,2BAA2B,CAAC;AAC5F,OAAO,KAAK,EAAE,wBAAwB,EAA+B,MAAM,oBAAoB,CAAC;AAChG,OAAO,KAAK,EAAmB,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAEpE,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAO3D,MAAM,WAAW,qBAAqB;IACpC,oBAAoB,EAAE,MAAM,GAAG,SAAS,CAAC;IACzC,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,gBAAgB,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC;IACnE,cAAc,EAAE,wBAAwB,CAAC;IACzC,mBAAmB,EAAE,OAAO,CAAC;IAC7B,eAAe,EAAE,OAAO,CAAC;IACzB,kBAAkB,EAAE,KAAK,CAAC,kBAAkB,CAAC;IAC7C,YAAY,EAAE,YAAY,CAAC;CAC5B;AAED,MAAM,WAAW,2BAA2B;IAC1C;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;CAC3B;AAED,qBAAa,aAAa;IACxB,gBAAuB,gBAAgB,EAAE,MAAM,CACkB;IAEjE,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAqB;IAC3D,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAoD;IAGrF,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAqB;IAG/C,OAAO,CAAC,QAAQ,CAAC,oCAAoC,CAA0C;IAE/F,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAe;IAE7C,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAA2B;IAG/D,OAAO,CAAC,yBAAyB,CAAkE;IACnG,OAAO,CAAC,oBAAoB,CAG1B;IACF,OAAO,CAAC,qBAAqB,CAG3B;IACF,OAAO,CAAC,iBAAiB,CAAmF;IAErG,UAAU,EAAE,MAAM,CAAK;IACvB,YAAY,EAAE,MAAM,CAAK;IAEhC;;OAEG;IACH,SAAgB,mBAAmB,EAAE,OAAO,CAAC;IAE7C;;OAEG;IACH,SAAgB,eAAe,EAAE,OAAO,CAAC;gBAEtB,OAAO,EAAE,qBAAqB;IAgBjD;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAqE5B,OAAO,CAAC,MAAM,CAAC,kBAAkB;IAOjC,IAAW,QAAQ,IAAI,aAAa,CAAC,gBAAgB,CAAC,CAErD;IAED;;OAEG;IACI,qBAAqB,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,GAAG,IAAI;IA+B7D;;OAEG;IACI,gBAAgB,CACrB,SAAS,EAAE,kBAAkB,EAC7B,WAAW,EAAE,MAAM,EACnB,sBAAsB,EAAE,cAAc,GAAG,SAAS,EAClD,UAAU,CAAC,EAAE,2BAA2B,GACvC,IAAI;IAmBP;;;OAGG;IACI,gBAAgB,CACrB,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,UAAU,EAAE,EAAE,CAAC,UAAU,EACzB,cAAc,CAAC,EAAE,cAAc,GAC9B,IAAI;IA0BP;;;;;;OAMG;WAEW,mBAAmB,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,2BAA2B,GAAG,GAAG,GAAG,SAAS;IAWrG,OAAO,CAAC,MAAM,CAAC,oBAAoB;IA+CnC;;OAEG;IACH,OAAO,CAAC,mCAAmC;IAc3C;;OAEG;IACI,2BAA2B,CAChC,SAAS,EAAE,kBAAkB,EAC7B,WAAW,EAAE,MAAM,EACnB,UAAU,EAAE,EAAE,CAAC,UAAU,EACzB,GAAG,EAAE,MAAM,EACX,UAAU,CAAC,EAAE,2BAA2B,GACvC,gBAAgB;IAsBnB;;;;OAIG;IACI,oCAAoC,CAAC,cAAc,EAAE,cAAc,GAAG,gBAAgB,EAAE;IAsB/F;;;OAGG;IACI,sCAAsC,IAAI,gBAAgB,EAAE;IAoBnE;;;;OAIG;IACI,iCAAiC,IAAI,IAAI;IAiBzC,QAAQ,CACb,SAAS,EAAE,gBAAgB,EAC3B,OAAO,EAAE,MAAM,EACf,UAAU,CAAC,EAAE,2BAA2B,GACvC,IAAI;IAYA,UAAU,CACf,SAAS,EAAE,gBAAgB,EAC3B,OAAO,EAAE,MAAM,EACf,UAAU,CAAC,EAAE,2BAA2B,GACvC,IAAI;IAYA,OAAO,CACZ,SAAS,EAAE,gBAAgB,EAC3B,OAAO,EAAE,MAAM,EACf,UAAU,CAAC,EAAE,2BAA2B,GACvC,IAAI;IAYA,UAAU,CACf,SAAS,EAAE,gBAAgB,EAC3B,OAAO,EAAE,MAAM,EACf,UAAU,CAAC,EAAE,2BAA2B,GACvC,IAAI;IAYA,mBAAmB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAMxC,mBAAmB,IAAI,IAAI;IAI3B,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAM3C;;OAEG;IACH,OAAO,CAAC,cAAc;IAmEtB;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAiB1B;;OAEG;IACH,OAAO,CAAC,sBAAsB;CAiB/B"}
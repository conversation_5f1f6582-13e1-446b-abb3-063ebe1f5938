import type { AstEntity } from '../analyzer/AstEntity';
/**
 * This is a data structure used by the Collector to track an AstEntity that may be emitted in the *.d.ts file.
 *
 * @remarks
 * The additional contextual state beyond AstSymbol is:
 * - Whether it's an export of this entry point or not
 * - The nameForEmit, which may get renamed by DtsRollupGenerator._makeUniqueNames()
 * - The export name (or names, if the same symbol is exported multiple times)
 */
export declare class CollectorEntity {
    /**
     * The AstEntity that this entry represents.
     */
    readonly astEntity: AstEntity;
    private _exportNames;
    private _exportNamesSorted;
    private _singleExportName;
    private _localExportNamesByParent;
    private _nameForEmit;
    private _sortKey;
    constructor(astEntity: AstEntity);
    /**
     * The declaration name that will be emitted in the .d.ts rollup, .api.md, and .api.json files. Generated by
     * `Collector._makeUniqueNames`. Be aware that the declaration may be renamed to avoid conflicts with (1)
     * global names (e.g. `Promise`) and (2) if local, other local names across different files.
     */
    get nameForEmit(): string | undefined;
    set nameForEmit(value: string | undefined);
    /**
     * The list of export names if this symbol is exported from the entry point.
     *
     * @remarks
     * Note that a given symbol may be exported more than once:
     * ```
     * class X { }
     * export { X }
     * export { X as Y }
     * ```
     */
    get exportNames(): ReadonlySet<string>;
    /**
     * If exportNames contains only one string, then singleExportName is that string.
     * In all other cases, it is undefined.
     */
    get singleExportName(): string | undefined;
    /**
     * This is true if exportNames contains only one string, and the declaration can be exported using the inline syntax
     * such as "export class X { }" instead of "export { X }".
     */
    get shouldInlineExport(): boolean;
    /**
     * Indicates that this entity is exported from the package entry point. Compare to `CollectorEntity.exported`.
     */
    get exportedFromEntryPoint(): boolean;
    /**
     * Indicates that this entity is exported from its parent module (i.e. either the package entry point or
     * a local namespace). Compare to `CollectorEntity.consumable`.
     *
     * @remarks
     * In the example below:
     *
     * ```ts
     * declare function add(): void;
     * declare namespace calculator {
     *  export {
     *    add
     *  }
     * }
     * ```
     *
     * Namespace `calculator` is neither exported nor consumable, function `add` is exported (from `calculator`)
     * but not consumable.
     */
    get exported(): boolean;
    /**
     * Indicates that it is possible for a consumer of the API to "consume" this entity, either by importing
     * it directly or via a namespace. If an entity is not consumable, then API Extractor will report an
     * `ae-forgotten-export` warning. Compare to `CollectorEntity.exported`.
     *
     * @remarks
     * An API item is consumable if:
     *
     * 1. It is exported from the top-level entry point OR
     * 2. It is exported from a consumable parent entity.
     *
     * For an example of #2, consider how `AstNamespaceImport` entities are processed. A generated rollup.d.ts
     * might look like this:
     *
     * ```ts
     * declare function add(): void;
     * declare namespace calculator {
     *   export {
     *     add
     *   }
     * }
     * export { calculator }
     * ```
     *
     * In this example, `add` is exported via the consumable `calculator` namespace.
     */
    get consumable(): boolean;
    /**
     * Return the first consumable parent that exports this entity. If there is none, returns
     * `undefined`.
     */
    getFirstExportingConsumableParent(): CollectorEntity | undefined;
    /**
     * Adds a new export name to the entity.
     */
    addExportName(exportName: string): void;
    /**
     * Adds a new local export name to the entity.
     *
     * @remarks
     * In the example below:
     *
     * ```ts
     * declare function add(): void;
     * declare namespace calculator {
     *  export {
     *    add
     *  }
     * }
     * ```
     *
     * `add` is the local export name for the `CollectorEntity` for `add`.
     */
    addLocalExportName(localExportName: string, parent: CollectorEntity): void;
    /**
     * A sorting key used by DtsRollupGenerator._makeUniqueNames()
     */
    getSortKey(): string;
}
//# sourceMappingURL=CollectorEntity.d.ts.map
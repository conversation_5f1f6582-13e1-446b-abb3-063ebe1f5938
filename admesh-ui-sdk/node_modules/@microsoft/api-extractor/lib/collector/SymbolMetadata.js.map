{"version": 3, "file": "SymbolMetadata.js", "sourceRoot": "", "sources": ["../../src/collector/SymbolMetadata.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAW3D;;;GAGG;AACH,MAAa,cAAc;IAKzB,YAAmB,OAA+B;QAChD,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;IAC/D,CAAC;CACF;AARD,wCAQC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport type { ReleaseTag } from '@microsoft/api-extractor-model';\n\n/**\n * Constructor parameters for `SymbolMetadata`.\n */\nexport interface ISymbolMetadataOptions {\n  maxEffectiveReleaseTag: ReleaseTag;\n}\n\n/**\n * Stores the Collector's additional analysis for an `AstSymbol`.  This object is assigned to `AstSymbol.metadata`\n * but consumers must always obtain it by calling `Collector.fetchSymbolMetadata()`.\n */\nexport class SymbolMetadata {\n  // For all declarations associated with this symbol, this is the\n  // `ApiItemMetadata.effectiveReleaseTag` value that is most public.\n  public readonly maxEffectiveReleaseTag: ReleaseTag;\n\n  public constructor(options: ISymbolMetadataOptions) {\n    this.maxEffectiveReleaseTag = options.maxEffectiveReleaseTag;\n  }\n}\n"]}
{"version": 3, "file": "DeclarationMetadata.js", "sourceRoot": "", "sources": ["../../src/collector/DeclarationMetadata.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAK3D;;;;;;;GAOG;AACH,MAAsB,mBAAmB;CAuBxC;AAvBD,kDAuBC;AAED;;GAEG;AACH,MAAa,2BAA4B,SAAQ,mBAAmB;IAApE;;QACS,uBAAkB,GAAoC,SAAS,CAAC;QAEhE,gBAAW,GAAY,KAAK,CAAC;QAE7B,0BAAqB,GAAqB,EAAE,CAAC;IACtD,CAAC;CAAA;AAND,kEAMC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\nimport type * as tsdoc from '@microsoft/tsdoc';\nimport type { AstDeclaration } from '../analyzer/AstDeclaration';\n\n/**\n * Stores the Collector's additional analysis for a specific `AstDeclaration` signature.  This object is assigned to\n * `AstDeclaration.declarationMetadata` but consumers must always obtain it by calling\n * `Collector.fetchDeclarationMetadata()`.\n *\n * Note that ancillary declarations share their `ApiItemMetadata` with the main declaration,\n * whereas a separate `DeclarationMetadata` object is created for each declaration.\n */\nexport abstract class DeclarationMetadata {\n  /**\n   * The ParserContext from when the TSDoc comment was parsed from the source code.\n   * If the source code did not contain a doc comment, then this will be undefined.\n   *\n   * Note that if an ancillary declaration has a doc comment, it is tracked here, whereas\n   * `ApiItemMetadata.tsdocComment` corresponds to documentation for the main declaration.\n   */\n  public abstract readonly tsdocParserContext: tsdoc.ParserContext | undefined;\n\n  /**\n   * If true, then this declaration is treated as part of another declaration.\n   */\n  public abstract readonly isAncillary: boolean;\n\n  /**\n   * A list of other declarations that are treated as being part of this declaration.  For example, a property\n   * getter/setter pair will be treated as a single API item, with the setter being treated as ancillary to the getter.\n   *\n   * If the `ancillaryDeclarations` array is non-empty, then `isAncillary` will be false for this declaration,\n   * and `isAncillary` will be true for all the array items.\n   */\n  public abstract readonly ancillaryDeclarations: ReadonlyArray<AstDeclaration>;\n}\n\n/**\n * Used internally by the `Collector` to build up `DeclarationMetadata`.\n */\nexport class InternalDeclarationMetadata extends DeclarationMetadata {\n  public tsdocParserContext: tsdoc.ParserContext | undefined = undefined;\n\n  public isAncillary: boolean = false;\n\n  public ancillaryDeclarations: AstDeclaration[] = [];\n}\n"]}
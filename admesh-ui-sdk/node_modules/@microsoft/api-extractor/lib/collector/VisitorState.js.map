{"version": 3, "file": "VisitorState.js", "sourceRoot": "", "sources": ["../../src/collector/VisitorState.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;GAEG;AACH,IAAY,YAiBX;AAjBD,WAAY,YAAY;IACtB;;OAEG;IACH,yDAAa,CAAA;IAEb;;;;OAIG;IACH,uDAAY,CAAA;IAEZ;;OAEG;IACH,qDAAW,CAAA;AACb,CAAC,EAjBW,YAAY,4BAAZ,YAAY,QAiBvB", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\n/**\n * Keeps track of a directed graph traversal that needs to detect cycles.\n */\nexport enum VisitorState {\n  /**\n   * We have not visited the node yet.\n   */\n  Unvisited = 0,\n\n  /**\n   * We have visited the node, but have not finished traversing its references yet.\n   * If we reach a node that is already in the `Visiting` state, this means we have\n   * encountered a cyclic reference.\n   */\n  Visiting = 1,\n\n  /**\n   * We are finished vising the node and all its references.\n   */\n  Visited = 2\n}\n"]}
{"version": 3, "file": "WorkingPackage.js", "sourceRoot": "", "sources": ["../../src/collector/WorkingPackage.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAgB3D;;;;;;;;;;;;;GAaG;AACH,MAAa,cAAc;IAmCzB,YAAmB,OAA+B;QAChD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IAC/B,CAAC;CACF;AA/CD,wCA+CC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON>ICEN<PERSON> in the project root for license information.\n\nimport type * as ts from 'typescript';\nimport type * as tsdoc from '@microsoft/tsdoc';\n\nimport type { INodePackageJson } from '@rushstack/node-core-library';\n\n/**\n * Constructor options for WorkingPackage\n */\nexport interface IWorkingPackageOptions {\n  packageFolder: string;\n  packageJson: INodePackageJson;\n  entryPointSourceFile: ts.SourceFile;\n}\n\n/**\n * Information about the working package for a particular invocation of API Extractor.\n *\n * @remarks\n * API Extractor tries to model the world as a collection of NPM packages, such that each\n * .d.ts file belongs to at most one package.  When API Extractor is invoked on a project,\n * we refer to that project as being the \"working package\".  There is exactly one\n * \"working package\" for the duration of this analysis.  Any files that do not belong to\n * the working package are referred to as \"external\":  external declarations belonging to\n * external packages.\n *\n * If API Extractor is invoked on a standalone .d.ts file, the \"working package\" may not\n * have an actual package.json file on disk, but we still refer to it in concept.\n */\nexport class WorkingPackage {\n  /**\n   * Returns the folder for the package.json file of the working package.\n   *\n   * @remarks\n   * If the entry point is `C:\\Folder\\project\\src\\index.ts` and the nearest package.json\n   * is `C:\\Folder\\project\\package.json`, then the packageFolder is `C:\\Folder\\project`\n   */\n  public readonly packageFolder: string;\n\n  /**\n   * The parsed package.json file for the working package.\n   */\n  public readonly packageJson: INodePackageJson;\n\n  /**\n   * The entry point being processed during this invocation of API Extractor.\n   *\n   * @remarks\n   * The working package may have multiple entry points; however, today API Extractor\n   * only processes a single entry point during an invocation.  This will be improved\n   * in the future.\n   */\n  public readonly entryPointSourceFile: ts.SourceFile;\n\n  /**\n   * The `@packageDocumentation` comment, if any, for the working package.\n   */\n  public tsdocComment: tsdoc.DocComment | undefined;\n\n  /**\n   * Additional parser information for `WorkingPackage.tsdocComment`.\n   */\n  public tsdocParserContext: tsdoc.ParserContext | undefined;\n\n  public constructor(options: IWorkingPackageOptions) {\n    this.packageFolder = options.packageFolder;\n    this.packageJson = options.packageJson;\n    this.entryPointSourceFile = options.entryPointSourceFile;\n  }\n\n  /**\n   * Returns the full name of the working package.\n   */\n  public get name(): string {\n    return this.packageJson.name;\n  }\n}\n"]}
{"version": 3, "file": "ExportAnalyzer.js", "sourceRoot": "", "sources": ["../../src/analyzer/ExportAnalyzer.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+CAAiC;AACjC,oEAA6D;AAE7D,2DAAwD;AACxD,2CAAwC;AACxC,2CAA+E;AAC/E,2CAAmE;AACnE,+DAA4D;AAC5D,+EAA4E;AAG5E,6DAA0D;AAC1D,mDAAgD;AAChD,6DAA0D;AAgC1D;;;;;;;;;GASG;AACH,MAAa,cAAc;IAczB,YACE,OAAmB,EACnB,WAA2B,EAC3B,mBAAwC,EACxC,cAA+B;QAZhB,8BAAyB,GAA8B,IAAI,GAAG,EAAwB,CAAC;QAExG,4CAA4C;QAC3B,kCAA6B,GAAuB,IAAI,GAAG,EAAiB,CAAC;QAE7E,qBAAgB,GAA2B,IAAI,GAAG,EAAqB,CAAC;QACxE,gCAA2B,GAAuC,IAAI,GAAG,EAAE,CAAC;QAQ3F,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAChD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACI,4BAA4B,CACjC,UAAyB,EACzB,eAAgD,EAChD,UAAmB;QAEnB,MAAM,YAAY,GAAc,IAAI,CAAC,8BAA8B,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;QAEjG,iEAAiE;QACjE,gFAAgF;QAChF,oDAAoD;QACpD,IAAI,SAAS,GAA0B,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACxF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,wGAAwG;YACxG,MAAM,kBAAkB,GACtB,eAAe,KAAK,SAAS,IAAI,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC;YAE5F,SAAS,GAAG,IAAI,qBAAS,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,kBAAkB,EAAE,CAAC,CAAC;YAE5E,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;YAE5D,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;gBACzB,6GAA6G;gBAC7G,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC;oBAChF,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;wBACrC,MAAM,IAAI,iCAAa,CACrB,8EAA8E,CAC/E,CAAC;oBACJ,CAAC;oBAED,MAAM,cAAc,GAAc,qCAAiB,CAAC,aAAa,CAC/D,cAAc,EACd,IAAI,CAAC,YAAY,CAClB,CAAC;oBAEF,0DAA0D;oBAC1D,MAAM,oBAAoB,GACxB,qCAAiB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;oBACvD,IAAI,oBAAoB,EAAE,CAAC;wBACzB,MAAM,SAAS,GAA0B,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;4BAC3E,cAAc,EAAE,cAAc;4BAC9B,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,sBAAsB,EAAE,IAAI;4BAC5B,YAAY,EAAE,IAAI;yBACnB,CAAC,CAAC;wBAEH,IAAI,CAAC,SAAS,EAAE,CAAC;4BACf,MAAM,IAAI,KAAK,CACb,sBAAsB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;gCAC5D,yDAA2B,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CACtE,CAAC;wBACJ,CAAC;wBAED,SAAS,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;oBACvE,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,mEAAmE;gBAEnE,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBACzB,8FAA8F;oBAC9F,8CAA8C;oBAC9C,MAAM,gBAAgB,GAA0B,YAAY,CAAC,OAAO,CAAC,GAAG,CACtE,EAAE,CAAC,kBAAkB,CAAC,UAAU,CACjC,CAAC;oBACF,IAAI,gBAAgB,EAAE,CAAC;wBACrB,KAAK,MAAM,qBAAqB,IAAI,gBAAgB,CAAC,eAAe,EAAE,IAAI,EAAE,EAAE,CAAC;4BAC7E,IAAI,EAAE,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,EAAE,CAAC;gCAClD,MAAM,kBAAkB,GAA0B,IAAI,CAAC,wBAAwB,CAC7E,qBAAqB,EACrB,gBAAgB,CACjB,CAAC;gCAEF,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;oCACrC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;gCACxD,CAAC;4BACH,CAAC;iCAAM,CAAC;gCACN,uEAAuE;gCACvE,oCAAoC;4BACtC,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;OAOG;IACK,8BAA8B,CACpC,UAAyB,EACzB,eAAgD;QAEhD,MAAM,YAAY,GAA0B,yCAAmB,CAAC,0BAA0B,CACxF,UAAU,EACV,IAAI,CAAC,YAAY,CAClB,CAAC;QACF,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,8EAA8E;YAC9E,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,wGAAwG;YACxG,mDAAmD;YAEnD,sCAAsC;YACtC,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/E,6FAA6F;gBAC7F,IAAI,cAAc,GAA0B,yCAAmB,CAAC,yBAAyB,CACvF,eAAe,CAAC,qBAAqB,EACrC,IAAI,CAAC,YAAY,CAClB,CAAC;gBAEF,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;oBACjC,wGAAwG;oBACxG,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC;gBAC7F,CAAC;gBAED,IAAI,cAAc,KAAK,SAAS,IAAI,cAAc,KAAK,eAAe,CAAC,qBAAqB,EAAE,CAAC;oBAC7F,iFAAiF;oBACjF,MAAM,MAAM,GAA0B,yCAAmB,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;oBAC1F,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;wBACzB,2CAA2C;wBAC3C,sCAAsC;wBACtC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;4BACtD,4EAA4E;4BAC5E,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;4BACnD,OAAO,MAAM,CAAC;wBAChB,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,IAAI,iCAAa,CAAC,kCAAkC,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,mBAA8B;QAC5D,IAAI,mBAAmB,CAAC,UAAU,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,mBAAmB,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YAC1D,MAAM,mBAAmB,GAAyB;gBAChD,iBAAiB,EAAE,IAAI,GAAG,EAAa;gBACvC,qBAAqB,EAAE,IAAI,GAAG,EAAqB;gBACnD,2BAA2B,EAAE,IAAI,GAAG,EAAa;aAClD,CAAC;YAEF,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;YAE3E,mBAAmB,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAChE,CAAC;QACD,OAAO,mBAAmB,CAAC,mBAAmB,CAAC;IACjD,CAAC;IAED;;;OAGG;IACK,qBAAqB,CAC3B,yBAA0F,EAC1F,eAAuB;;QAEvB,MAAM,SAAS,GAA4C,EAAE,CAAC,gBAAgB,CAAC,yBAAyB,CAAC;YACvG,CAAC,CAAC,yBAAyB,CAAC,QAAQ;YACpC,CAAC,CAAC,yBAAyB,CAAC,eAAe,CAAC;QAC9C,MAAM,IAAI,GACR,SAAS,IAAI,EAAE,CAAC,mBAAmB,CAAC,SAAS,CAAC;YAC5C,CAAC,CAAC,yCAAmB,CAAC,uBAAuB,CACzC,yBAAyB,CAAC,aAAa,EAAE,EACzC,SAAS,EACT,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CACnC;YACH,CAAC,CAAC,SAAS,CAAC;QAEhB,MAAM,cAAc,GAAsC,yCAAmB,CAAC,iBAAiB,CAC7F,IAAI,CAAC,QAAQ,EACb,yBAAyB,CAAC,aAAa,EAAE,EACzC,eAAe,EACf,IAAI,CACL,CAAC;QAEF,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACjC,yGAAyG;YACzG,0FAA0F;YAC1F,sDAAsD;YACtD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gEAAgE;QAChE,MAAM,WAAW,GAAuB,MAAA,cAAc,CAAC,SAAS,0CAAE,IAAI,CAAC;QACvE,IAAI,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,cAAc,CAAC,uBAAuB,KAAK,SAAS,EAAE,CAAC;YACzD,wGAAwG;YACxG,4BAA4B;YAC5B,MAAM,IAAI,iCAAa,CACrB,uCAAuC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,gBAAgB;gBACpF,yDAA2B,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,CAC3E,CAAC;QACJ,CAAC;QAED,OAAO,cAAc,CAAC,uBAAuB,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACI,6BAA6B,CAAC,UAAyB;QAC5D,OAAO,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC5D,CAAC;IAEO,2BAA2B,CAAC,mBAAyC,EAAE,SAAoB;QACjG,MAAM,EAAE,iBAAiB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,GAAG,mBAAmB,CAAC;QACtG,IAAI,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QACD,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEjC,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACzB,2BAA2B,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,qDAAqD;YACrD,IAAI,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBACnC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,EAAE;oBAClE,QAAQ,UAAU,EAAE,CAAC;wBACnB,KAAK,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;wBACtC,KAAK,EAAE,CAAC,kBAAkB,CAAC,YAAY;4BACrC,MAAM;wBACR;4BACE,kFAAkF;4BAClF,IAAI,UAAU,KAAK,EAAE,CAAC,kBAAkB,CAAC,OAAO,IAAI,iBAAiB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gCACjF,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;oCAClD,MAAM,SAAS,GAAc,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;oCAEtF,IAAI,SAAS,YAAY,qBAAS,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;wCAC5D,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oCAC1C,CAAC;oCAED,IAAI,SAAS,YAAY,uCAAkB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;wCAC/E,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oCAC1C,CAAC;oCAED,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gCAC1D,CAAC;4BACH,CAAC;4BACD,MAAM;oBACV,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,KAAK,MAAM,kBAAkB,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;gBAC/D,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAC7B,MAAiB,EACjB,yBAAkC;QAElC,sCAAsC;QACtC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC;YACjE,uFAAuF;YACvF,WAAW;YACX,EAAE;YACF,oCAAoC;YACpC,iBAAiB;YACjB,IAAI;YACJ,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,OAAO,GAAc,MAAM,CAAC;QAEhC,IAAI,yBAAyB,EAAE,CAAC;YAC9B,OAAO,GAAG,qCAAiB,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACvE,CAAC;aAAM,CAAC;YACN,SAAS,CAAC;gBACR,uFAAuF;gBACvF,KAAK,MAAM,WAAW,IAAI,OAAO,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;oBACrD,IAAI,gBAAuC,CAAC;oBAC5C,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;oBACzE,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;wBACnC,OAAO,gBAAgB,CAAC;oBAC1B,CAAC;oBACD,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;oBACzE,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;wBACnC,OAAO,gBAAgB,CAAC;oBAC1B,CAAC;gBACH,CAAC;gBAED,sCAAsC;gBACtC,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC5C,MAAM;gBACR,CAAC;gBAED,MAAM,YAAY,GAAc,yCAAmB,CAAC,yBAAyB,CAC3E,OAAO,EACP,IAAI,CAAC,YAAY,CAClB,CAAC;gBACF,wCAAwC;gBACxC,IAAI,CAAC,YAAY,IAAI,YAAY,KAAK,OAAO,EAAE,CAAC;oBAC9C,MAAM;gBACR,CAAC;gBAED,OAAO,GAAG,YAAY,CAAC;YACzB,CAAC;QACH,CAAC;QAED,+CAA+C;QAC/C,MAAM,SAAS,GAA0B,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC3E,cAAc,EAAE,OAAO;YACvB,UAAU,EAAE,yBAAyB;YACrC,sBAAsB,EAAE,KAAK;YAC7B,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,0CAA0C,CAC/C,IAAuB,EACvB,yBAAkC;QAElC,MAAM,kBAAkB,GAAuB,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;QAEpF,IAAI,kBAAkB,EAAE,CAAC;YACvB,IAAI,UAAkB,CAAC;YACvB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,iBAAiB;gBACjB,8DAA8D;gBAC9D,EAAE;gBACF,uBAAuB;gBACvB,oBAAoB;gBACpB,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,iBAAiB;gBACjB,sCAAsC;gBACtC,EAAE;gBACF,uBAAuB;gBACvB,yBAAyB;gBAEzB,UAAU,GAAG,6BAAa,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YACzE,CAAC;YAED,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;gBACrC,UAAU,EAAE,yBAAa,CAAC,UAAU;gBACpC,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,kBAAkB;gBAC9B,UAAU,EAAE,KAAK;aAClB,CAAC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,MAAM,cAAc,GAAsC,IAAI,CAAC,SAAS;YACtE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBACnD,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK;gBACtB,CAAC,CAAC,IAAI,CAAC,SAAS;YAClB,CAAC,CAAC,IAAI,CAAC;QAET,uFAAuF;QACvF,MAAM,YAAY,GAA0B,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;QAClG,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,iCAAa,CACrB,oCAAoC,IAAI,CAAC,OAAO,EAAE,IAAI;gBACpD,yDAA2B,CAAC,iBAAiB,CAAC,IAAI,CAAC,CACtD,CAAC;QACJ,CAAC;QAED,IAAI,cAAc,GAAc,YAAY,CAAC;QAC7C,SAAS,CAAC;YACR,MAAM,mBAAmB,GAA0B,IAAI,CAAC,wBAAwB,CAC9E,cAAc,EACd,yBAAyB,CAC1B,CAAC;YAEF,IAAI,mBAAmB,EAAE,CAAC;gBACxB,OAAO,mBAAmB,CAAC;YAC7B,CAAC;YAED,MAAM,kBAAkB,GACtB,cAAc,CAAC,YAAY,IAAK,cAAc,CAAC,YAAY,CAAC,CAAC,CAAyB,CAAC;YAEzF,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC/E,OAAO,IAAI,CAAC,0CAA0C,CACpD,kBAAuC,EACvC,yBAAyB,CAC1B,CAAC;YACJ,CAAC;YAED,sCAAsC;YACtC,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnD,MAAM;YACR,CAAC;YAED,MAAM,YAAY,GAAc,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YACnF,IAAI,CAAC,YAAY,IAAI,YAAY,KAAK,cAAc,EAAE,CAAC;gBACrD,MAAM;YACR,CAAC;YAED,cAAc,GAAG,YAAY,CAAC;QAChC,CAAC;QAED,MAAM,SAAS,GAA0B,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC3E,cAAc,EAAE,cAAc;YAC9B,UAAU,EAAE,yBAAyB;YACrC,sBAAsB,EAAE,KAAK;YAC7B,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,0BAA0B,CAChC,WAA2B,EAC3B,iBAA4B;QAE5B,MAAM,iBAAiB,GACrB,qCAAiB,CAAC,eAAe,CAAuB,WAAW,EAAE,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAExG,IAAI,iBAAiB,EAAE,CAAC;YACtB,IAAI,UAAU,GAAuB,SAAS,CAAC;YAE/C,IAAI,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;gBACvD,WAAW;gBACX,kCAAkC;gBAClC,EAAE;gBACF,qBAAqB;gBACrB,yCAAyC;gBACzC,kBAAkB;gBAClB,yCAAyC;gBACzC,kBAAkB;gBAClB,qDAAqD;gBACrD,uCAAuC;gBACvC,wCAAwC;gBACxC,qCAAqC;gBACrC,qCAAqC;gBACrC,6BAA6B;gBAE7B,wCAAwC;gBACxC,MAAM,eAAe,GAAuB,WAAiC,CAAC;gBAC9E,UAAU,GAAG,CAAC,eAAe,CAAC,YAAY,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;YACvF,CAAC;iBAAM,IAAI,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;gBAC9D,WAAW;gBACX,uCAAuC;gBACvC,EAAE;gBACF,qBAAqB;gBACrB,yCAAyC;gBACzC,qBAAqB;gBACrB,sCAAsC;gBACtC,mCAAmC;gBACnC,wCAAwC;gBACxC,qCAAqC;gBACrC,oCAAoC;gBACpC,6BAA6B;gBAE7B,kFAAkF;gBAElF,MAAM,SAAS,GAAc,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;gBACjG,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;YAChF,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,iCAAa,CACrB,0CAA0C,WAAW,CAAC,OAAO,EAAE,IAAI;oBACjE,yDAA2B,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAC7D,CAAC;YACJ,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,CAAC,eAAe,EAAE,CAAC;gBACtC,MAAM,kBAAkB,GAAuB,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;gBAEjG,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;oBACrC,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE;wBAC7C,UAAU,EAAE,yBAAa,CAAC,WAAW;wBACrC,UAAU,EAAE,kBAAkB;wBAC9B,UAAU,EAAE,UAAU;wBACtB,UAAU,EAAE,KAAK;qBAClB,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,IAAI,CAAC,8BAA8B,CAAC,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;YAC/F,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,sBAAsB,CAC5B,SAAoB,EACpB,iBAA4B,EAC5B,WAA2B;QAE3B,MAAM,eAAe,GAAuB,IAAI,CAAC,sBAAsB,CACrE,SAAS,EACT,iBAAiB,EACjB,WAAW,CACZ,CAAC;QAEF,OAAO,IAAI,uCAAkB,CAAC;YAC5B,aAAa,EAAE,eAAe,CAAC,SAAS;YACxC,SAAS,EAAE,SAAS;YACpB,WAAW;YACX,MAAM,EAAE,iBAAiB;SAC1B,CAAC,CAAC;IACL,CAAC;IAEO,0BAA0B,CAChC,WAA2B,EAC3B,iBAA4B;QAE5B,MAAM,iBAAiB,GACrB,qCAAiB,CAAC,eAAe,CAAuB,WAAW,EAAE,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAExG,IAAI,iBAAiB,EAAE,CAAC;YACtB,MAAM,kBAAkB,GAAuB,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;YAEjG,IAAI,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;gBACvD,WAAW;gBACX,uCAAuC;gBACvC,EAAE;gBACF,qBAAqB;gBACrB,yCAAyC;gBACzC,kBAAkB;gBAClB,mDAAmD;gBACnD,wCAAwC;gBACxC,qCAAqC;gBACrC,0CAA0C;gBAC1C,qCAAqC;gBACrC,oCAAoC;gBACpC,6BAA6B;gBAE7B,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;oBACrC,MAAM,SAAS,GAAc,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;oBACjG,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;gBAChF,CAAC;gBAED,iGAAiG;gBACjG,4BAA4B;gBAC5B,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;oBACrC,UAAU,EAAE,yBAAa,CAAC,UAAU;oBACpC,UAAU,EAAE,iBAAiB,CAAC,IAAI;oBAClC,UAAU,EAAE,kBAAkB;oBAC9B,UAAU,EAAE,cAAc,CAAC,cAAc,CAAC,iBAAiB,CAAC;iBAC7D,CAAC,CAAC;YACL,CAAC;YAED,IAAI,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;gBACvD,WAAW;gBACX,oCAAoC;gBACpC,EAAE;gBACF,qBAAqB;gBACrB,yCAAyC;gBACzC,kBAAkB;gBAClB,oBAAoB;gBACpB,2CAA2C;gBAC3C,oBAAoB;gBACpB,uDAAuD;gBACvD,iCAAiC;gBACjC,uCAAuC;gBACvC,2BAA2B;gBAC3B,yCAAyC;gBACzC,0CAA0C;gBAC1C,qCAAqC;gBACrC,oCAAoC;gBACpC,6BAA6B;gBAE7B,wCAAwC;gBACxC,MAAM,eAAe,GAAuB,WAAiC,CAAC;gBAC9E,MAAM,UAAU,GAAW,CAAC,eAAe,CAAC,YAAY,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;gBAEnG,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;oBACrC,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE;wBAC7C,UAAU,EAAE,yBAAa,CAAC,WAAW;wBACrC,UAAU,EAAE,kBAAkB;wBAC9B,UAAU,EAAE,UAAU;wBACtB,UAAU,EAAE,cAAc,CAAC,cAAc,CAAC,iBAAiB,CAAC;qBAC7D,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,IAAI,CAAC,8BAA8B,CAAC,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;YAC/F,CAAC;iBAAM,IAAI,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC3D,WAAW;gBACX,gCAAgC;gBAChC,EAAE;gBACF,qBAAqB;gBACrB,yCAAyC;gBACzC,+DAA+D;gBAC/D,2BAA2B;gBAC3B,mCAAmC;gBACnC,oBAAoB;gBACpB,2CAA2C;gBAC3C,oBAAoB;gBACpB,2BAA2B;gBAC3B,yCAAyC;gBACzC,0CAA0C;gBAC1C,qCAAqC;gBACrC,gCAAgC;gBAChC,6BAA6B;gBAE7B,MAAM,YAAY,GAAoB,WAA8B,CAAC;gBACrE,MAAM,UAAU,GAAW,YAAY,CAAC,IAAI;oBAC1C,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE;oBACpC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAElC,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;oBACrC,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE;wBAC7C,UAAU,EAAE,yBAAa,CAAC,aAAa;wBACvC,UAAU,EAAE,kBAAkB;wBAC9B,UAAU;wBACV,UAAU,EAAE,cAAc,CAAC,cAAc,CAAC,iBAAiB,CAAC;qBAC7D,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,IAAI,CAAC,8BAA8B,CACxC,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAC7B,iBAAiB,EACjB,iBAAiB,CAClB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,iCAAa,CACrB,0CAA0C,WAAW,CAAC,OAAO,EAAE,IAAI;oBACjE,yDAA2B,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAC7D,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC,yBAAyB,CAAC,WAAW,CAAC,EAAE,CAAC;YAC9C,WAAW;YACX,oCAAoC;YACpC,EAAE;YACF,2BAA2B;YAC3B,yCAAyC;YACzC,qCAAqC;YACrC,sCAAsC;YACtC,6BAA6B;YAC7B,qCAAqC;YACrC,+BAA+B;YAC/B,qCAAqC;YACrC,gCAAgC;YAChC,6BAA6B;YAC7B,IAAI,EAAE,CAAC,yBAAyB,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC9D,IAAI,EAAE,CAAC,mBAAmB,CAAC,WAAW,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;oBACnE,MAAM,YAAY,GAAW,yCAAmB,CAAC,4BAA4B,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBAChG,MAAM,kBAAkB,GAAW,yCAAmB,CAAC,4BAA4B,CACjF,WAAW,CAAC,eAAe,CAAC,UAAU,CACvC,CAAC;oBAEF,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE;wBAC7C,UAAU,EAAE,yBAAa,CAAC,YAAY;wBACtC,UAAU,EAAE,kBAAkB;wBAC9B,UAAU,EAAE,YAAY;wBACxB,UAAU,EAAE,KAAK;qBAClB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,sBAAsB,CAC5B,SAAoB,EACpB,iBAA4B,EAC5B,WAA2B;QAE3B,IAAI,eAAe,GAAmC,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACtG,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,eAAe,GAAG,IAAI,uCAAkB,CAAC;gBACvC,aAAa,EAAE,iBAAiB,CAAC,IAAI;gBACrC,SAAS,EAAE,SAAS;gBACpB,WAAW,EAAE,WAAW;gBACxB,MAAM,EAAE,iBAAiB;aAC1B,CAAC,CAAC;YACH,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,iBAAuC;QACnE,IAAI,iBAAiB,CAAC,YAAY,EAAE,CAAC;YACnC,OAAO,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC;QACrD,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,8BAA8B,CACpC,UAAkB,EAClB,yBAAsE,EACtE,YAAuB;QAEvB,MAAM,kBAAkB,GAAc,IAAI,CAAC,wBAAwB,CACjE,yBAAyB,EACzB,YAAY,CACb,CAAC;QACF,MAAM,SAAS,GAAc,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;QACxF,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,qBAAqB,CAAC,UAAkB,EAAE,SAAoB;QACpE,MAAM,iBAAiB,GAAmB,IAAI,GAAG,EAAa,CAAC;QAC/D,MAAM,SAAS,GAA0B,IAAI,CAAC,wBAAwB,CACpE,UAAU,EACV,SAAS,EACT,iBAAiB,CAClB,CAAC;QACF,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,iCAAa,CACrB,gCAAgC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAClG,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,UAAkB,EAAE,SAAoB;QACrE,MAAM,iBAAiB,GAAmB,IAAI,GAAG,EAAa,CAAC;QAC/D,OAAO,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;IACjF,CAAC;IAEO,wBAAwB,CAC9B,UAAkB,EAClB,SAAoB,EACpB,iBAAiC;QAEjC,IAAI,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEjC,IAAI,SAAS,GAA0B,SAAS,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxF,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,2BAA2B;QAC3B,MAAM,iBAAiB,GAAgB,EAAE,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,YAAY,GAA0B,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAClG,IAAI,YAAY,EAAE,CAAC;gBACjB,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;gBAE9E,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC5B,SAAS,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,sBAAsB;oBACnF,OAAO,SAAS,CAAC;gBACnB,CAAC;YACH,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,KAAK,MAAM,kBAAkB,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAC/D,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YAE7F,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5B,IAAI,kBAAkB,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;oBACxD,mFAAmF;oBACnF,MAAM,SAAS,GAAc,SAAsB,CAAC;oBACpD,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,cAAc,EAAE;wBACpD,UAAU,EAAE,yBAAa,CAAC,WAAW;wBACrC,UAAU,EAAE,kBAAkB,CAAC,kBAAkB;wBACjD,UAAU,EAAE,UAAU;wBACtB,UAAU,EAAE,KAAK;qBAClB,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,yBAAyB,CAC/B,yBAA0F;QAE1F,MAAM,eAAe,GAAW,IAAI,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;QACpF,IAAI,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,EAAE,eAAe,CAAC,EAAE,CAAC;YAC3E,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;OAGG;IACK,wBAAwB,CAC9B,yBAAsE,EACtE,YAAuB;QAEvB,MAAM,eAAe,GAAW,IAAI,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;QACpF,MAAM,IAAI,GACR,yBAAyB,CAAC,eAAe;YACzC,EAAE,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,eAAe,CAAC;YAC/D,CAAC,CAAC,yCAAmB,CAAC,uBAAuB,CACzC,yBAAyB,CAAC,aAAa,EAAE,EACzC,yBAAyB,CAAC,eAAe,EACzC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CACnC;YACH,CAAC,CAAC,SAAS,CAAC;QAChB,MAAM,cAAc,GAAsC,yCAAmB,CAAC,iBAAiB,CAC7F,IAAI,CAAC,QAAQ,EACb,yBAAyB,CAAC,aAAa,EAAE,EACzC,eAAe,EACf,IAAI,CACL,CAAC;QAEF,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACjC,qEAAqE;YACrE,EAAE;YACF,sGAAsG;YACtG,wGAAwG;YACxG,4DAA4D;YAC5D,MAAM,IAAI,iCAAa,CACrB,qDAAqD,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI;gBACtF,yDAA2B,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,CAC3E,CAAC;QACJ,CAAC;QAED,oGAAoG;QACpG,wFAAwF;QACxF,MAAM,gBAAgB,GAA8B,IAAI,CAAC,QAAQ,CAAC,aAAa,CAC7E,cAAc,CAAC,gBAAgB,CAChC,CAAC;QACF,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,kGAAkG;YAClG,oDAAoD;YACpD,MAAM,IAAI,iCAAa,CACrB,oCAAoC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI;gBACrF,yDAA2B,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,CAC3E,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAY,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC;QACnG,MAAM,eAAe,GAAwB;YAC3C,eAAe,EAAE,eAAe;YAChC,qBAAqB,EAAE,YAAY;SACpC,CAAC;QACF,MAAM,kBAAkB,GAAc,IAAI,CAAC,4BAA4B,CACrE,gBAAgB,EAChB,eAAe,EACf,UAAU,CACX,CAAC;QAEF,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAEO,eAAe,CAAC,YAAmC,EAAE,OAA0B;QACrF,MAAM,GAAG,GAAW,qBAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,SAAS,GAA0B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEtE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,SAAS,GAAG,IAAI,qBAAS,CAAC,OAAO,CAAC,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAE1C,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,cAAc,GAAc,qCAAiB,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBAEnG,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;oBACxD,cAAc,EAAE,cAAc;oBAC9B,UAAU,EAAE,IAAI;oBAChB,sBAAsB,EAAE,KAAK;oBAC7B,YAAY,EAAE,IAAI;iBACnB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,4EAA4E;YAC5E,oDAAoD;YACpD,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBACxB,SAAS,CAAC,oBAAoB,GAAG,KAAK,CAAC;YACzC,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,mBAAmB,CACzB,yBAA0F;QAE1F,uGAAuG;QACvG,MAAM,eAAe,GACnB,qCAAiB,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QAElE,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,IAAI,iCAAa,CACrB,oCAAoC;gBAClC,yDAA2B,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,CAC3E,CAAC;QACJ,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;CACF;AAv7BD,wCAu7BC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as ts from 'typescript';\nimport { InternalError } from '@rushstack/node-core-library';\n\nimport { TypeScriptHelpers } from './TypeScriptHelpers';\nimport { AstSymbol } from './AstSymbol';\nimport { AstImport, type IAstImportOptions, AstImportKind } from './AstImport';\nimport { AstModule, type IAstModuleExportInfo } from './AstModule';\nimport { TypeScriptInternals } from './TypeScriptInternals';\nimport { SourceFileLocationFormatter } from './SourceFileLocationFormatter';\nimport type { IFetchAstSymbolOptions } from './AstSymbolTable';\nimport type { AstEntity } from './AstEntity';\nimport { AstNamespaceImport } from './AstNamespaceImport';\nimport { SyntaxHelpers } from './SyntaxHelpers';\nimport { AstNamespaceExport } from './AstNamespaceExport';\n\n/**\n * Exposes the minimal APIs from AstSymbolTable that are needed by ExportAnalyzer.\n *\n * In particular, we want ExportAnalyzer to be able to call AstSymbolTable._fetchAstSymbol() even though it\n * is a very private API that should not be exposed to any other components.\n */\nexport interface IAstSymbolTable {\n  fetchAstSymbol(options: IFetchAstSymbolOptions): AstSymbol | undefined;\n\n  analyze(astEntity: AstEntity): void;\n}\n\n/**\n * Used with ExportAnalyzer.fetchAstModuleBySourceFile() to provide contextual information about how the source file\n * was imported.\n */\ninterface IAstModuleReference {\n  /**\n   * For example, if we are following a statement like `import { X } from 'some-package'`, this will be the\n   * string `\"some-package\"`.\n   */\n  moduleSpecifier: string;\n\n  /**\n   * For example, if we are following a statement like `import { X } from 'some-package'`, this will be the\n   * symbol for `X`.\n   */\n  moduleSpecifierSymbol: ts.Symbol;\n}\n\n/**\n * The ExportAnalyzer is an internal part of AstSymbolTable that has been moved out into its own source file\n * because it is a complex and mostly self-contained algorithm.\n *\n * Its job is to build up AstModule objects by crawling import statements to discover where declarations come from.\n * This is conceptually the same as the compiler's own TypeChecker.getExportsOfModule(), except that when\n * ExportAnalyzer encounters a declaration that was imported from an external package, it remembers how it was imported\n * (i.e. the AstImport object).  Today the compiler API does not expose this information, which is crucial for\n * generating .d.ts rollups.\n */\nexport class ExportAnalyzer {\n  private readonly _program: ts.Program;\n  private readonly _typeChecker: ts.TypeChecker;\n  private readonly _bundledPackageNames: ReadonlySet<string>;\n  private readonly _astSymbolTable: IAstSymbolTable;\n\n  private readonly _astModulesByModuleSymbol: Map<ts.Symbol, AstModule> = new Map<ts.Symbol, AstModule>();\n\n  // Used with isImportableAmbientSourceFile()\n  private readonly _importableAmbientSourceFiles: Set<ts.SourceFile> = new Set<ts.SourceFile>();\n\n  private readonly _astImportsByKey: Map<string, AstImport> = new Map<string, AstImport>();\n  private readonly _astNamespaceImportByModule: Map<AstModule, AstNamespaceImport> = new Map();\n\n  public constructor(\n    program: ts.Program,\n    typeChecker: ts.TypeChecker,\n    bundledPackageNames: ReadonlySet<string>,\n    astSymbolTable: IAstSymbolTable\n  ) {\n    this._program = program;\n    this._typeChecker = typeChecker;\n    this._bundledPackageNames = bundledPackageNames;\n    this._astSymbolTable = astSymbolTable;\n  }\n\n  /**\n   * For a given source file, this analyzes all of its exports and produces an AstModule object.\n   *\n   * @param moduleReference - contextual information about the import statement that took us to this source file.\n   * or `undefined` if this source file is the initial entry point\n   * @param isExternal - whether the given `moduleReference` is external.\n   */\n  public fetchAstModuleFromSourceFile(\n    sourceFile: ts.SourceFile,\n    moduleReference: IAstModuleReference | undefined,\n    isExternal: boolean\n  ): AstModule {\n    const moduleSymbol: ts.Symbol = this._getModuleSymbolFromSourceFile(sourceFile, moduleReference);\n\n    // Don't traverse into a module that we already processed before:\n    // The compiler allows m1 to have \"export * from 'm2'\" and \"export * from 'm3'\",\n    // even if m2 and m3 both have \"export * from 'm4'\".\n    let astModule: AstModule | undefined = this._astModulesByModuleSymbol.get(moduleSymbol);\n    if (!astModule) {\n      // (If moduleReference === undefined, then this is the entry point of the local project being analyzed.)\n      const externalModulePath: string | undefined =\n        moduleReference !== undefined && isExternal ? moduleReference.moduleSpecifier : undefined;\n\n      astModule = new AstModule({ sourceFile, moduleSymbol, externalModulePath });\n\n      this._astModulesByModuleSymbol.set(moduleSymbol, astModule);\n\n      if (astModule.isExternal) {\n        // It's an external package, so do the special simplified analysis that doesn't crawl into referenced modules\n        for (const exportedSymbol of this._typeChecker.getExportsOfModule(moduleSymbol)) {\n          if (externalModulePath === undefined) {\n            throw new InternalError(\n              'Failed assertion: externalModulePath=undefined but astModule.isExternal=true'\n            );\n          }\n\n          const followedSymbol: ts.Symbol = TypeScriptHelpers.followAliases(\n            exportedSymbol,\n            this._typeChecker\n          );\n\n          // Ignore virtual symbols that don't have any declarations\n          const arbitraryDeclaration: ts.Declaration | undefined =\n            TypeScriptHelpers.tryGetADeclaration(followedSymbol);\n          if (arbitraryDeclaration) {\n            const astSymbol: AstSymbol | undefined = this._astSymbolTable.fetchAstSymbol({\n              followedSymbol: followedSymbol,\n              isExternal: astModule.isExternal,\n              includeNominalAnalysis: true,\n              addIfMissing: true\n            });\n\n            if (!astSymbol) {\n              throw new Error(\n                `Unsupported export ${JSON.stringify(exportedSymbol.name)}:\\n` +\n                  SourceFileLocationFormatter.formatDeclaration(arbitraryDeclaration)\n              );\n            }\n\n            astModule.cachedExportedEntities.set(exportedSymbol.name, astSymbol);\n          }\n        }\n      } else {\n        // The module is part of the local project, so do the full analysis\n\n        if (moduleSymbol.exports) {\n          // The \"export * from 'module-name';\" declarations are all attached to a single virtual symbol\n          // whose name is InternalSymbolName.ExportStar\n          const exportStarSymbol: ts.Symbol | undefined = moduleSymbol.exports.get(\n            ts.InternalSymbolName.ExportStar\n          );\n          if (exportStarSymbol) {\n            for (const exportStarDeclaration of exportStarSymbol.getDeclarations() || []) {\n              if (ts.isExportDeclaration(exportStarDeclaration)) {\n                const starExportedModule: AstModule | undefined = this._fetchSpecifierAstModule(\n                  exportStarDeclaration,\n                  exportStarSymbol\n                );\n\n                if (starExportedModule !== undefined) {\n                  astModule.starExportedModules.add(starExportedModule);\n                }\n              } else {\n                // Ignore ExportDeclaration nodes that don't match the expected pattern\n                // TODO: Should we report a warning?\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return astModule;\n  }\n\n  /**\n   * Retrieves the symbol for the module corresponding to the ts.SourceFile that is being imported/exported.\n   *\n   * @remarks\n   * The `module` keyword can be used to declare multiple TypeScript modules inside a single source file.\n   * (This is a deprecated construct and mainly used for typings such as `@types/node`.)  In this situation,\n   * `moduleReference` helps us to fish out the correct module symbol.\n   */\n  private _getModuleSymbolFromSourceFile(\n    sourceFile: ts.SourceFile,\n    moduleReference: IAstModuleReference | undefined\n  ): ts.Symbol {\n    const moduleSymbol: ts.Symbol | undefined = TypeScriptInternals.tryGetSymbolForDeclaration(\n      sourceFile,\n      this._typeChecker\n    );\n    if (moduleSymbol !== undefined) {\n      // This is the normal case.  The SourceFile acts is a module and has a symbol.\n      return moduleSymbol;\n    }\n\n    if (moduleReference !== undefined) {\n      // But there is also an elaborate case where the source file contains one or more \"module\" declarations,\n      // and our moduleReference took us to one of those.\n\n      // eslint-disable-next-line no-bitwise\n      if ((moduleReference.moduleSpecifierSymbol.flags & ts.SymbolFlags.Alias) !== 0) {\n        // Follow the import/export declaration to one hop the exported item inside the target module\n        let followedSymbol: ts.Symbol | undefined = TypeScriptInternals.getImmediateAliasedSymbol(\n          moduleReference.moduleSpecifierSymbol,\n          this._typeChecker\n        );\n\n        if (followedSymbol === undefined) {\n          // This is a workaround for a compiler bug where getImmediateAliasedSymbol() sometimes returns undefined\n          followedSymbol = this._typeChecker.getAliasedSymbol(moduleReference.moduleSpecifierSymbol);\n        }\n\n        if (followedSymbol !== undefined && followedSymbol !== moduleReference.moduleSpecifierSymbol) {\n          // The parent of the exported symbol will be the module that we're importing from\n          const parent: ts.Symbol | undefined = TypeScriptInternals.getSymbolParent(followedSymbol);\n          if (parent !== undefined) {\n            // Make sure the thing we found is a module\n            // eslint-disable-next-line no-bitwise\n            if ((parent.flags & ts.SymbolFlags.ValueModule) !== 0) {\n              // Record that that this is an ambient module that can also be imported from\n              this._importableAmbientSourceFiles.add(sourceFile);\n              return parent;\n            }\n          }\n        }\n      }\n    }\n\n    throw new InternalError('Unable to determine module for: ' + sourceFile.fileName);\n  }\n\n  /**\n   * Implementation of {@link AstSymbolTable.fetchAstModuleExportInfo}.\n   */\n  public fetchAstModuleExportInfo(entryPointAstModule: AstModule): IAstModuleExportInfo {\n    if (entryPointAstModule.isExternal) {\n      throw new Error('fetchAstModuleExportInfo() is not supported for external modules');\n    }\n\n    if (entryPointAstModule.astModuleExportInfo === undefined) {\n      const astModuleExportInfo: IAstModuleExportInfo = {\n        visitedAstModules: new Set<AstModule>(),\n        exportedLocalEntities: new Map<string, AstEntity>(),\n        starExportedExternalModules: new Set<AstModule>()\n      };\n\n      this._collectAllExportsRecursive(astModuleExportInfo, entryPointAstModule);\n\n      entryPointAstModule.astModuleExportInfo = astModuleExportInfo;\n    }\n    return entryPointAstModule.astModuleExportInfo;\n  }\n\n  /**\n   * Returns true if the module specifier refers to an external package.  Ignores packages listed in the\n   * \"bundledPackages\" setting from the api-extractor.json config file.\n   */\n  private _isExternalModulePath(\n    importOrExportDeclaration: ts.ImportDeclaration | ts.ExportDeclaration | ts.ImportTypeNode,\n    moduleSpecifier: string\n  ): boolean {\n    const specifier: ts.TypeNode | ts.Expression | undefined = ts.isImportTypeNode(importOrExportDeclaration)\n      ? importOrExportDeclaration.argument\n      : importOrExportDeclaration.moduleSpecifier;\n    const mode: ts.ModuleKind.CommonJS | ts.ModuleKind.ESNext | undefined =\n      specifier && ts.isStringLiteralLike(specifier)\n        ? TypeScriptInternals.getModeForUsageLocation(\n            importOrExportDeclaration.getSourceFile(),\n            specifier,\n            this._program.getCompilerOptions()\n          )\n        : undefined;\n\n    const resolvedModule: ts.ResolvedModuleFull | undefined = TypeScriptInternals.getResolvedModule(\n      this._program,\n      importOrExportDeclaration.getSourceFile(),\n      moduleSpecifier,\n      mode\n    );\n\n    if (resolvedModule === undefined) {\n      // The TS compiler API `getResolvedModule` cannot resolve ambient modules. Thus, to match API Extractor's\n      // previous behavior, simply treat all ambient modules as external. This bug is tracked by\n      // https://github.com/microsoft/rushstack/issues/3335.\n      return true;\n    }\n\n    // Either something like `jquery` or `@microsoft/api-extractor`.\n    const packageName: string | undefined = resolvedModule.packageId?.name;\n    if (packageName !== undefined && this._bundledPackageNames.has(packageName)) {\n      return false;\n    }\n\n    if (resolvedModule.isExternalLibraryImport === undefined) {\n      // This presumably means the compiler couldn't figure out whether the module was external, but we're not\n      // sure how this can happen.\n      throw new InternalError(\n        `Cannot determine whether the module ${JSON.stringify(moduleSpecifier)} is external\\n` +\n          SourceFileLocationFormatter.formatDeclaration(importOrExportDeclaration)\n      );\n    }\n\n    return resolvedModule.isExternalLibraryImport;\n  }\n\n  /**\n   * Returns true if when we analyzed sourceFile, we found that it contains an \"export=\" statement that allows\n   * it to behave /either/ as an ambient module /or/ as a regular importable module.  In this case,\n   * `AstSymbolTable._fetchAstSymbol()` will analyze its symbols even though `TypeScriptHelpers.isAmbient()`\n   * returns true.\n   */\n  public isImportableAmbientSourceFile(sourceFile: ts.SourceFile): boolean {\n    return this._importableAmbientSourceFiles.has(sourceFile);\n  }\n\n  private _collectAllExportsRecursive(astModuleExportInfo: IAstModuleExportInfo, astModule: AstModule): void {\n    const { visitedAstModules, starExportedExternalModules, exportedLocalEntities } = astModuleExportInfo;\n    if (visitedAstModules.has(astModule)) {\n      return;\n    }\n    visitedAstModules.add(astModule);\n\n    if (astModule.isExternal) {\n      starExportedExternalModules.add(astModule);\n    } else {\n      // Fetch each of the explicit exports for this module\n      if (astModule.moduleSymbol.exports) {\n        astModule.moduleSymbol.exports.forEach((exportSymbol, exportName) => {\n          switch (exportName) {\n            case ts.InternalSymbolName.ExportStar:\n            case ts.InternalSymbolName.ExportEquals:\n              break;\n            default:\n              // Don't collect the \"export default\" symbol unless this is the entry point module\n              if (exportName !== ts.InternalSymbolName.Default || visitedAstModules.size === 1) {\n                if (!exportedLocalEntities.has(exportSymbol.name)) {\n                  const astEntity: AstEntity = this._getExportOfAstModule(exportSymbol.name, astModule);\n\n                  if (astEntity instanceof AstSymbol && !astEntity.isExternal) {\n                    this._astSymbolTable.analyze(astEntity);\n                  }\n\n                  if (astEntity instanceof AstNamespaceImport && !astEntity.astModule.isExternal) {\n                    this._astSymbolTable.analyze(astEntity);\n                  }\n\n                  exportedLocalEntities.set(exportSymbol.name, astEntity);\n                }\n              }\n              break;\n          }\n        });\n      }\n\n      for (const starExportedModule of astModule.starExportedModules) {\n        this._collectAllExportsRecursive(astModuleExportInfo, starExportedModule);\n      }\n    }\n  }\n\n  /**\n   * For a given symbol (which was encountered in the specified sourceFile), this fetches the AstEntity that it\n   * refers to.  For example, if a particular interface describes the return value of a function, this API can help\n   * us determine a TSDoc declaration reference for that symbol (if the symbol is exported).\n   */\n  public fetchReferencedAstEntity(\n    symbol: ts.Symbol,\n    referringModuleIsExternal: boolean\n  ): AstEntity | undefined {\n    // eslint-disable-next-line no-bitwise\n    if ((symbol.flags & ts.SymbolFlags.FunctionScopedVariable) !== 0) {\n      // If a symbol refers back to part of its own definition, don't follow that rabbit hole\n      // Example:\n      //\n      // function f(x: number): typeof x {\n      //    return 123;\n      // }\n      return undefined;\n    }\n\n    let current: ts.Symbol = symbol;\n\n    if (referringModuleIsExternal) {\n      current = TypeScriptHelpers.followAliases(symbol, this._typeChecker);\n    } else {\n      for (;;) {\n        // Is this symbol an import/export that we need to follow to find the real declaration?\n        for (const declaration of current.declarations || []) {\n          let matchedAstEntity: AstEntity | undefined;\n          matchedAstEntity = this._tryMatchExportDeclaration(declaration, current);\n          if (matchedAstEntity !== undefined) {\n            return matchedAstEntity;\n          }\n          matchedAstEntity = this._tryMatchImportDeclaration(declaration, current);\n          if (matchedAstEntity !== undefined) {\n            return matchedAstEntity;\n          }\n        }\n\n        // eslint-disable-next-line no-bitwise\n        if (!(current.flags & ts.SymbolFlags.Alias)) {\n          break;\n        }\n\n        const currentAlias: ts.Symbol = TypeScriptInternals.getImmediateAliasedSymbol(\n          current,\n          this._typeChecker\n        );\n        // Stop if we reach the end of the chain\n        if (!currentAlias || currentAlias === current) {\n          break;\n        }\n\n        current = currentAlias;\n      }\n    }\n\n    // Otherwise, assume it is a normal declaration\n    const astSymbol: AstSymbol | undefined = this._astSymbolTable.fetchAstSymbol({\n      followedSymbol: current,\n      isExternal: referringModuleIsExternal,\n      includeNominalAnalysis: false,\n      addIfMissing: true\n    });\n\n    return astSymbol;\n  }\n\n  public fetchReferencedAstEntityFromImportTypeNode(\n    node: ts.ImportTypeNode,\n    referringModuleIsExternal: boolean\n  ): AstEntity | undefined {\n    const externalModulePath: string | undefined = this._tryGetExternalModulePath(node);\n\n    if (externalModulePath) {\n      let exportName: string;\n      if (node.qualifier) {\n        // Example input:\n        //   import('api-extractor-lib1-test').Lib1GenericType<number>\n        //\n        // Extracted qualifier:\n        //   Lib1GenericType\n        exportName = node.qualifier.getText().trim();\n      } else {\n        // Example input:\n        //   import('api-extractor-lib1-test')\n        //\n        // Extracted qualifier:\n        //   apiExtractorLib1Test\n\n        exportName = SyntaxHelpers.makeCamelCaseIdentifier(externalModulePath);\n      }\n\n      return this._fetchAstImport(undefined, {\n        importKind: AstImportKind.ImportType,\n        exportName: exportName,\n        modulePath: externalModulePath,\n        isTypeOnly: false\n      });\n    }\n\n    // Internal reference: AstSymbol\n    const rightMostToken: ts.Identifier | ts.ImportTypeNode = node.qualifier\n      ? node.qualifier.kind === ts.SyntaxKind.QualifiedName\n        ? node.qualifier.right\n        : node.qualifier\n      : node;\n\n    // There is no symbol property in a ImportTypeNode, obtain the associated export symbol\n    const exportSymbol: ts.Symbol | undefined = this._typeChecker.getSymbolAtLocation(rightMostToken);\n    if (!exportSymbol) {\n      throw new InternalError(\n        `Symbol not found for identifier: ${node.getText()}\\n` +\n          SourceFileLocationFormatter.formatDeclaration(node)\n      );\n    }\n\n    let followedSymbol: ts.Symbol = exportSymbol;\n    for (;;) {\n      const referencedAstEntity: AstEntity | undefined = this.fetchReferencedAstEntity(\n        followedSymbol,\n        referringModuleIsExternal\n      );\n\n      if (referencedAstEntity) {\n        return referencedAstEntity;\n      }\n\n      const followedSymbolNode: ts.Node | ts.ImportTypeNode | undefined =\n        followedSymbol.declarations && (followedSymbol.declarations[0] as ts.Node | undefined);\n\n      if (followedSymbolNode && followedSymbolNode.kind === ts.SyntaxKind.ImportType) {\n        return this.fetchReferencedAstEntityFromImportTypeNode(\n          followedSymbolNode as ts.ImportTypeNode,\n          referringModuleIsExternal\n        );\n      }\n\n      // eslint-disable-next-line no-bitwise\n      if (!(followedSymbol.flags & ts.SymbolFlags.Alias)) {\n        break;\n      }\n\n      const currentAlias: ts.Symbol = this._typeChecker.getAliasedSymbol(followedSymbol);\n      if (!currentAlias || currentAlias === followedSymbol) {\n        break;\n      }\n\n      followedSymbol = currentAlias;\n    }\n\n    const astSymbol: AstSymbol | undefined = this._astSymbolTable.fetchAstSymbol({\n      followedSymbol: followedSymbol,\n      isExternal: referringModuleIsExternal,\n      includeNominalAnalysis: false,\n      addIfMissing: true\n    });\n\n    return astSymbol;\n  }\n\n  private _tryMatchExportDeclaration(\n    declaration: ts.Declaration,\n    declarationSymbol: ts.Symbol\n  ): AstEntity | undefined {\n    const exportDeclaration: ts.ExportDeclaration | undefined =\n      TypeScriptHelpers.findFirstParent<ts.ExportDeclaration>(declaration, ts.SyntaxKind.ExportDeclaration);\n\n    if (exportDeclaration) {\n      let exportName: string | undefined = undefined;\n\n      if (declaration.kind === ts.SyntaxKind.ExportSpecifier) {\n        // EXAMPLE:\n        // \"export { A } from './file-a';\"\n        //\n        // ExportDeclaration:\n        //   ExportKeyword:  pre=[export] sep=[ ]\n        //   NamedExports:\n        //     FirstPunctuation:  pre=[{] sep=[ ]\n        //     SyntaxList:\n        //       ExportSpecifier:  <------------- declaration\n        //         Identifier:  pre=[A] sep=[ ]\n        //     CloseBraceToken:  pre=[}] sep=[ ]\n        //   FromKeyword:  pre=[from] sep=[ ]\n        //   StringLiteral:  pre=['./file-a']\n        //   SemicolonToken:  pre=[;]\n\n        // Example: \" ExportName as RenamedName\"\n        const exportSpecifier: ts.ExportSpecifier = declaration as ts.ExportSpecifier;\n        exportName = (exportSpecifier.propertyName || exportSpecifier.name).getText().trim();\n      } else if (declaration.kind === ts.SyntaxKind.NamespaceExport) {\n        // EXAMPLE:\n        // \"export * as theLib from 'the-lib';\"\n        //\n        // ExportDeclaration:\n        //   ExportKeyword:  pre=[export] sep=[ ]\n        //   NamespaceExport:\n        //     AsteriskToken:  pre=[*] sep=[ ]\n        //     AsKeyword:  pre=[as] sep=[ ]\n        //     Identifier:  pre=[theLib] sep=[ ]\n        //   FromKeyword:  pre=[from] sep=[ ]\n        //   StringLiteral:  pre=['the-lib']\n        //   SemicolonToken:  pre=[;]\n\n        // Issue tracking this feature: https://github.com/microsoft/rushstack/issues/2780\n\n        const astModule: AstModule = this._fetchSpecifierAstModule(exportDeclaration, declarationSymbol);\n        return this._getAstNamespaceExport(astModule, declarationSymbol, declaration);\n      } else {\n        throw new InternalError(\n          `Unimplemented export declaration kind: ${declaration.getText()}\\n` +\n            SourceFileLocationFormatter.formatDeclaration(declaration)\n        );\n      }\n\n      // Ignore \"export { A }\" without a module specifier\n      if (exportDeclaration.moduleSpecifier) {\n        const externalModulePath: string | undefined = this._tryGetExternalModulePath(exportDeclaration);\n\n        if (externalModulePath !== undefined) {\n          return this._fetchAstImport(declarationSymbol, {\n            importKind: AstImportKind.NamedImport,\n            modulePath: externalModulePath,\n            exportName: exportName,\n            isTypeOnly: false\n          });\n        }\n\n        return this._getExportOfSpecifierAstModule(exportName, exportDeclaration, declarationSymbol);\n      }\n    }\n\n    return undefined;\n  }\n\n  private _getAstNamespaceExport(\n    astModule: AstModule,\n    declarationSymbol: ts.Symbol,\n    declaration: ts.Declaration\n  ): AstNamespaceExport {\n    const imoprtNamespace: AstNamespaceImport = this._getAstNamespaceImport(\n      astModule,\n      declarationSymbol,\n      declaration\n    );\n\n    return new AstNamespaceExport({\n      namespaceName: imoprtNamespace.localName,\n      astModule: astModule,\n      declaration,\n      symbol: declarationSymbol\n    });\n  }\n\n  private _tryMatchImportDeclaration(\n    declaration: ts.Declaration,\n    declarationSymbol: ts.Symbol\n  ): AstEntity | undefined {\n    const importDeclaration: ts.ImportDeclaration | undefined =\n      TypeScriptHelpers.findFirstParent<ts.ImportDeclaration>(declaration, ts.SyntaxKind.ImportDeclaration);\n\n    if (importDeclaration) {\n      const externalModulePath: string | undefined = this._tryGetExternalModulePath(importDeclaration);\n\n      if (declaration.kind === ts.SyntaxKind.NamespaceImport) {\n        // EXAMPLE:\n        // \"import * as theLib from 'the-lib';\"\n        //\n        // ImportDeclaration:\n        //   ImportKeyword:  pre=[import] sep=[ ]\n        //   ImportClause:\n        //     NamespaceImport:  <------------- declaration\n        //       AsteriskToken:  pre=[*] sep=[ ]\n        //       AsKeyword:  pre=[as] sep=[ ]\n        //       Identifier:  pre=[theLib] sep=[ ]\n        //   FromKeyword:  pre=[from] sep=[ ]\n        //   StringLiteral:  pre=['the-lib']\n        //   SemicolonToken:  pre=[;]\n\n        if (externalModulePath === undefined) {\n          const astModule: AstModule = this._fetchSpecifierAstModule(importDeclaration, declarationSymbol);\n          return this._getAstNamespaceImport(astModule, declarationSymbol, declaration);\n        }\n\n        // Here importSymbol=undefined because {@inheritDoc} and such are not going to work correctly for\n        // a package or source file.\n        return this._fetchAstImport(undefined, {\n          importKind: AstImportKind.StarImport,\n          exportName: declarationSymbol.name,\n          modulePath: externalModulePath,\n          isTypeOnly: ExportAnalyzer._getIsTypeOnly(importDeclaration)\n        });\n      }\n\n      if (declaration.kind === ts.SyntaxKind.ImportSpecifier) {\n        // EXAMPLE:\n        // \"import { A, B } from 'the-lib';\"\n        //\n        // ImportDeclaration:\n        //   ImportKeyword:  pre=[import] sep=[ ]\n        //   ImportClause:\n        //     NamedImports:\n        //       FirstPunctuation:  pre=[{] sep=[ ]\n        //       SyntaxList:\n        //         ImportSpecifier:  <------------- declaration\n        //           Identifier:  pre=[A]\n        //         CommaToken:  pre=[,] sep=[ ]\n        //         ImportSpecifier:\n        //           Identifier:  pre=[B] sep=[ ]\n        //       CloseBraceToken:  pre=[}] sep=[ ]\n        //   FromKeyword:  pre=[from] sep=[ ]\n        //   StringLiteral:  pre=['the-lib']\n        //   SemicolonToken:  pre=[;]\n\n        // Example: \" ExportName as RenamedName\"\n        const importSpecifier: ts.ImportSpecifier = declaration as ts.ImportSpecifier;\n        const exportName: string = (importSpecifier.propertyName || importSpecifier.name).getText().trim();\n\n        if (externalModulePath !== undefined) {\n          return this._fetchAstImport(declarationSymbol, {\n            importKind: AstImportKind.NamedImport,\n            modulePath: externalModulePath,\n            exportName: exportName,\n            isTypeOnly: ExportAnalyzer._getIsTypeOnly(importDeclaration)\n          });\n        }\n\n        return this._getExportOfSpecifierAstModule(exportName, importDeclaration, declarationSymbol);\n      } else if (declaration.kind === ts.SyntaxKind.ImportClause) {\n        // EXAMPLE:\n        // \"import A, { B } from './A';\"\n        //\n        // ImportDeclaration:\n        //   ImportKeyword:  pre=[import] sep=[ ]\n        //   ImportClause:  <------------- declaration (referring to A)\n        //     Identifier:  pre=[A]\n        //     CommaToken:  pre=[,] sep=[ ]\n        //     NamedImports:\n        //       FirstPunctuation:  pre=[{] sep=[ ]\n        //       SyntaxList:\n        //         ImportSpecifier:\n        //           Identifier:  pre=[B] sep=[ ]\n        //       CloseBraceToken:  pre=[}] sep=[ ]\n        //   FromKeyword:  pre=[from] sep=[ ]\n        //   StringLiteral:  pre=['./A']\n        //   SemicolonToken:  pre=[;]\n\n        const importClause: ts.ImportClause = declaration as ts.ImportClause;\n        const exportName: string = importClause.name\n          ? importClause.name.getText().trim()\n          : ts.InternalSymbolName.Default;\n\n        if (externalModulePath !== undefined) {\n          return this._fetchAstImport(declarationSymbol, {\n            importKind: AstImportKind.DefaultImport,\n            modulePath: externalModulePath,\n            exportName,\n            isTypeOnly: ExportAnalyzer._getIsTypeOnly(importDeclaration)\n          });\n        }\n\n        return this._getExportOfSpecifierAstModule(\n          ts.InternalSymbolName.Default,\n          importDeclaration,\n          declarationSymbol\n        );\n      } else {\n        throw new InternalError(\n          `Unimplemented import declaration kind: ${declaration.getText()}\\n` +\n            SourceFileLocationFormatter.formatDeclaration(declaration)\n        );\n      }\n    }\n\n    if (ts.isImportEqualsDeclaration(declaration)) {\n      // EXAMPLE:\n      // import myLib = require('my-lib');\n      //\n      // ImportEqualsDeclaration:\n      //   ImportKeyword:  pre=[import] sep=[ ]\n      //   Identifier:  pre=[myLib] sep=[ ]\n      //   FirstAssignment:  pre=[=] sep=[ ]\n      //   ExternalModuleReference:\n      //     RequireKeyword:  pre=[require]\n      //     OpenParenToken:  pre=[(]\n      //     StringLiteral:  pre=['my-lib']\n      //     CloseParenToken:  pre=[)]\n      //   SemicolonToken:  pre=[;]\n      if (ts.isExternalModuleReference(declaration.moduleReference)) {\n        if (ts.isStringLiteralLike(declaration.moduleReference.expression)) {\n          const variableName: string = TypeScriptInternals.getTextOfIdentifierOrLiteral(declaration.name);\n          const externalModuleName: string = TypeScriptInternals.getTextOfIdentifierOrLiteral(\n            declaration.moduleReference.expression\n          );\n\n          return this._fetchAstImport(declarationSymbol, {\n            importKind: AstImportKind.EqualsImport,\n            modulePath: externalModuleName,\n            exportName: variableName,\n            isTypeOnly: false\n          });\n        }\n      }\n    }\n\n    return undefined;\n  }\n\n  private _getAstNamespaceImport(\n    astModule: AstModule,\n    declarationSymbol: ts.Symbol,\n    declaration: ts.Declaration\n  ): AstNamespaceImport {\n    let namespaceImport: AstNamespaceImport | undefined = this._astNamespaceImportByModule.get(astModule);\n    if (namespaceImport === undefined) {\n      namespaceImport = new AstNamespaceImport({\n        namespaceName: declarationSymbol.name,\n        astModule: astModule,\n        declaration: declaration,\n        symbol: declarationSymbol\n      });\n      this._astNamespaceImportByModule.set(astModule, namespaceImport);\n    }\n\n    return namespaceImport;\n  }\n\n  private static _getIsTypeOnly(importDeclaration: ts.ImportDeclaration): boolean {\n    if (importDeclaration.importClause) {\n      return !!importDeclaration.importClause.isTypeOnly;\n    }\n    return false;\n  }\n\n  private _getExportOfSpecifierAstModule(\n    exportName: string,\n    importOrExportDeclaration: ts.ImportDeclaration | ts.ExportDeclaration,\n    exportSymbol: ts.Symbol\n  ): AstEntity {\n    const specifierAstModule: AstModule = this._fetchSpecifierAstModule(\n      importOrExportDeclaration,\n      exportSymbol\n    );\n    const astEntity: AstEntity = this._getExportOfAstModule(exportName, specifierAstModule);\n    return astEntity;\n  }\n\n  private _getExportOfAstModule(exportName: string, astModule: AstModule): AstEntity {\n    const visitedAstModules: Set<AstModule> = new Set<AstModule>();\n    const astEntity: AstEntity | undefined = this._tryGetExportOfAstModule(\n      exportName,\n      astModule,\n      visitedAstModules\n    );\n    if (astEntity === undefined) {\n      throw new InternalError(\n        `Unable to analyze the export ${JSON.stringify(exportName)} in\\n` + astModule.sourceFile.fileName\n      );\n    }\n    return astEntity;\n  }\n\n  /**\n   * Implementation of {@link AstSymbolTable.tryGetExportOfAstModule}.\n   */\n  public tryGetExportOfAstModule(exportName: string, astModule: AstModule): AstEntity | undefined {\n    const visitedAstModules: Set<AstModule> = new Set<AstModule>();\n    return this._tryGetExportOfAstModule(exportName, astModule, visitedAstModules);\n  }\n\n  private _tryGetExportOfAstModule(\n    exportName: string,\n    astModule: AstModule,\n    visitedAstModules: Set<AstModule>\n  ): AstEntity | undefined {\n    if (visitedAstModules.has(astModule)) {\n      return undefined;\n    }\n    visitedAstModules.add(astModule);\n\n    let astEntity: AstEntity | undefined = astModule.cachedExportedEntities.get(exportName);\n    if (astEntity !== undefined) {\n      return astEntity;\n    }\n\n    // Try the explicit exports\n    const escapedExportName: ts.__String = ts.escapeLeadingUnderscores(exportName);\n    if (astModule.moduleSymbol.exports) {\n      const exportSymbol: ts.Symbol | undefined = astModule.moduleSymbol.exports.get(escapedExportName);\n      if (exportSymbol) {\n        astEntity = this.fetchReferencedAstEntity(exportSymbol, astModule.isExternal);\n\n        if (astEntity !== undefined) {\n          astModule.cachedExportedEntities.set(exportName, astEntity); // cache for next time\n          return astEntity;\n        }\n      }\n    }\n\n    // Try each of the star imports\n    for (const starExportedModule of astModule.starExportedModules) {\n      astEntity = this._tryGetExportOfAstModule(exportName, starExportedModule, visitedAstModules);\n\n      if (astEntity !== undefined) {\n        if (starExportedModule.externalModulePath !== undefined) {\n          // This entity was obtained from an external module, so return an AstImport instead\n          const astSymbol: AstSymbol = astEntity as AstSymbol;\n          return this._fetchAstImport(astSymbol.followedSymbol, {\n            importKind: AstImportKind.NamedImport,\n            modulePath: starExportedModule.externalModulePath,\n            exportName: exportName,\n            isTypeOnly: false\n          });\n        }\n\n        return astEntity;\n      }\n    }\n\n    return undefined;\n  }\n\n  private _tryGetExternalModulePath(\n    importOrExportDeclaration: ts.ImportDeclaration | ts.ExportDeclaration | ts.ImportTypeNode\n  ): string | undefined {\n    const moduleSpecifier: string = this._getModuleSpecifier(importOrExportDeclaration);\n    if (this._isExternalModulePath(importOrExportDeclaration, moduleSpecifier)) {\n      return moduleSpecifier;\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Given an ImportDeclaration of the form `export { X } from \"___\";`, this interprets the module specifier (`\"___\"`)\n   * and fetches the corresponding AstModule object.\n   */\n  private _fetchSpecifierAstModule(\n    importOrExportDeclaration: ts.ImportDeclaration | ts.ExportDeclaration,\n    exportSymbol: ts.Symbol\n  ): AstModule {\n    const moduleSpecifier: string = this._getModuleSpecifier(importOrExportDeclaration);\n    const mode: ts.ModuleKind.CommonJS | ts.ModuleKind.ESNext | undefined =\n      importOrExportDeclaration.moduleSpecifier &&\n      ts.isStringLiteralLike(importOrExportDeclaration.moduleSpecifier)\n        ? TypeScriptInternals.getModeForUsageLocation(\n            importOrExportDeclaration.getSourceFile(),\n            importOrExportDeclaration.moduleSpecifier,\n            this._program.getCompilerOptions()\n          )\n        : undefined;\n    const resolvedModule: ts.ResolvedModuleFull | undefined = TypeScriptInternals.getResolvedModule(\n      this._program,\n      importOrExportDeclaration.getSourceFile(),\n      moduleSpecifier,\n      mode\n    );\n\n    if (resolvedModule === undefined) {\n      // Encountered in https://github.com/microsoft/rushstack/issues/1914.\n      //\n      // It's also possible for this to occur with ambient modules. However, in practice this doesn't happen\n      // as API Extractor treats all ambient modules as external per the logic in `_isExternalModulePath`, and\n      // thus this code path is never reached for ambient modules.\n      throw new InternalError(\n        `getResolvedModule() could not resolve module name ${JSON.stringify(moduleSpecifier)}\\n` +\n          SourceFileLocationFormatter.formatDeclaration(importOrExportDeclaration)\n      );\n    }\n\n    // Map the filename back to the corresponding SourceFile. This circuitous approach is needed because\n    // we have no way to access the compiler's internal resolveExternalModuleName() function\n    const moduleSourceFile: ts.SourceFile | undefined = this._program.getSourceFile(\n      resolvedModule.resolvedFileName\n    );\n    if (!moduleSourceFile) {\n      // This should not happen, since getResolvedModule() specifically looks up names that the compiler\n      // found in export declarations for this source file\n      throw new InternalError(\n        `getSourceFile() failed to locate ${JSON.stringify(resolvedModule.resolvedFileName)}\\n` +\n          SourceFileLocationFormatter.formatDeclaration(importOrExportDeclaration)\n      );\n    }\n\n    const isExternal: boolean = this._isExternalModulePath(importOrExportDeclaration, moduleSpecifier);\n    const moduleReference: IAstModuleReference = {\n      moduleSpecifier: moduleSpecifier,\n      moduleSpecifierSymbol: exportSymbol\n    };\n    const specifierAstModule: AstModule = this.fetchAstModuleFromSourceFile(\n      moduleSourceFile,\n      moduleReference,\n      isExternal\n    );\n\n    return specifierAstModule;\n  }\n\n  private _fetchAstImport(importSymbol: ts.Symbol | undefined, options: IAstImportOptions): AstImport {\n    const key: string = AstImport.getKey(options);\n\n    let astImport: AstImport | undefined = this._astImportsByKey.get(key);\n\n    if (!astImport) {\n      astImport = new AstImport(options);\n      this._astImportsByKey.set(key, astImport);\n\n      if (importSymbol) {\n        const followedSymbol: ts.Symbol = TypeScriptHelpers.followAliases(importSymbol, this._typeChecker);\n\n        astImport.astSymbol = this._astSymbolTable.fetchAstSymbol({\n          followedSymbol: followedSymbol,\n          isExternal: true,\n          includeNominalAnalysis: false,\n          addIfMissing: true\n        });\n      }\n    } else {\n      // If we encounter at least one import that does not use the type-only form,\n      // then the .d.ts rollup will NOT use \"import type\".\n      if (!options.isTypeOnly) {\n        astImport.isTypeOnlyEverywhere = false;\n      }\n    }\n\n    return astImport;\n  }\n\n  private _getModuleSpecifier(\n    importOrExportDeclaration: ts.ImportDeclaration | ts.ExportDeclaration | ts.ImportTypeNode\n  ): string {\n    // The name of the module, which could be like \"./SomeLocalFile' or like 'external-package/entry/point'\n    const moduleSpecifier: string | undefined =\n      TypeScriptHelpers.getModuleSpecifier(importOrExportDeclaration);\n\n    if (!moduleSpecifier) {\n      throw new InternalError(\n        'Unable to parse module specifier\\n' +\n          SourceFileLocationFormatter.formatDeclaration(importOrExportDeclaration)\n      );\n    }\n\n    return moduleSpecifier;\n  }\n}\n"]}
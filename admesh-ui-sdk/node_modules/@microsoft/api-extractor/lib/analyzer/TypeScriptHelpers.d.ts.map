{"version": 3, "file": "TypeScriptHelpers.d.ts", "sourceRoot": "", "sources": ["../../src/analyzer/TypeScriptHelpers.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EAAE,MAAM,YAAY,CAAC;AAKjC,qBAAa,iBAAiB;IAG5B,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,0BAA0B,CAAwB;IAI1E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,uBAAuB,CAAyB;IAExE;;;;;;;;;;OAUG;WACW,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,MAAM;IAgBtF;;;OAGG;WACW,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC,WAAW,GAAG,OAAO;IAaxF;;;;OAIG;WACW,kBAAkB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,WAAW,GAAG,SAAS;IAO/E;;OAEG;WACW,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC,WAAW,GAAG,OAAO;IA8BhF;;;OAGG;WACW,uBAAuB,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,MAAM;WAexF,kBAAkB,CAC9B,uBAAuB,EAAE,EAAE,CAAC,iBAAiB,GAAG,EAAE,CAAC,iBAAiB,GAAG,EAAE,CAAC,cAAc,GACvF,MAAM,GAAG,SAAS;IA4BrB;;;;;;;;;;;;OAYG;WACW,aAAa,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,EAC3C,IAAI,EAAE,EAAE,CAAC,IAAI,EACb,YAAY,EAAE,EAAE,CAAC,UAAU,EAAE,GAC5B,CAAC,GAAG,SAAS;IAyBhB;;;OAGG;WACW,kBAAkB,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,EAChD,IAAI,EAAE,EAAE,CAAC,IAAI,EACb,WAAW,EAAE,EAAE,CAAC,UAAU,GACzB,CAAC,GAAG,SAAS;IAehB;;OAEG;WACW,eAAe,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,EAAE,CAAC,UAAU,GAAG,CAAC,GAAG,SAAS;IAa1G;;;;OAIG;WACW,iBAAiB,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,EAC/C,IAAI,EAAE,EAAE,CAAC,IAAI,EACb,WAAW,EAAE,EAAE,CAAC,UAAU,GACzB,CAAC,GAAG,SAAS;IAehB;;;;;;;OAOG;WACW,4BAA4B,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,GAAG,MAAM,GAAG,SAAS;IASjF;;OAEG;WACW,kBAAkB,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,GAAG,OAAO;IAI5D;;OAEG;WACW,mBAAmB,CAAC,eAAe,EAAE,EAAE,CAAC,oBAAoB,GAAG,MAAM,GAAG,SAAS;CAsBhG"}
{"version": 3, "file": "Span.js", "sourceRoot": "", "sources": ["../../src/analyzer/Span.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+CAAiC;AACjC,oEAAyE;AAEzE,iEAA8D;AAQ9D,IAAK,qBAiBJ;AAjBD,WAAK,qBAAqB;IACxB;;OAEG;IACH,yEAAY,CAAA;IACZ;;OAEG;IACH,mGAAyB,CAAA;IACzB;;OAEG;IACH,qGAA0B,CAAA;IAC1B;;OAEG;IACH,iEAAQ,CAAA;AACV,CAAC,EAjBI,qBAAqB,KAArB,qBAAqB,QAiBzB;AAED;;GAEG;AACH,IAAY,qBAeX;AAfD,WAAY,qBAAqB;IAC/B;;OAEG;IACH,iEAAQ,CAAA;IAER;;OAEG;IACH,6EAAc,CAAA;IAEd;;OAEG;IACH,uFAAmB,CAAA;AACrB,CAAC,EAfW,qBAAqB,qCAArB,qBAAqB,QAehC;AAED;;GAEG;AACH,MAAa,gBAAgB;IA4C3B,YAAmB,IAAU;QA3C7B;;;;WAIG;QACI,iBAAY,GAAY,KAAK,CAAC;QAErC;;WAEG;QACI,uBAAkB,GAAY,KAAK,CAAC;QAE3C;;;;WAIG;QACI,iBAAY,GAAY,KAAK,CAAC;QAOrC;;;;;;;;;;;;WAYG;QACI,qBAAgB,GAA0B,qBAAqB,CAAC,IAAI,CAAC;QAO1E,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACvE,CAAC;IAED,IAAW,MAAM,CAAC,KAAa;QAC7B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACvE,CAAC;IAED,IAAW,MAAM,CAAC,KAAa;QAC7B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YACnD,IAAI,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,eAAe,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,OAAO;QACZ,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjC,CAAC;CACF;AAhGD,4CAgGC;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAa,IAAI;IAkBf,YAAmB,IAAa;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACjG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,YAAY,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,iBAAiB,GAAqB,SAAS,CAAC;QAEpD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC;YACtD,MAAM,SAAS,GAAS,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;YACzB,SAAS,CAAC,gBAAgB,GAAG,iBAAiB,CAAC;YAE/C,IAAI,iBAAiB,EAAE,CAAC;gBACtB,iBAAiB,CAAC,YAAY,GAAG,SAAS,CAAC;YAC7C,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE9B,mEAAmE;YACnE,IAAI,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC3C,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;YACzC,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACvC,8EAA8E;gBAC9E,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;gBACnC,MAAM,IAAI,iCAAa,CAAC,qBAAqB,CAAC,CAAC;YACjD,CAAC;YAED,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,iBAAiB,CAAC,QAAQ,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;oBACtD,qFAAqF;oBACrF,kFAAkF;oBAClF,6EAA6E;oBAC7E,4FAA4F;oBAC5F,IAAI,kBAAkB,GAAS,iBAAiB,CAAC;oBACjD,OAAO,kBAAkB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC9C,MAAM,SAAS,GAAS,kBAAkB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBAC5F,IAAI,SAAS,CAAC,QAAQ,KAAK,kBAAkB,CAAC,QAAQ,EAAE,CAAC;4BACvD,+EAA+E;4BAC/E,2CAA2C;4BAC3C,MAAM;wBACR,CAAC;wBACD,kBAAkB,GAAG,SAAS,CAAC;oBACjC,CAAC;oBACD,kBAAkB,CAAC,oBAAoB,GAAG,iBAAiB,CAAC,QAAQ,CAAC;oBACrE,kBAAkB,CAAC,kBAAkB,GAAG,SAAS,CAAC,UAAU,CAAC;gBAC/D,CAAC;YACH,CAAC;YAED,iBAAiB,GAAG,SAAS,CAAC;QAChC,CAAC;IACH,CAAC;IAED,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACH,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACH,IAAW,eAAe;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACH,IAAW,WAAW;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACf,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACzB,mCAAmC;YACnC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAC1E,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM;QACf,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACzB,kCAAkC;YAClC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7F,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAChF,CAAC;IAED;;;OAGG;IACI,qBAAqB;QAC1B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;QACzE,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,WAA0B;QAC/C,IAAI,OAAO,GAAqB,IAAI,CAAC;QAErC,OAAO,OAAO,EAAE,CAAC;YACf,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBACjC,OAAO,OAAO,CAAC;YACjB,CAAC;YACD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,QAA8B;QAC3C,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,IAAI,MAAM,GAAW,EAAE,CAAC;QACxB,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QAEtB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QAC5B,CAAC;QAED,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QACtB,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC;QAEzB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,MAAM,MAAM,GAAmB,IAAI,+BAAc,EAAE,CAAC;QACpD,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,kBAAkB,CAAC;YACtB,MAAM,EAAE,MAAM;YACd,iBAAiB,EAAE,SAAS;YAC5B,qBAAqB,EAAE,qBAAqB,CAAC,QAAQ;SACtD,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC;IAEM,iBAAiB,CAAC,MAAsB;QAC7C,IAAI,CAAC,kBAAkB,CAAC;YACtB,MAAM,EAAE,MAAM;YACd,iBAAiB,EAAE,SAAS;YAC5B,qBAAqB,EAAE,qBAAqB,CAAC,QAAQ;SACtD,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,SAAiB,EAAE;QAChC,IAAI,MAAM,GAAW,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAEnE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAC3D,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAC3D,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;QAC9D,CAAC;QACD,MAAM,IAAI,IAAI,CAAC;QAEf,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,SAAiB,EAAE;QACxC,IAAI,MAAM,GAAW,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAEnE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QACxE,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QACxE,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;QAC9D,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,IAAI,EAAE,CAAC;YACtE,MAAM,IAAI,oBAAoB,GAAG,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAC7F,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACnC,MAAM,IAAI,eAAe,CAAC;QAC5B,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;YACzC,MAAM,IAAI,qBAAqB,CAAC;QAClC,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACnC,MAAM,IAAI,eAAe,CAAC;QAC5B,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC5C,MAAM,IAAI,aAAa,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC;QACtD,CAAC;QACD,MAAM,IAAI,IAAI,CAAC;QAEf,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACpC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,GAAG,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,cAAc,CAAC;QAC9D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAAkC;QAC3D,yCAAyC;QACzC,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YACxB,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QAClC,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC/B,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,IAAI,EAAE,CAAC;YACtE,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAE/C,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,UAAU,EAAE,CAAC;YAC5E,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,YAAgC,CAAC;QAErC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;gBACnC,6CAA6C;gBAC7C,MAAM,QAAQ,GAAW,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC;gBAE3F,iCAAiC;gBACjC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxB,YAAY,GAAG,QAAQ,CAAC;gBAC1B,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACjB,0FAA0F;YAC1F,oCAAoC;YAEpC,MAAM,iBAAiB,GAAW,YAAY,CAAC,MAAM,CAAC;YACtD,qDAAqD;YACrD,MAAM,cAAc,GAAW,YAAY,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;YACvE,MAAM,aAAa,GAAW,YAAY,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;YAE1F,wBAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAEzD,MAAM,YAAY,GAA8B,EAAE,GAAG,OAAO,EAAE,CAAC;YAE/D,IAAI,iBAAiB,GAAW,CAAC,CAAC;YAClC,KAAK,IAAI,KAAK,GAAW,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;gBAClE,IAAI,OAAa,CAAC;gBAElB,kCAAkC;gBAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;oBAC5D,iDAAiD;oBACjD,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC/B,YAAY,CAAC,iBAAiB,GAAG,SAAS,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACN,gDAAgD;oBAChD,OAAO,GAAG,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC;oBAE5C,IAAI,iBAAiB,GAAG,iBAAiB,EAAE,CAAC;wBAC1C,YAAY,CAAC,iBAAiB,GAAG,cAAc,CAAC;oBAClD,CAAC;yBAAM,CAAC;wBACN,YAAY,CAAC,iBAAiB,GAAG,aAAa,CAAC;oBACjD,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,8DAA8D;YAC9D,MAAM,cAAc,GAAW,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAEpD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;gBACpC,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;oBAC5C,6FAA6F;oBAC7F,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC;wBAChD,MAAM,KAAK,GAAS,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;wBAErC;wBACE,kFAAkF;wBAClF,kCAAkC;wBAClC,CAAC,GAAG,cAAc,GAAG,CAAC;4BACtB,qGAAqG;4BACrG,IAAI,CAAC,SAAS,EACd,CAAC;4BACD,MAAM,YAAY,GAA8B,EAAE,GAAG,OAAO,EAAE,CAAC;4BAC/D,YAAY,CAAC,iBAAiB,GAAG,SAAS,CAAC;4BAC3C,KAAK,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;wBACzC,CAAC;6BAAM,CAAC;4BACN,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;wBACpC,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,yBAAyB;oBACzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAClC,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;oBACpC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAE/C,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBAC5C,IAAI,IAAI,CAAC,SAAS,IAAI,cAAc,KAAK,CAAC,EAAE,CAAC;oBAC3C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;oBAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,eAAe,EAAE,CAAC;YACjF,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,OAAkC;QAC/D,IAAI,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,QAAQ,EAAE,CAAC;YACrE,MAAM,IAAI,iCAAa,CAAC,mCAAmC,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,qBAAqB,CAAC;IAC9E,CAAC;IAEO,oBAAoB,CAAC,OAAkC;QAC7D,IAAI,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,sBAAsB,EAAE,CAAC;YACnF,MAAM,IAAI,iCAAa,CAAC,0CAA0C,CAAC,CAAC;QACtE,CAAC;QACD,OAAO,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,QAAQ,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,IAAY,EAAE,OAAkC;QAC7D,IAAI,UAAU,GAAW,IAAI,CAAC;QAE9B,IAAI,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;YAClF,IAAI,KAAK,GAAW,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;gBACf,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC;gBACrB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;gBACrD,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACzC,OAAO,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,sBAAsB,CAAC;gBAE7E,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,sBAAsB,EAAE,CAAC;YACnF,IAAI,KAAK,GAAW,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;gBACf,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC;gBACrB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;gBACrD,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACzC,OAAO,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,IAAI,CAAC;gBAE3D,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAClC,CAAC;QACH,CAAC;QAED,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC;IAEO,WAAW,CAAC,IAAY;QAC9B,OAAO,wBAAI,CAAC,oBAAoB,CAAC,wBAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAChE,CAAC;IAEO,aAAa,CAAC,UAAkB,EAAE,QAAgB;QACxD,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;YAC5B,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACxE,CAAC;CACF;AArdD,oBAqdC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport * as ts from 'typescript';\nimport { InternalError, Sort, Text } from '@rushstack/node-core-library';\n\nimport { IndentedWriter } from '../generators/IndentedWriter';\n\ninterface IWriteModifiedTextOptions {\n  writer: IndentedWriter;\n  separatorOverride: string | undefined;\n  indentDocCommentState: IndentDocCommentState;\n}\n\nenum IndentDocCommentState {\n  /**\n   * `indentDocComment` was not requested for this subtree.\n   */\n  Inactive = 0,\n  /**\n   * `indentDocComment` was requested and we are looking for the opening `/` `*`\n   */\n  AwaitingOpenDelimiter = 1,\n  /**\n   * `indentDocComment` was requested and we are looking for the closing `*` `/`\n   */\n  AwaitingCloseDelimiter = 2,\n  /**\n   * `indentDocComment` was requested and we have finished indenting the comment.\n   */\n  Done = 3\n}\n\n/**\n * Choices for SpanModification.indentDocComment.\n */\nexport enum IndentDocCommentScope {\n  /**\n   * Do not detect and indent comments.\n   */\n  None = 0,\n\n  /**\n   * Look for one doc comment in the {@link Span.prefix} text only.\n   */\n  PrefixOnly = 1,\n\n  /**\n   * Look for one doc comment potentially distributed across the Span and its children.\n   */\n  SpanAndChildren = 2\n}\n\n/**\n * Specifies various transformations that will be performed by Span.getModifiedText().\n */\nexport class SpanModification {\n  /**\n   * If true, all of the child spans will be omitted from the Span.getModifiedText() output.\n   * @remarks\n   * Also, the modify() operation will not recurse into these spans.\n   */\n  public omitChildren: boolean = false;\n\n  /**\n   * If true, then the Span.separator will be removed from the Span.getModifiedText() output.\n   */\n  public omitSeparatorAfter: boolean = false;\n\n  /**\n   * If true, then Span.getModifiedText() will sort the immediate children according to their Span.sortKey\n   * property.  The separators will also be fixed up to ensure correct indentation.  If the Span.sortKey is undefined\n   * for some items, those items will not be moved, i.e. their array indexes will be unchanged.\n   */\n  public sortChildren: boolean = false;\n\n  /**\n   * Used if the parent span has Span.sortChildren=true.\n   */\n  public sortKey: string | undefined;\n\n  /**\n   * Optionally configures getModifiedText() to search for a \"/*\" doc comment and indent it.\n   * At most one comment is detected.\n   *\n   * @remarks\n   * The indentation can be applied to the `Span.modifier.prefix` only, or it can be applied to the\n   * full subtree of nodes (as needed for `ts.SyntaxKind.JSDocComment` trees).  However the enabled\n   * scopes must not overlap.\n   *\n   * This feature is enabled selectively because (1) we do not want to accidentally match `/*` appearing\n   * in a string literal or other expression that is not a comment, and (2) parsing comments is relatively\n   * expensive.\n   */\n  public indentDocComment: IndentDocCommentScope = IndentDocCommentScope.None;\n\n  private readonly _span: Span;\n  private _prefix: string | undefined;\n  private _suffix: string | undefined;\n\n  public constructor(span: Span) {\n    this._span = span;\n    this.reset();\n  }\n\n  /**\n   * Allows the Span.prefix text to be changed.\n   */\n  public get prefix(): string {\n    return this._prefix !== undefined ? this._prefix : this._span.prefix;\n  }\n\n  public set prefix(value: string) {\n    this._prefix = value;\n  }\n\n  /**\n   * Allows the Span.suffix text to be changed.\n   */\n  public get suffix(): string {\n    return this._suffix !== undefined ? this._suffix : this._span.suffix;\n  }\n\n  public set suffix(value: string) {\n    this._suffix = value;\n  }\n\n  /**\n   * Reverts any modifications made to this object.\n   */\n  public reset(): void {\n    this.omitChildren = false;\n    this.omitSeparatorAfter = false;\n    this.sortChildren = false;\n    this.sortKey = undefined;\n    this._prefix = undefined;\n    this._suffix = undefined;\n    if (this._span.kind === ts.SyntaxKind.JSDocComment) {\n      this.indentDocComment = IndentDocCommentScope.SpanAndChildren;\n    }\n  }\n\n  /**\n   * Effectively deletes the Span from the tree, by skipping its children, skipping its separator,\n   * and setting its prefix/suffix to the empty string.\n   */\n  public skipAll(): void {\n    this.prefix = '';\n    this.suffix = '';\n    this.omitChildren = true;\n    this.omitSeparatorAfter = true;\n  }\n}\n\n/**\n * The Span class provides a simple way to rewrite TypeScript source files\n * based on simple syntax transformations, i.e. without having to process deeper aspects\n * of the underlying grammar.  An example transformation might be deleting JSDoc comments\n * from a source file.\n *\n * @remarks\n * TypeScript's abstract syntax tree (AST) is represented using Node objects.\n * The Node text ignores its surrounding whitespace, and does not have an ordering guarantee.\n * For example, a JSDocComment node can be a child of a FunctionDeclaration node, even though\n * the actual comment precedes the function in the input stream.\n *\n * The Span class is a wrapper for a single Node, that provides access to every character\n * in the input stream, such that Span.getText() will exactly reproduce the corresponding\n * full Node.getText() output.\n *\n * A Span is comprised of these parts, which appear in sequential order:\n * - A prefix\n * - A collection of child spans\n * - A suffix\n * - A separator (e.g. whitespace between this span and the next item in the tree)\n *\n * These parts can be modified via Span.modification.  The modification is applied by\n * calling Span.getModifiedText().\n */\nexport class Span {\n  public readonly node: ts.Node;\n\n  // To improve performance, substrings are not allocated until actually needed\n  public readonly startIndex: number;\n  public readonly endIndex: number;\n\n  public readonly children: Span[];\n\n  public readonly modification: SpanModification;\n\n  private _parent: Span | undefined;\n  private _previousSibling: Span | undefined;\n  private _nextSibling: Span | undefined;\n\n  private _separatorStartIndex: number;\n  private _separatorEndIndex: number;\n\n  public constructor(node: ts.Node) {\n    this.node = node;\n    this.startIndex = node.kind === ts.SyntaxKind.SourceFile ? node.getFullStart() : node.getStart();\n    this.endIndex = node.end;\n    this._separatorStartIndex = 0;\n    this._separatorEndIndex = 0;\n    this.children = [];\n    this.modification = new SpanModification(this);\n\n    let previousChildSpan: Span | undefined = undefined;\n\n    for (const childNode of this.node.getChildren() || []) {\n      const childSpan: Span = new Span(childNode);\n      childSpan._parent = this;\n      childSpan._previousSibling = previousChildSpan;\n\n      if (previousChildSpan) {\n        previousChildSpan._nextSibling = childSpan;\n      }\n\n      this.children.push(childSpan);\n\n      // Normalize the bounds so that a child is never outside its parent\n      if (childSpan.startIndex < this.startIndex) {\n        this.startIndex = childSpan.startIndex;\n      }\n\n      if (childSpan.endIndex > this.endIndex) {\n        // This has never been observed empirically, but here's how we would handle it\n        this.endIndex = childSpan.endIndex;\n        throw new InternalError('Unexpected AST case');\n      }\n\n      if (previousChildSpan) {\n        if (previousChildSpan.endIndex < childSpan.startIndex) {\n          // There is some leftover text after previous child -- assign it as the separator for\n          // the preceding span.  If the preceding span has no suffix, then assign it to the\n          // deepest preceding span with no suffix.  This heuristic simplifies the most\n          // common transformations, and otherwise it can be fished out using getLastInnerSeparator().\n          let separatorRecipient: Span = previousChildSpan;\n          while (separatorRecipient.children.length > 0) {\n            const lastChild: Span = separatorRecipient.children[separatorRecipient.children.length - 1];\n            if (lastChild.endIndex !== separatorRecipient.endIndex) {\n              // There is a suffix, so we cannot push the separator any further down, or else\n              // it would get printed before this suffix.\n              break;\n            }\n            separatorRecipient = lastChild;\n          }\n          separatorRecipient._separatorStartIndex = previousChildSpan.endIndex;\n          separatorRecipient._separatorEndIndex = childSpan.startIndex;\n        }\n      }\n\n      previousChildSpan = childSpan;\n    }\n  }\n\n  public get kind(): ts.SyntaxKind {\n    return this.node.kind;\n  }\n\n  /**\n   * The parent Span, if any.\n   * NOTE: This will be undefined for a root Span, even though the corresponding Node\n   * may have a parent in the AST.\n   */\n  public get parent(): Span | undefined {\n    return this._parent;\n  }\n\n  /**\n   * If the current object is this.parent.children[i], then previousSibling corresponds\n   * to this.parent.children[i-1] if it exists.\n   * NOTE: This will be undefined for a root Span, even though the corresponding Node\n   * may have a previous sibling in the AST.\n   */\n  public get previousSibling(): Span | undefined {\n    return this._previousSibling;\n  }\n\n  /**\n   * If the current object is this.parent.children[i], then previousSibling corresponds\n   * to this.parent.children[i+1] if it exists.\n   * NOTE: This will be undefined for a root Span, even though the corresponding Node\n   * may have a previous sibling in the AST.\n   */\n  public get nextSibling(): Span | undefined {\n    return this._nextSibling;\n  }\n\n  /**\n   * The text associated with the underlying Node, up to its first child.\n   */\n  public get prefix(): string {\n    if (this.children.length) {\n      // Everything up to the first child\n      return this._getSubstring(this.startIndex, this.children[0].startIndex);\n    } else {\n      return this._getSubstring(this.startIndex, this.endIndex);\n    }\n  }\n\n  /**\n   * The text associated with the underlying Node, after its last child.\n   * If there are no children, this is always an empty string.\n   */\n  public get suffix(): string {\n    if (this.children.length) {\n      // Everything after the last child\n      return this._getSubstring(this.children[this.children.length - 1].endIndex, this.endIndex);\n    } else {\n      return '';\n    }\n  }\n\n  /**\n   * Whitespace that appeared after this node, and before the \"next\" node in the tree.\n   * Here we mean \"next\" according to an inorder traversal, not necessarily a sibling.\n   */\n  public get separator(): string {\n    return this._getSubstring(this._separatorStartIndex, this._separatorEndIndex);\n  }\n\n  /**\n   * Returns the separator of this Span, or else recursively calls getLastInnerSeparator()\n   * on the last child.\n   */\n  public getLastInnerSeparator(): string {\n    if (this.separator) {\n      return this.separator;\n    }\n    if (this.children.length > 0) {\n      return this.children[this.children.length - 1].getLastInnerSeparator();\n    }\n    return '';\n  }\n\n  /**\n   * Returns the first parent node with the specified  SyntaxKind, or undefined if there is no match.\n   */\n  public findFirstParent(kindToMatch: ts.SyntaxKind): Span | undefined {\n    let current: Span | undefined = this;\n\n    while (current) {\n      if (current.kind === kindToMatch) {\n        return current;\n      }\n      current = current.parent;\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Recursively invokes the callback on this Span and all its children.  The callback\n   * can make changes to Span.modification for each node.\n   */\n  public forEach(callback: (span: Span) => void): void {\n    callback(this);\n    for (const child of this.children) {\n      child.forEach(callback);\n    }\n  }\n\n  /**\n   * Returns the original unmodified text represented by this Span.\n   */\n  public getText(): string {\n    let result: string = '';\n    result += this.prefix;\n\n    for (const child of this.children) {\n      result += child.getText();\n    }\n\n    result += this.suffix;\n    result += this.separator;\n\n    return result;\n  }\n\n  /**\n   * Returns the text represented by this Span, after applying all requested modifications.\n   */\n  public getModifiedText(): string {\n    const writer: IndentedWriter = new IndentedWriter();\n    writer.trimLeadingSpaces = true;\n\n    this._writeModifiedText({\n      writer: writer,\n      separatorOverride: undefined,\n      indentDocCommentState: IndentDocCommentState.Inactive\n    });\n\n    return writer.getText();\n  }\n\n  public writeModifiedText(output: IndentedWriter): void {\n    this._writeModifiedText({\n      writer: output,\n      separatorOverride: undefined,\n      indentDocCommentState: IndentDocCommentState.Inactive\n    });\n  }\n\n  /**\n   * Returns a diagnostic dump of the tree, showing the prefix/suffix/separator for\n   * each node.\n   */\n  public getDump(indent: string = ''): string {\n    let result: string = indent + ts.SyntaxKind[this.node.kind] + ': ';\n\n    if (this.prefix) {\n      result += ' pre=[' + this._getTrimmed(this.prefix) + ']';\n    }\n    if (this.suffix) {\n      result += ' suf=[' + this._getTrimmed(this.suffix) + ']';\n    }\n    if (this.separator) {\n      result += ' sep=[' + this._getTrimmed(this.separator) + ']';\n    }\n    result += '\\n';\n\n    for (const child of this.children) {\n      result += child.getDump(indent + '  ');\n    }\n\n    return result;\n  }\n\n  /**\n   * Returns a diagnostic dump of the tree, showing the SpanModification settings for each nodde.\n   */\n  public getModifiedDump(indent: string = ''): string {\n    let result: string = indent + ts.SyntaxKind[this.node.kind] + ': ';\n\n    if (this.prefix) {\n      result += ' pre=[' + this._getTrimmed(this.modification.prefix) + ']';\n    }\n    if (this.suffix) {\n      result += ' suf=[' + this._getTrimmed(this.modification.suffix) + ']';\n    }\n    if (this.separator) {\n      result += ' sep=[' + this._getTrimmed(this.separator) + ']';\n    }\n    if (this.modification.indentDocComment !== IndentDocCommentScope.None) {\n      result += ' indentDocComment=' + IndentDocCommentScope[this.modification.indentDocComment];\n    }\n    if (this.modification.omitChildren) {\n      result += ' omitChildren';\n    }\n    if (this.modification.omitSeparatorAfter) {\n      result += ' omitSeparatorAfter';\n    }\n    if (this.modification.sortChildren) {\n      result += ' sortChildren';\n    }\n    if (this.modification.sortKey !== undefined) {\n      result += ` sortKey=\"${this.modification.sortKey}\"`;\n    }\n    result += '\\n';\n\n    if (!this.modification.omitChildren) {\n      for (const child of this.children) {\n        result += child.getModifiedDump(indent + '  ');\n      }\n    } else {\n      result += `${indent}  (${this.children.length} children)\\n`;\n    }\n\n    return result;\n  }\n\n  /**\n   * Recursive implementation of `getModifiedText()` and `writeModifiedText()`.\n   */\n  private _writeModifiedText(options: IWriteModifiedTextOptions): void {\n    // Apply indentation based on \"{\" and \"}\"\n    if (this.prefix === '{') {\n      options.writer.increaseIndent();\n    } else if (this.prefix === '}') {\n      options.writer.decreaseIndent();\n    }\n\n    if (this.modification.indentDocComment !== IndentDocCommentScope.None) {\n      this._beginIndentDocComment(options);\n    }\n\n    this._write(this.modification.prefix, options);\n\n    if (this.modification.indentDocComment === IndentDocCommentScope.PrefixOnly) {\n      this._endIndentDocComment(options);\n    }\n\n    let sortedSubset: Span[] | undefined;\n\n    if (!this.modification.omitChildren) {\n      if (this.modification.sortChildren) {\n        // We will only sort the items with a sortKey\n        const filtered: Span[] = this.children.filter((x) => x.modification.sortKey !== undefined);\n\n        // Is there at least one of them?\n        if (filtered.length > 1) {\n          sortedSubset = filtered;\n        }\n      }\n    }\n\n    if (sortedSubset) {\n      // This is the complicated special case that sorts an arbitrary subset of the child nodes,\n      // preserving the surrounding nodes.\n\n      const sortedSubsetCount: number = sortedSubset.length;\n      // Remember the separator for the first and last ones\n      const firstSeparator: string = sortedSubset[0].getLastInnerSeparator();\n      const lastSeparator: string = sortedSubset[sortedSubsetCount - 1].getLastInnerSeparator();\n\n      Sort.sortBy(sortedSubset, (x) => x.modification.sortKey);\n\n      const childOptions: IWriteModifiedTextOptions = { ...options };\n\n      let sortedSubsetIndex: number = 0;\n      for (let index: number = 0; index < this.children.length; ++index) {\n        let current: Span;\n\n        // Is this an item that we sorted?\n        if (this.children[index].modification.sortKey === undefined) {\n          // No, take the next item from the original array\n          current = this.children[index];\n          childOptions.separatorOverride = undefined;\n        } else {\n          // Yes, take the next item from the sortedSubset\n          current = sortedSubset[sortedSubsetIndex++];\n\n          if (sortedSubsetIndex < sortedSubsetCount) {\n            childOptions.separatorOverride = firstSeparator;\n          } else {\n            childOptions.separatorOverride = lastSeparator;\n          }\n        }\n\n        current._writeModifiedText(childOptions);\n      }\n    } else {\n      // This is the normal case that does not need to sort children\n      const childrenLength: number = this.children.length;\n\n      if (!this.modification.omitChildren) {\n        if (options.separatorOverride !== undefined) {\n          // Special case where the separatorOverride is passed down to the \"last inner separator\" span\n          for (let i: number = 0; i < childrenLength; ++i) {\n            const child: Span = this.children[i];\n\n            if (\n              // Only the last child inherits the separatorOverride, because only it can contain\n              // the \"last inner separator\" span\n              i < childrenLength - 1 ||\n              // If this.separator is specified, then we will write separatorOverride below, so don't pass it along\n              this.separator\n            ) {\n              const childOptions: IWriteModifiedTextOptions = { ...options };\n              childOptions.separatorOverride = undefined;\n              child._writeModifiedText(childOptions);\n            } else {\n              child._writeModifiedText(options);\n            }\n          }\n        } else {\n          // The normal simple case\n          for (const child of this.children) {\n            child._writeModifiedText(options);\n          }\n        }\n      }\n\n      this._write(this.modification.suffix, options);\n\n      if (options.separatorOverride !== undefined) {\n        if (this.separator || childrenLength === 0) {\n          this._write(options.separatorOverride, options);\n        }\n      } else {\n        if (!this.modification.omitSeparatorAfter) {\n          this._write(this.separator, options);\n        }\n      }\n    }\n\n    if (this.modification.indentDocComment === IndentDocCommentScope.SpanAndChildren) {\n      this._endIndentDocComment(options);\n    }\n  }\n\n  private _beginIndentDocComment(options: IWriteModifiedTextOptions): void {\n    if (options.indentDocCommentState !== IndentDocCommentState.Inactive) {\n      throw new InternalError('indentDocComment cannot be nested');\n    }\n    options.indentDocCommentState = IndentDocCommentState.AwaitingOpenDelimiter;\n  }\n\n  private _endIndentDocComment(options: IWriteModifiedTextOptions): void {\n    if (options.indentDocCommentState === IndentDocCommentState.AwaitingCloseDelimiter) {\n      throw new InternalError('missing \"*/\" delimiter for comment block');\n    }\n    options.indentDocCommentState = IndentDocCommentState.Inactive;\n  }\n\n  /**\n   * Writes one chunk of `text` to the `options.writer`, applying the `indentDocComment` rewriting.\n   */\n  private _write(text: string, options: IWriteModifiedTextOptions): void {\n    let parsedText: string = text;\n\n    if (options.indentDocCommentState === IndentDocCommentState.AwaitingOpenDelimiter) {\n      let index: number = parsedText.indexOf('/*');\n      if (index >= 0) {\n        index += '/*'.length;\n        options.writer.write(parsedText.substring(0, index));\n        parsedText = parsedText.substring(index);\n        options.indentDocCommentState = IndentDocCommentState.AwaitingCloseDelimiter;\n\n        options.writer.increaseIndent(' ');\n      }\n    }\n\n    if (options.indentDocCommentState === IndentDocCommentState.AwaitingCloseDelimiter) {\n      let index: number = parsedText.indexOf('*/');\n      if (index >= 0) {\n        index += '*/'.length;\n        options.writer.write(parsedText.substring(0, index));\n        parsedText = parsedText.substring(index);\n        options.indentDocCommentState = IndentDocCommentState.Done;\n\n        options.writer.decreaseIndent();\n      }\n    }\n\n    options.writer.write(parsedText);\n  }\n\n  private _getTrimmed(text: string): string {\n    return Text.truncateWithEllipsis(Text.convertToLf(text), 100);\n  }\n\n  private _getSubstring(startIndex: number, endIndex: number): string {\n    if (startIndex === endIndex) {\n      return '';\n    }\n    return this.node.getSourceFile().text.substring(startIndex, endIndex);\n  }\n}\n"]}
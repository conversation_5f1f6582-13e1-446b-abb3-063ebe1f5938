{"version": 3, "file": "TypeScriptInternals.js", "sourceRoot": "", "sources": ["../../src/analyzer/TypeScriptInternals.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,uDAAuD;AAEvD,+CAAiC;AACjC,oEAA6D;AAS7D,MAAa,mBAAmB;IACvB,MAAM,CAAC,yBAAyB,CAAC,MAAiB,EAAE,WAA2B;QACpF,qBAAqB;QACrB,8EAA8E;QAC9E,OAAQ,WAAmB,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,yDAAyD;IAC1H,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,0BAA0B,CACtC,WAA2B,EAC3B,OAAuB;QAEvB,IAAI,MAAM,GAA2B,WAAmB,CAAC,MAAM,CAAC;QAChE,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,KAAK,EAAE,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACpE,MAAM,IAAI,GAAmC,EAAE,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAClF,MAAM,GAAG,CAAC,IAAI,IAAI,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC;QACjE,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,iBAAiB,CAAC,MAAiB;QAC/C;QACE,sCAAsC;QACtC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS;YACtC,EAAU,CAAC,aAAa,CAAC,MAAM,CAAC,KAAM,EAAU,CAAC,UAAU,CAAC,IAAI,EACjE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,qBAAqB,CAAC,IAAa,EAAE,IAAY;QAC7D,qBAAqB;QACrB,qFAAqF;QAErF,OAAQ,EAAU,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,4BAA4B,CACxC,IAA8D;QAE9D,qBAAqB;QACrB,sFAAsF;QAEtF,OAAQ,EAAU,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,iBAAiB,CAC7B,OAAmB,EACnB,UAAyB,EACzB,cAAsB,EACtB,IAA+D;QAE/D,qBAAqB;QACrB,kFAAkF;QAClF,MAAM,MAAM,GAA4D,OAAe,CAAC,iBAAiB,CACvG,UAAU,EACV,cAAc,EACd,IAAI,CACL,CAAC;QACF,OAAO,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,cAAc,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,uBAAuB,CACnC,IAAmB,EACnB,KAA2B,EAC3B,eAAmC;QAEnC,qBAAqB;QACrB,mFAAmF;;QAEnF,OAAO,MAAA,EAAE,CAAC,uBAAuB,mDAAG,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe,CAAC,MAAiB;QAC7C,OAAQ,MAAc,CAAC,MAAM,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,iBAAiB,CAAC,WAA2B;QACzD,OAAQ,WAAmB,CAAC,WAAW,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,yBAAyB,CAAC,OAAmB;;QACzD,MAAM,UAAU,GAAQ,OAAO,CAAC;QAChC,MAAM,mBAAmB,GACvB,MAAA,UAAU,CAAC,kCAAkC,mCAAI,UAAU,CAAC,cAAc,CAAC;QAE7E,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,MAAM,IAAI,iCAAa,CAAC,8EAA8E,CAAC,CAAC;QAC1G,CAAC;QACD,MAAM,WAAW,GAAQ,mBAAmB,EAAE,CAAC;QAC/C,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;YACjC,MAAM,IAAI,iCAAa,CAAC,qCAAqC,CAAC,CAAC;QACjE,CAAC;QACD,MAAM,QAAQ,GAAQ,WAAW,CAAC,eAAe,EAAE,CAAC;QACpD,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC5B,MAAM,IAAI,iCAAa,CAAC,oCAAoC,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAU,CAAC,IAAyD;QAChF,0IAA0I;QAC1I,OAAQ,EAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;CACF;AA3ID,kDA2IC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON>IC<PERSON><PERSON> in the project root for license information.\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport * as ts from 'typescript';\nimport { InternalError } from '@rushstack/node-core-library';\n\n/**\n * Exposes the TypeScript compiler internals for detecting global variable names.\n */\nexport interface IGlobalVariableAnalyzer {\n  hasGlobalName(name: string): boolean;\n}\n\nexport class TypeScriptInternals {\n  public static getImmediateAliasedSymbol(symbol: ts.Symbol, typeChecker: ts.TypeChecker): ts.Symbol {\n    // Compiler internal:\n    // https://github.com/microsoft/TypeScript/blob/v3.2.2/src/compiler/checker.ts\n    return (typeChecker as any).getImmediateAliasedSymbol(symbol); // eslint-disable-line @typescript-eslint/no-explicit-any\n  }\n\n  /**\n   * Returns the Symbol for the provided Declaration.  This is a workaround for a missing\n   * feature of the TypeScript Compiler API.   It is the only apparent way to reach\n   * certain data structures, and seems to always work, but is not officially documented.\n   *\n   * @returns The associated Symbol.  If there is no semantic information (e.g. if the\n   * declaration is an extra semicolon somewhere), then \"undefined\" is returned.\n   */\n  public static tryGetSymbolForDeclaration(\n    declaration: ts.Declaration,\n    checker: ts.TypeChecker\n  ): ts.Symbol | undefined {\n    let symbol: ts.Symbol | undefined = (declaration as any).symbol;\n    if (symbol && symbol.escapedName === ts.InternalSymbolName.Computed) {\n      const name: ts.DeclarationName | undefined = ts.getNameOfDeclaration(declaration);\n      symbol = (name && checker.getSymbolAtLocation(name)) || symbol;\n    }\n    return symbol;\n  }\n\n  /**\n   * Returns whether the provided Symbol is a TypeScript \"late-bound\" Symbol (i.e. was created by the Checker\n   * for a computed property based on its type, rather than by the Binder).\n   */\n  public static isLateBoundSymbol(symbol: ts.Symbol): boolean {\n    if (\n      // eslint-disable-next-line no-bitwise\n      symbol.flags & ts.SymbolFlags.Transient &&\n      (ts as any).getCheckFlags(symbol) === (ts as any).CheckFlags.Late\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Retrieves the comment ranges associated with the specified node.\n   */\n  public static getJSDocCommentRanges(node: ts.Node, text: string): ts.CommentRange[] | undefined {\n    // Compiler internal:\n    // https://github.com/microsoft/TypeScript/blob/v2.4.2/src/compiler/utilities.ts#L616\n\n    return (ts as any).getJSDocCommentRanges.apply(this, arguments);\n  }\n\n  /**\n   * Retrieves the (unescaped) value of an string literal, numeric literal, or identifier.\n   */\n  public static getTextOfIdentifierOrLiteral(\n    node: ts.Identifier | ts.StringLiteralLike | ts.NumericLiteral\n  ): string {\n    // Compiler internal:\n    // https://github.com/microsoft/TypeScript/blob/v3.2.2/src/compiler/utilities.ts#L2721\n\n    return (ts as any).getTextOfIdentifierOrLiteral(node);\n  }\n\n  /**\n   * Retrieves the (cached) module resolution information for a module name that was exported from a SourceFile.\n   * The compiler populates this cache as part of analyzing the source file.\n   */\n  public static getResolvedModule(\n    program: ts.Program,\n    sourceFile: ts.SourceFile,\n    moduleNameText: string,\n    mode: ts.ModuleKind.CommonJS | ts.ModuleKind.ESNext | undefined\n  ): ts.ResolvedModuleFull | undefined {\n    // Compiler internal:\n    // https://github.com/microsoft/TypeScript/blob/v5.3.3/src/compiler/types.ts#L4698\n    const result: ts.ResolvedModuleWithFailedLookupLocations | undefined = (program as any).getResolvedModule(\n      sourceFile,\n      moduleNameText,\n      mode\n    );\n    return result?.resolvedModule;\n  }\n\n  /**\n   * Gets the mode required for module resolution required with the addition of Node16/nodenext\n   */\n  public static getModeForUsageLocation(\n    file: ts.SourceFile,\n    usage: ts.StringLiteralLike,\n    compilerOptions: ts.CompilerOptions\n  ): ts.ModuleKind.CommonJS | ts.ModuleKind.ESNext | undefined {\n    // Compiler internal:\n    // https://github.com/microsoft/TypeScript/blob/v5.8.2/src/compiler/program.ts#L931\n\n    return ts.getModeForUsageLocation?.(file, usage, compilerOptions);\n  }\n\n  /**\n   * Returns ts.Symbol.parent if it exists.\n   */\n  public static getSymbolParent(symbol: ts.Symbol): ts.Symbol | undefined {\n    return (symbol as any).parent;\n  }\n\n  /**\n   * In an statement like `export default class X { }`, the `Symbol.name` will be `default`\n   * whereas the `localSymbol` is `X`.\n   */\n  public static tryGetLocalSymbol(declaration: ts.Declaration): ts.Symbol | undefined {\n    return (declaration as any).localSymbol;\n  }\n\n  public static getGlobalVariableAnalyzer(program: ts.Program): IGlobalVariableAnalyzer {\n    const anyProgram: any = program;\n    const typeCheckerInstance: any =\n      anyProgram.getDiagnosticsProducingTypeChecker ?? anyProgram.getTypeChecker;\n\n    if (!typeCheckerInstance) {\n      throw new InternalError('Missing Program.getDiagnosticsProducingTypeChecker or Program.getTypeChecker');\n    }\n    const typeChecker: any = typeCheckerInstance();\n    if (!typeChecker.getEmitResolver) {\n      throw new InternalError('Missing TypeChecker.getEmitResolver');\n    }\n    const resolver: any = typeChecker.getEmitResolver();\n    if (!resolver.hasGlobalName) {\n      throw new InternalError('Missing EmitResolver.hasGlobalName');\n    }\n    return resolver;\n  }\n\n  /**\n   * Returns whether a variable is declared with the const keyword\n   */\n  public static isVarConst(node: ts.VariableDeclaration | ts.VariableDeclarationList): boolean {\n    // Compiler internal: https://github.com/microsoft/TypeScript/blob/71286e3d49c10e0e99faac360a6bbd40f12db7b6/src/compiler/utilities.ts#L925\n    return (ts as any).isVarConst(node);\n  }\n}\n"]}
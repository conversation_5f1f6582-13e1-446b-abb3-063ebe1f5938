{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;;;;;GAMG;AAEH,2DAA0D;AAAjD,oHAAA,gBAAgB,OAAA;AAEzB,qDAAsF;AAA7E,8GAAA,aAAa,OAAA;AAEtB,6CAA2F;AAAlF,sGAAA,SAAS,OAAA;AAAgC,4GAAA,eAAe,OAAA;AAEjE,yDAK+B;AAD7B,kHAAA,eAAe,OAAA;AAKjB,6DAA4D;AAAnD,sHAAA,iBAAiB,OAAA;AAE1B,2DAIgC;AAH9B,oHAAA,gBAAgB,OAAA;AAEhB,4HAAA,wBAAwB,OAAA;AAG1B,+DAA8D;AAArD,wHAAA,kBAAkB,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\n/**\n * API Extractor helps with validation, documentation, and reviewing of the exported API for a TypeScript library.\n * The `@microsoft/api-extractor` package provides the command-line tool.  It also exposes a developer API that you\n * can use to invoke API Extractor programmatically.\n *\n * @packageDocumentation\n */\n\nexport { ConsoleMessageId } from './api/ConsoleMessageId';\n\nexport { CompilerState, type ICompilerStateCreateOptions } from './api/CompilerState';\n\nexport { Extractor, type IExtractorInvokeOptions, ExtractorResult } from './api/Extractor';\n\nexport {\n  type IExtractorConfigApiReport,\n  type IExtractorConfigPrepareOptions,\n  type IExtractorConfigLoadForFolderOptions,\n  ExtractorConfig\n} from './api/ExtractorConfig';\n\nexport type { IApiModelGenerationOptions } from './generators/ApiModelGenerator';\n\nexport { ExtractorLogLevel } from './api/ExtractorLogLevel';\n\nexport {\n  ExtractorMessage,\n  type IExtractorMessageProperties,\n  ExtractorMessageCategory\n} from './api/ExtractorMessage';\n\nexport { ExtractorMessageId } from './api/ExtractorMessageId';\n\nexport type {\n  ApiReportVariant,\n  IConfigCompiler,\n  IConfigApiReport,\n  IConfigDocModel,\n  IConfigDtsRollup,\n  IConfigTsdocMetadata,\n  IConfigMessageReportingRule,\n  IConfigMessageReportingTable,\n  IExtractorMessagesConfig,\n  IConfigFile,\n  ReleaseTagForTrim\n} from './api/IConfigFile';\n"]}
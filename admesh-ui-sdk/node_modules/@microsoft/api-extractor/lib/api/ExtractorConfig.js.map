{"version": 3, "file": "ExtractorConfig.js", "sourceRoot": "", "sources": ["../../src/api/ExtractorConfig.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,2CAA6B;AAC7B,iDAAmC;AACnC,iCAAkC;AAClC,oEAWsC;AACtC,wDAAoE;AACpE,wEAA6E;AAC7E,4CAA0E;AAC1E,0DAA0D;AAQ1D,+EAA4E;AAC5E,8DAA2D;AAG3D,qGAAsE;AA8ItE,sDAAsD;AACtD,MAAM,wBAAwB,GAAgC,CAAC,UAAU,CAAC,CAAC;AAE3E;;;;;;;GAOG;AACH,MAAM,mBAAmB,GAA4C;IACnE,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,IAAI;IAChB,WAAW,EAAE,IAAI;IACjB,gBAAgB,EAAE,IAAI;IACtB,aAAa,EAAE,IAAI;CACpB,CAAC;AAqCF;;;;GAIG;AACH,MAAa,eAAe;IAyE1B;;;;OAIG;IACH,IAAW,cAAc;QACvB,MAAM,cAAc,GAA0C,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC9F,OAAO,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;IACnG,CAAC;IAED;;;;OAIG;IACH,IAAW,kBAAkB;QAC3B,MAAM,cAAc,GAA0C,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC9F,OAAO,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;IACvG,CAAC;IA4DD,YAAoB,EAClB,aAAa,EACb,WAAW,EACX,aAAa,EACb,sBAAsB,EACtB,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,gBAAgB,EAChB,gCAAgC,EAChC,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,yBAAyB,EACzB,eAAe,EACf,+BAA+B,EAC/B,gBAAgB,EAChB,aAAa,EACb,iBAAiB,EACjB,oBAAoB,EACpB,mBAAmB,EACnB,qBAAqB,EACrB,oBAAoB,EACpB,oBAAoB,EACpB,qBAAqB,EACrB,eAAe,EACf,kBAAkB,EAClB,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,eAAe,EACY;QAC3B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,gCAAgC,GAAG,gCAAgC,CAAC;QACzE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;QAC3D,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,+BAA+B,GAAG,+BAA+B,CAAC;QACvE,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;QACnD,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;QACnD,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAED;;;;;;;OAOG;IACI,iBAAiB;QACtB,6EAA6E;QAC7E,MAAM,MAAM,GAAW,6BAAa,CAAC,mBAAmB,CAAC,IAAI,EAAE;YAC7D,cAAc,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;SAC1D,CAAC,CAAC;QAEH,yEAAyE;QAEzE,8DAA8D;QAC7D,MAAc,CAAC,eAAe,GAAG;YAChC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ;YACvC,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;SAChE,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,YAAoB;QAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,iCAAa,CAAC,0BAA0B,GAAG,YAAY,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,wBAAI,CAAC,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1D,OAAO,wBAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,CAAC;QAChF,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACI,MAAM,CAAC,gBAAgB,CAC5B,OAA6C;QAE7C,MAAM,iBAAiB,GAAsB,OAAO,CAAC,iBAAiB,IAAI,IAAI,qCAAiB,EAAE,CAAC;QAClG,MAAM,cAAc,GAAW,OAAO,CAAC,cAAc,CAAC;QAEtD,qFAAqF;QACrF,MAAM,mBAAmB,GACvB,iBAAiB,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAAC;QACjE,MAAM,aAAa,GAAuB,mBAAmB;YAC3D,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;YACnC,CAAC,CAAC,SAAS,CAAC;QAEd,4DAA4D;QAC5D,MAAM,UAAU,GAAW,aAAa,IAAI,cAAc,CAAC;QAE3D,IAAI,wBAAwB,GAAuB,SAAS,CAAC;QAE7D,6CAA6C;QAC7C,IAAI,cAAc,GAAW,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;QACvF,IAAI,8BAAU,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;YACtC,IAAI,8BAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;gBACvE,MAAM,IAAI,KAAK,CAAC,qBAAqB,eAAe,CAAC,QAAQ,sCAAsC,CAAC,CAAC;YACvG,CAAC;QACH,CAAC;aAAM,CAAC;YACN,qCAAqC;YACrC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEjE,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;gBACvC,0GAA0G;gBAC1G,+BAA+B;gBAC/B,IAAI,aAAa,EAAE,CAAC;oBAClB,IAAI,SAAqB,CAAC;oBAC1B,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;wBACtB,mGAAmG;wBACnG,IAAI,CAAC,wBAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAAE,CAAC;4BACtE,MAAM,IAAI,KAAK,CACb,iFAAiF;gCAC/E,mBAAmB;gCACnB,aAAa;gCACb,mBAAmB;gCACnB,OAAO,CAAC,SAAS,CAAC,yBAAyB,CAC9C,CAAC;wBACJ,CAAC;wBACD,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;oBAChC,CAAC;yBAAM,CAAC;wBACN,SAAS,GAAG,uBAAS,CAAC,oBAAoB,CAAC;4BACzC,iBAAiB,EAAE,aAAa;yBACjC,CAAC,CAAC;oBACL,CAAC;oBAED,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;wBACvB,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,wBAAwB,EAAE,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;wBAE3F,2FAA2F;wBAC3F,kGAAkG;wBAClG,iGAAiG;wBACjG,iDAAiD;wBACjD,wBAAwB,GAAG,aAAa,CAAC;oBAC3C,CAAC;gBACH,CAAC;gBACD,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;oBACvC,+DAA+D;oBAC/D,OAAO,SAAS,CAAC;gBACnB,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,oBAAoB,GAAW,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAClE,MAAM,YAAY,GAAgB,eAAe,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QAEjF,OAAO;YACL,YAAY;YACZ,oBAAoB;YACpB,mBAAmB;YACnB,wBAAwB;SACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,kBAAkB,CAAC,kBAA0B;QACzD,MAAM,oBAAoB,GAAW,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACtE,MAAM,YAAY,GAAgB,eAAe,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QAEjF,MAAM,iBAAiB,GAAsB,IAAI,qCAAiB,EAAE,CAAC;QACrE,MAAM,mBAAmB,GACvB,iBAAiB,CAAC,4BAA4B,CAAC,oBAAoB,CAAC,CAAC;QAEvE,MAAM,eAAe,GAAoB,eAAe,CAAC,OAAO,CAAC;YAC/D,YAAY;YACZ,oBAAoB;YACpB,mBAAmB;SACpB,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,QAAQ,CAAC,YAAoB;QACzC,+DAA+D;QAC/D,MAAM,YAAY,GAAgB,IAAI,GAAG,EAAU,CAAC;QAEpD,IAAI,qBAAqB,GAAW,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC/D,IAAI,YAAY,GAAyB,EAAE,CAAC;QAE5C,yJAAyJ;QACzJ,mNAAmN;QACnN,uEAAuE;QACvE,MAAM,eAAe,GAA+B,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;YACzE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,OAAO,QAAQ,CAAC;YAClB,CAAC;YACD,uCAAuC;YACvC,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC;QAEF,IAAI,CAAC;YACH,GAAG,CAAC;gBACF,4CAA4C;gBAC5C,IAAI,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,CAAC;oBAC5C,MAAM,IAAI,KAAK,CACb,uDAAuD;wBACrD,mCAAmC,qBAAqB,GAAG,CAC9D,CAAC;gBACJ,CAAC;gBACD,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;gBAExC,MAAM,uBAAuB,GAAW,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;gBAE5E,yDAAyD;gBACzD,MAAM,UAAU,GAAgB,4BAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAErE,IAAI,YAAY,GAAW,UAAU,CAAC,OAAO,IAAI,EAAE,CAAC;gBAEpD,sDAAsD;gBACtD,OAAO,UAAU,CAAC,OAAO,CAAC;gBAE1B,IAAI,YAAY,EAAE,CAAC;oBACjB,IAAI,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;wBACtC,kDAAkD;wBAClD,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;oBACrE,CAAC;yBAAM,CAAC;wBACN,iDAAiD;wBACjD,EAAE;wBACF,mEAAmE;wBACnE,IAAI,CAAC;4BACH,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE;gCACxC,OAAO,EAAE,uBAAuB;6BACjC,CAAC,CAAC;wBACL,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,MAAM,IAAI,KAAK,CAAC,gCAAgC,YAAY,MAAO,CAAW,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC5F,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,uGAAuG;gBACvG,qCAAqC;gBACrC,eAAe,CAAC,+BAA+B,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;gBAErF,6DAA6D;gBAC7D,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;gBAC5D,YAAY,GAAG,UAAU,CAAC;gBAE1B,qBAAqB,GAAG,YAAY,CAAC;YACvC,CAAC,QAAQ,qBAAqB,EAAE;QAClC,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,iBAAiB,qBAAqB,KAAK,GAAI,CAAW,CAAC,OAAO,CAAC,CAAC;QACtF,CAAC;QAED,6BAA6B;QAC7B,YAAY,GAAG,MAAM,CAAC,SAAS,CAC7B,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,cAAc,CAAC,EAChD,YAAY,EACZ,eAAe,CAChB,CAAC;QAEF,eAAe,CAAC,UAAU,CAAC,cAAc,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QAEtE,+EAA+E;QAC/E,OAAO,YAA2B,CAAC;IACrC,CAAC;IAEO,MAAM,CAAC,+BAA+B,CAC5C,UAAuB,EACvB,uBAA+B;QAE/B,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;YAC7B,UAAU,CAAC,aAAa,GAAG,eAAe,CAAC,8BAA8B,CACvE,eAAe,EACf,UAAU,CAAC,aAAa,EACxB,uBAAuB,CACxB,CAAC;QACJ,CAAC;QAED,IAAI,UAAU,CAAC,sBAAsB,EAAE,CAAC;YACtC,UAAU,CAAC,sBAAsB,GAAG,eAAe,CAAC,8BAA8B,CAChF,wBAAwB,EACxB,UAAU,CAAC,sBAAsB,EACjC,uBAAuB,CACxB,CAAC;QACJ,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,IAAI,UAAU,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;gBACzC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,GAAG,eAAe,CAAC,8BAA8B,CACnF,kBAAkB,EAClB,UAAU,CAAC,QAAQ,CAAC,gBAAgB,EACpC,uBAAuB,CACxB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YACzB,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;gBACtC,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,eAAe,CAAC,8BAA8B,CAChF,cAAc,EACd,UAAU,CAAC,SAAS,CAAC,YAAY,EACjC,uBAAuB,CACxB,CAAC;YACJ,CAAC;YACD,IAAI,UAAU,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;gBAC1C,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,eAAe,CAAC,8BAA8B,CACpF,kBAAkB,EAClB,UAAU,CAAC,SAAS,CAAC,gBAAgB,EACrC,uBAAuB,CACxB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,IAAI,UAAU,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;gBACxC,UAAU,CAAC,QAAQ,CAAC,eAAe,GAAG,eAAe,CAAC,8BAA8B,CAClF,iBAAiB,EACjB,UAAU,CAAC,QAAQ,CAAC,eAAe,EACnC,uBAAuB,CACxB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YACzB,IAAI,UAAU,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;gBAC3C,UAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG,eAAe,CAAC,8BAA8B,CACrF,mBAAmB,EACnB,UAAU,CAAC,SAAS,CAAC,iBAAiB,EACtC,uBAAuB,CACxB,CAAC;YACJ,CAAC;YACD,IAAI,UAAU,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC;gBAC9C,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,eAAe,CAAC,8BAA8B,CACxF,sBAAsB,EACtB,UAAU,CAAC,SAAS,CAAC,oBAAoB,EACzC,uBAAuB,CACxB,CAAC;YACJ,CAAC;YACD,IAAI,UAAU,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;gBAC7C,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,eAAe,CAAC,8BAA8B,CACvF,qBAAqB,EACrB,UAAU,CAAC,SAAS,CAAC,mBAAmB,EACxC,uBAAuB,CACxB,CAAC;YACJ,CAAC;YACD,IAAI,UAAU,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;gBAC/C,UAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG,eAAe,CAAC,8BAA8B,CACzF,uBAAuB,EACvB,UAAU,CAAC,SAAS,CAAC,qBAAqB,EAC1C,uBAAuB,CACxB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;YAC7B,IAAI,UAAU,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;gBACnD,UAAU,CAAC,aAAa,CAAC,qBAAqB,GAAG,eAAe,CAAC,8BAA8B,CAC7F,uBAAuB,EACvB,UAAU,CAAC,aAAa,CAAC,qBAAqB,EAC9C,uBAAuB,CACxB,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,8BAA8B,CAC3C,SAAiB,EACjB,UAAkB,EAClB,uBAA+B;QAE/B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YACjC,IAAI,UAAU,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChD,kGAAkG;gBAClG,sDAAsD;gBACtD,OAAO,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,OAAO,CAAC,OAAuC;;QAC3D,MAAM,iBAAiB,GAAW,OAAO,CAAC,oBAAoB,IAAI,0BAA0B,CAAC;QAC7F,MAAM,YAAY,GAAyB,OAAO,CAAC,YAAY,CAAC;QAEhE,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CACb,yFAAyF,CAC1F,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBACnD,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;QAED,eAAe,CAAC,UAAU,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;QAE3E,MAAM,mBAAmB,GAAuB,OAAO,CAAC,mBAAmB,CAAC;QAC5E,IAAI,aAAa,GAAuB,SAAS,CAAC;QAClD,IAAI,WAAW,GAAiC,SAAS,CAAC;QAE1D,IAAI,mBAAmB,EAAE,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBACzC,uFAAuF;gBACvF,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;YAC5F,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAChF,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,MAAM,iBAAiB,GAAsB,IAAI,qCAAiB,EAAE,CAAC;gBACrE,WAAW,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;YAC3E,CAAC;YAED,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACpD,CAAC;QAED,uFAAuF;QACvF,mGAAmG;QACnG,IAAI,yBAAqG,CAAC;QAE1G,IAAI,CAAC;YACH,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC3B,0CAA0C;gBAC1C,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;gBAChC,0CAA0C;gBAC1C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,aAAqB,CAAC;YAC1B,IAAI,YAAY,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,UAAU,EAAE,CAAC;gBACrD,IAAI,OAAO,CAAC,wBAAwB,EAAE,CAAC;oBACrC,8CAA8C;oBAC9C,aAAa,GAAG,OAAO,CAAC,wBAAwB,CAAC;oBAEjD,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,EAAE,CAAC;wBACzD,MAAM,IAAI,KAAK,CACb,gEAAgE;4BAC9D,OAAO,CAAC,wBAAwB,CACnC,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;wBAClC,MAAM,IAAI,KAAK,CACb,0FAA0F;4BACxF,uDAAuD,CAC1D,CAAC;oBACJ,CAAC;oBAED,uGAAuG;oBACvG,qGAAqG;oBACrG,sGAAsG;oBACtG,6CAA6C;oBAE7C,IAAI,aAAa,GAAW,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;oBACvE,SAAS,CAAC;wBACR,MAAM,YAAY,GAAW,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;wBACvE,IAAI,8BAAU,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;4BACpC,aAAa,GAAG,aAAa,CAAC;4BAC9B,MAAM;wBACR,CAAC;wBACD,MAAM,YAAY,GAAW,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;wBACzD,IAAI,YAAY,KAAK,EAAE,IAAI,YAAY,KAAK,aAAa,EAAE,CAAC;4BAC1D,MAAM,IAAI,KAAK,CACb,2FAA2F;gCACzF,6CAA6C,CAChD,CAAC;wBACJ,CAAC;wBACD,aAAa,GAAG,YAAY,CAAC;oBAC/B,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,eAAe,CAAC,sBAAsB,CAAC,YAAY,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;gBAEpF,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,CAAC;oBACnD,MAAM,IAAI,KAAK,CAAC,qDAAqD,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;gBACtG,CAAC;gBAED,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;YAC7C,CAAC;YAED,MAAM,YAAY,GAAiC;gBACjD,mBAAmB,EAAE,iBAAiB;gBACtC,WAAW,EAAE,iBAAiB;gBAC9B,aAAa,EAAE,aAAa;aAC7B,CAAC;YAEF,IAAI,WAAW,EAAE,CAAC;gBAChB,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC;gBAC5C,YAAY,CAAC,mBAAmB,GAAG,+BAAW,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACnF,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,sBAAsB,EAAE,CAAC;gBACzC,0CAA0C;gBAC1C,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;YACrE,CAAC;YACD,MAAM,sBAAsB,GAAW,eAAe,CAAC,sBAAsB,CAC3E,wBAAwB,EACxB,YAAY,CAAC,sBAAsB,EACnC,YAAY,CACb,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBACjE,MAAM,IAAI,KAAK,CACb,gEAAgE,GAAG,sBAAsB,CAC1F,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBACnF,MAAM,IAAI,KAAK,CAAC,oDAAoD,GAAG,sBAAsB,CAAC,CAAC;YACjG,CAAC;YAED,MAAM,eAAe,GAAa,YAAY,CAAC,eAAe,IAAI,EAAE,CAAC;YAErE,8FAA8F;YAC9F,qHAAqH;YAErH,MAAM,gBAAgB,GAAW,eAAe,CAAC,sBAAsB,CACrE,kBAAkB,EAClB,YAAY,CAAC,QAAQ,CAAC,gBAAgB,EACtC,YAAY,CACb,CAAC;YAEF,IAAI,YAAY,CAAC,QAAQ,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;gBACzD,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,MAAM,IAAI,KAAK,CAAC,+EAA+E,CAAC,CAAC;gBACnG,CAAC;gBACD,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBACzC,MAAM,IAAI,KAAK,CAAC,4DAA4D,GAAG,gBAAgB,CAAC,CAAC;gBACnG,CAAC;YACH,CAAC;YAED,IAAI,MAAA,YAAY,CAAC,SAAS,0CAAE,YAAY,EAAE,CAAC;gBACzC,qBAAqB,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,gBAAgB,GAAY,MAAA,MAAA,YAAY,CAAC,SAAS,0CAAE,OAAO,mCAAI,KAAK,CAAC;YAC3E,MAAM,gCAAgC,GACpC,MAAA,MAAA,YAAY,CAAC,SAAS,0CAAE,uBAAuB,mCAAI,KAAK,CAAC;YAC3D,IAAI,YAAY,GAAW,YAAY,CAAC,aAAa,CAAC;YACtD,IAAI,gBAAgB,GAAW,YAAY,CAAC,aAAa,CAAC;YAC1D,MAAM,aAAa,GAAgC,EAAE,CAAC;YACtD,IAAI,YAAY,GAAkC,EAAE,CAAC;YACrD,IAAI,gBAAgB,EAAE,CAAC;gBACrB,kEAAkE;gBAClE,MAAM,eAAe,GAAqB,YAAY,CAAC,SAAU,CAAC;gBAElE,MAAM,oBAAoB,GAAW,SAAS,CAAC;gBAC/C,IAAI,kBAA0B,CAAC;gBAC/B,IAAI,eAAe,CAAC,cAAc,EAAE,CAAC;oBACnC,IACE,eAAe,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;wBAChD,eAAe,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EACjD,CAAC;wBACD,MAAM,IAAI,KAAK,CACb,8DAA8D,eAAe,CAAC,cAAc,GAAG,CAChG,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;wBACnE,4EAA4E;wBAC5E,kBAAkB,GAAG,eAAe,CAAC,cAAc,CAAC;oBACtD,CAAC;yBAAM,CAAC;wBACN,6GAA6G;wBAC7G,wGAAwG;wBACxG,yEAAyE;wBACzE,8FAA8F;wBAC9F,yBAAyB;wBACzB,kBAAkB,GAAG,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;oBAC7F,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,gBAAgB;oBAChB,kBAAkB,GAAG,uBAAuB,CAAC;gBAC/C,CAAC;gBAED,MAAM,kBAAkB,GACtB,MAAA,eAAe,CAAC,cAAc,mCAAI,wBAAwB,CAAC;gBAE7D,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE,CAAC;oBACnD,6GAA6G;oBAC7G,MAAM,kBAAkB,GAAW,GAAG,kBAAkB,GACtD,iBAAiB,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,iBAAiB,EAC/D,GAAG,oBAAoB,EAAE,CAAC;oBAC1B,MAAM,kBAAkB,GAAW,eAAe,CAAC,uBAAuB,CACxE,gBAAgB,EAChB,kBAAkB,EAClB,YAAY,CACb,CAAC;oBAEF,aAAa,CAAC,IAAI,CAAC;wBACjB,QAAQ,EAAE,kBAAkB;wBAC5B,OAAO,EAAE,iBAAiB;qBAC3B,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,eAAe,CAAC,YAAY,EAAE,CAAC;oBACjC,YAAY,GAAG,eAAe,CAAC,sBAAsB,CACnD,cAAc,EACd,eAAe,CAAC,YAAY,EAC5B,YAAY,CACb,CAAC;gBACJ,CAAC;gBAED,IAAI,eAAe,CAAC,gBAAgB,EAAE,CAAC;oBACrC,gBAAgB,GAAG,eAAe,CAAC,sBAAsB,CACvD,kBAAkB,EAClB,eAAe,CAAC,gBAAgB,EAChC,YAAY,CACb,CAAC;gBACJ,CAAC;gBAED,YAAY,GAAG;oBACb,GAAG,mBAAmB;oBACtB,GAAG,eAAe,CAAC,YAAY;iBAChC,CAAC;YACJ,CAAC;YAED,IAAI,yBAAyB,GAA2C,SAAS,CAAC;YAClF,IAAI,eAAe,GAAW,EAAE,CAAC;YACjC,IAAI,+BAA+B,GAAY,KAAK,CAAC;YACrD,IAAI,gBAAoC,CAAC;YACzC,IAAI,MAAA,YAAY,CAAC,QAAQ,0CAAE,OAAO,EAAE,CAAC;gBACnC,eAAe,GAAG,eAAe,CAAC,sBAAsB,CACtD,iBAAiB,EACjB,YAAY,CAAC,QAAQ,CAAC,eAAe,EACrC,YAAY,CACb,CAAC;gBACF,+BAA+B,GAAG,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,uBAAuB,CAAC;gBAClF,gBAAgB,GAAG,YAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gBAE1D,MAAM,iBAAiB,GAAoB,IAAI,GAAG,EAAc,CAAC;gBACjE,MAAM,uBAAuB,GAAa,YAAY,CAAC,QAAQ,CAAC,iBAAiB,IAAI,CAAC,WAAW,CAAC,CAAC;gBACnG,KAAK,MAAM,gBAAgB,IAAI,uBAAuB,EAAE,CAAC;oBACvD,IAAI,UAAsB,CAAC;oBAC3B,QAAQ,gBAAgB,EAAE,CAAC;wBACzB,KAAK,WAAW,CAAC,CAAC,CAAC;4BACjB,UAAU,GAAG,gCAAU,CAAC,QAAQ,CAAC;4BACjC,MAAM;wBACR,CAAC;wBAED,KAAK,QAAQ,CAAC,CAAC,CAAC;4BACd,UAAU,GAAG,gCAAU,CAAC,KAAK,CAAC;4BAC9B,MAAM;wBACR,CAAC;wBAED,KAAK,OAAO,CAAC,CAAC,CAAC;4BACb,UAAU,GAAG,gCAAU,CAAC,IAAI,CAAC;4BAC7B,MAAM;wBACR,CAAC;wBAED,KAAK,SAAS,CAAC,CAAC,CAAC;4BACf,UAAU,GAAG,gCAAU,CAAC,MAAM,CAAC;4BAC/B,MAAM;wBACR,CAAC;wBAED,OAAO,CAAC,CAAC,CAAC;4BACR,MAAM,IAAI,KAAK,CAAC,oBAAoB,gBAAgB,oBAAoB,CAAC,CAAC;wBAC5E,CAAC;oBACH,CAAC;oBAED,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACpC,CAAC;gBAED,yBAAyB,GAAG;oBAC1B,iBAAiB;iBAClB,CAAC;YACJ,CAAC;YAED,IAAI,oBAAoB,GAAY,KAAK,CAAC;YAC1C,IAAI,qBAAqB,GAAW,EAAE,CAAC;YACvC,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC;gBAC/B,oBAAoB,GAAG,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC;gBAE5D,IAAI,oBAAoB,EAAE,CAAC;oBACzB,qBAAqB,GAAG,YAAY,CAAC,aAAa,CAAC,qBAAqB,IAAI,EAAE,CAAC;oBAE/E,IAAI,qBAAqB,CAAC,IAAI,EAAE,KAAK,UAAU,EAAE,CAAC;wBAChD,IAAI,CAAC,WAAW,EAAE,CAAC;4BACjB,MAAM,IAAI,KAAK,CACb,sFAAsF;gCACpF,4CAA4C,CAC/C,CAAC;wBACJ,CAAC;wBACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;4BACzB,MAAM,IAAI,KAAK,CACb,0EAA0E;gCACxE,mDAAmD,CACtD,CAAC;wBACJ,CAAC;wBACD,qBAAqB,GAAG,+CAAsB,CAAC,wBAAwB,CACrE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,EACjC,WAAW,CACZ,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,qBAAqB,GAAG,eAAe,CAAC,sBAAsB,CAC5D,uBAAuB,EACvB,YAAY,CAAC,aAAa,CAAC,qBAAqB,EAChD,YAAY,CACb,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;wBAC3B,MAAM,IAAI,KAAK,CACb,iDAAiD;4BAC/C,+CAA+C,CAClD,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,aAAa,GAAY,KAAK,CAAC;YACnC,IAAI,iBAAiB,GAAW,EAAE,CAAC;YACnC,IAAI,mBAAmB,GAAW,EAAE,CAAC;YACrC,IAAI,oBAAoB,GAAW,EAAE,CAAC;YACtC,IAAI,qBAAqB,GAAW,EAAE,CAAC;YACvC,IAAI,oBAAoB,GAAY,KAAK,CAAC;YAE1C,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC;gBAC3B,aAAa,GAAG,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC;gBACjD,iBAAiB,GAAG,eAAe,CAAC,sBAAsB,CACxD,mBAAmB,EACnB,YAAY,CAAC,SAAS,CAAC,iBAAiB,EACxC,YAAY,CACb,CAAC;gBACF,oBAAoB,GAAG,eAAe,CAAC,sBAAsB,CAC3D,sBAAsB,EACtB,YAAY,CAAC,SAAS,CAAC,oBAAoB,EAC3C,YAAY,CACb,CAAC;gBACF,mBAAmB,GAAG,eAAe,CAAC,sBAAsB,CAC1D,qBAAqB,EACrB,YAAY,CAAC,SAAS,CAAC,mBAAmB,EAC1C,YAAY,CACb,CAAC;gBACF,qBAAqB,GAAG,eAAe,CAAC,sBAAsB,CAC5D,uBAAuB,EACvB,YAAY,CAAC,SAAS,CAAC,qBAAqB,EAC5C,YAAY,CACb,CAAC;gBACF,oBAAoB,GAAG,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,oBAAoB,CAAC;YACvE,CAAC;YAED,IAAI,WAAwB,CAAC;YAC7B,QAAQ,YAAY,CAAC,WAAW,EAAE,CAAC;gBACjC,KAAK,IAAI;oBACP,WAAW,GAAG,+BAAW,CAAC,EAAE,CAAC;oBAC7B,MAAM;gBACR,KAAK,IAAI;oBACP,WAAW,GAAG,+BAAW,CAAC,SAAS,CAAC;oBACpC,MAAM;gBACR;oBACE,WAAW,GAAG,+BAAW,CAAC,IAAI,CAAC;oBAC/B,MAAM;YACV,CAAC;YAED,MAAM,eAAe,GAAoB,MAAA,YAAY,CAAC,eAAe,mCAAI,qCAAe,CAAC,MAAM,CAAC;YAEhG,yBAAyB,GAAG;gBAC1B,aAAa,EAAE,aAAa;gBAC5B,WAAW;gBACX,aAAa;gBACb,sBAAsB;gBACtB,eAAe;gBACf,gBAAgB;gBAChB,gBAAgB,EAAE,YAAY,CAAC,QAAQ,CAAC,gBAAgB;gBACxD,YAAY,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY;gBAClD,gBAAgB;gBAChB,aAAa;gBACb,YAAY;gBACZ,gBAAgB;gBAChB,gCAAgC;gBAChC,YAAY;gBACZ,yBAAyB;gBACzB,eAAe;gBACf,+BAA+B;gBAC/B,gBAAgB;gBAChB,aAAa;gBACb,iBAAiB;gBACjB,oBAAoB;gBACpB,mBAAmB;gBACnB,qBAAqB;gBACrB,oBAAoB;gBACpB,oBAAoB;gBACpB,qBAAqB;gBACrB,WAAW;gBACX,QAAQ,EAAE,YAAY,CAAC,QAAQ,IAAI,EAAE;gBACrC,QAAQ,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ;gBACjC,eAAe;aAChB,CAAC;QACJ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,iBAAiB,iBAAiB,KAAK,GAAI,CAAW,CAAC,OAAO,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,eAAe,GAAgC,OAAO,CAAC,eAAe,CAAC;QAE3E,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,mCAAmC;YACnC,IAAI,sBAAsB,GAAW,8BAAe,CAAC,uBAAuB,CAC1E,yBAAyB,CAAC,aAAa,CACxC,CAAC;YAEF,IAAI,CAAC,sBAAsB,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBAC1E,6FAA6F;gBAC7F,sBAAsB,GAAG,eAAe,CAAC,kBAAkB,CAAC;gBAC5D,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,sBAAsB,CAAC,EAAE,CAAC;oBAC/C,MAAM,IAAI,iCAAa,CAAC,iDAAiD,GAAG,sBAAsB,CAAC,CAAC;gBACtG,CAAC;YACH,CAAC;YACD,eAAe,GAAG,8BAAe,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;QACrE,CAAC;QAED,oFAAoF;QACpF,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,kBAAkB,GAAuB,IAAI,0BAAkB,EAAE,CAAC;QACxE,eAAe,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAEpD,yGAAyG;QACzG,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,eAAe,CAAC,EAAE,GAAG,yBAAyB,EAAE,eAAe,EAAE,kBAAkB,EAAE,CAAC,CAAC;IACpG,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,CAAC;IAClE,CAAC;IAEO,MAAM,CAAC,sBAAsB,CACnC,SAAiB,EACjB,KAAyB,EACzB,YAA0C;QAE1C,KAAK,GAAG,eAAe,CAAC,uBAAuB,CAAC,SAAS,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;QAChF,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;YACjB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,MAAM,CAAC,uBAAuB,CACpC,SAAiB,EACjB,KAAyB,EACzB,YAA0C;QAE1C,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAClC,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;YACjB,KAAK,GAAG,wBAAI,CAAC,UAAU,CAAC,KAAK,EAAE,uBAAuB,EAAE,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAC1F,KAAK,GAAG,wBAAI,CAAC,UAAU,CAAC,KAAK,EAAE,eAAe,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;YAE1E,MAAM,kBAAkB,GAAW,iBAAiB,CAAC;YACrD,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC5C,qDAAqD;gBACrD,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;YACzF,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3C,gGAAgG;gBAChG,MAAM,IAAI,KAAK,CACb,QAAQ,SAAS,uDAAuD;oBACtE,6CAA6C,CAChD,CAAC;YACJ,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,QAAQ,SAAS,+CAA+C,CAAC,CAAC;YACpF,CAAC;YACD,eAAe,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,mBAAmB,CAAC,QAAgB;QAChD,OAAO,eAAe,CAAC,+BAA+B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxE,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,sBAAsB,CAAC,KAAa,EAAE,SAAiB;QACpE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACrD,OAAO;QACT,CAAC;QAED,wCAAwC;QACxC,MAAM,WAAW,GAAW,cAAc,CAAC;QAC3C,MAAM,KAAK,GAA2B,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,QAAQ,SAAS,2CAA2C,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3F,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,QAAQ,SAAS,yDAAyD,KAAK,EAAE,CAAC,CAAC;IACrG,CAAC;;AAjmCH,0CAkmCC;AAjmCC;;GAEG;AACoB,0BAAU,GAAe,8BAAU,CAAC,gBAAgB,CAAC,mCAAkB,CAAC,CAAC;AAEhG;;GAEG;AACoB,wBAAQ,GAAyB,oBAAoB,CAAC;AAE7E;;;;GAIG;AACoB,kCAAkB,GAAW,IAAI,CAAC,OAAO,CAC9D,SAAS,EACT,+BAA+B,CAChC,CAAC;AAEsB,8BAAc,GAAyB,4BAAQ,CAAC,IAAI,CAC1E,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,wCAAwC,CAAC,CAC/D,CAAC;AAEF,iFAAiF;AACzD,+CAA+B,GAAW,iBAAiB,CAAC;AA0kCtF,MAAM,WAAW,GAAgB,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;AAEtF;;GAEG;AACH,SAAS,qBAAqB,CAC5B,YAAqC;IAErC,MAAM,mBAAmB,GAAa,EAAE,CAAC;IACzC,MAAM,WAAW,GAAuB,EAAE,CAAC,CAAC,kBAAkB;IAC9D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;QAC5C,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACzB,+FAA+F;YAC/F,sCAAsC;YACtC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChC,CAAC;QAED,gFAAgF;QAChF,IAAI,CAAC;YACH,0BAAkB,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,MAAM,aAAa,GAAa,EAAE,CAAC;IACnC,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,EAAE,CAAC;QACrD,aAAa,CAAC,IAAI,CAChB,GAAG,kBAAkB,6EAA6E,CACnG,CAAC;IACJ,CAAC;IACD,KAAK,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,WAAW,EAAE,CAAC;QACnD,aAAa,CAAC,IAAI,CAAC,GAAG,UAAU,KAAK,UAAU,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC7B,MAAM,YAAY,GAAW;YAC3B,oDAAoD;YACpD,GAAG,aAAa;SACjB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;IAChC,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as path from 'path';\nimport * as resolve from 'resolve';\nimport lodash = require('lodash');\nimport {\n  JsonFile,\n  JsonSchema,\n  FileSystem,\n  PackageJsonLookup,\n  type INodePackageJson,\n  PackageName,\n  Text,\n  InternalError,\n  Path,\n  NewlineKind\n} from '@rushstack/node-core-library';\nimport { type IRigConfig, RigConfig } from '@rushstack/rig-package';\nimport { EnumMemberOrder, ReleaseTag } from '@microsoft/api-extractor-model';\nimport { TSDocConfiguration, TSDocTagDefinition } from '@microsoft/tsdoc';\nimport { TSDocConfigFile } from '@microsoft/tsdoc-config';\n\nimport type {\n  ApiReportVariant,\n  IConfigApiReport,\n  IConfigFile,\n  IExtractorMessagesConfig\n} from './IConfigFile';\nimport { PackageMetadataManager } from '../analyzer/PackageMetadataManager';\nimport { MessageRouter } from '../collector/MessageRouter';\nimport type { IApiModelGenerationOptions } from '../generators/ApiModelGenerator';\n\nimport apiExtractorSchema from '../schemas/api-extractor.schema.json';\n\n/**\n * Tokens used during variable expansion of path fields from api-extractor.json.\n */\ninterface IExtractorConfigTokenContext {\n  /**\n   * The `<unscopedPackageName>` token returns the project's NPM package name, without any NPM scope.\n   * If there is no associated package.json file, then the value is `unknown-package`.\n   *\n   * Example: `my-project`\n   */\n  unscopedPackageName: string;\n\n  /**\n   * The `<packageName>` token returns the project's full NPM package name including any NPM scope.\n   * If there is no associated package.json file, then the value is `unknown-package`.\n   *\n   * Example: `@scope/my-project`\n   */\n  packageName: string;\n\n  /**\n   * The `<projectFolder>` token returns the expanded `\"projectFolder\"` setting from api-extractor.json.\n   */\n  projectFolder: string;\n}\n\n/**\n * Options for {@link ExtractorConfig.tryLoadForFolder}.\n *\n * @public\n */\nexport interface IExtractorConfigLoadForFolderOptions {\n  /**\n   * The folder path to start from when searching for api-extractor.json.\n   */\n  startingFolder: string;\n\n  /**\n   * An already constructed `PackageJsonLookup` cache object to use.  If omitted, a temporary one will\n   * be constructed.\n   */\n  packageJsonLookup?: PackageJsonLookup;\n\n  /**\n   * An already constructed `RigConfig` object.  If omitted, then a new `RigConfig` object will be constructed.\n   */\n  rigConfig?: IRigConfig;\n}\n\n/**\n * Options for {@link ExtractorConfig.prepare}.\n *\n * @public\n */\nexport interface IExtractorConfigPrepareOptions {\n  /**\n   * A configuration object as returned by {@link ExtractorConfig.loadFile}.\n   */\n  configObject: IConfigFile;\n\n  /**\n   * The absolute path of the file that the `configObject` object was loaded from.  This is used for error messages\n   * and when probing for `tsconfig.json`.\n   *\n   * @remarks\n   *\n   * If `configObjectFullPath` and `projectFolderLookupToken` are both unspecified, then the api-extractor.json\n   * config file must explicitly specify a `projectFolder` setting rather than relying on the `<lookup>` token.\n   */\n  configObjectFullPath: string | undefined;\n\n  /**\n   * The parsed package.json file for the working package, or undefined if API Extractor was invoked without\n   * a package.json file.\n   *\n   * @remarks\n   *\n   * If omitted, then the `<unscopedPackageName>` and `<packageName>` tokens will have default values.\n   */\n  packageJson?: INodePackageJson | undefined;\n\n  /**\n   * The absolute path of the file that the `packageJson` object was loaded from, or undefined if API Extractor\n   * was invoked without a package.json file.\n   *\n   * @remarks\n   *\n   * This is used for error messages and when resolving paths found in package.json.\n   *\n   * If `packageJsonFullPath` is specified but `packageJson` is omitted, the file will be loaded automatically.\n   */\n  packageJsonFullPath: string | undefined;\n\n  /**\n   * The default value for the `projectFolder` setting is the `<lookup>` token, which uses a heuristic to guess\n   * an appropriate project folder.  Use `projectFolderLookupValue` to manually specify the `<lookup>` token value\n   * instead.\n   *\n   * @remarks\n   * If the `projectFolder` setting is explicitly specified in api-extractor.json file, it should take precedence\n   * over a value specified via the API.  Thus the `projectFolderLookupToken` option provides a way to override\n   * the default value for `projectFolder` setting while still honoring a manually specified value.\n   */\n  projectFolderLookupToken?: string;\n\n  /**\n   * Allow customization of the tsdoc.json config file.  If omitted, this file will be loaded from its default\n   * location.  If the file does not exist, then the standard definitions will be used from\n   * `@microsoft/api-extractor/extends/tsdoc-base.json`.\n   */\n  tsdocConfigFile?: TSDocConfigFile;\n\n  /**\n   * When preparing the configuration object, folder and file paths referenced in the configuration are checked\n   * for existence, and an error is reported if they are not found.  This option can be used to disable this\n   * check for the main entry point module. This may be useful when preparing a configuration file for an\n   * un-built project.\n   */\n  ignoreMissingEntryPoint?: boolean;\n}\n\n/**\n * Configuration for a single API report, including its {@link IExtractorConfigApiReport.variant}.\n *\n * @public\n */\nexport interface IExtractorConfigApiReport {\n  /**\n   * Report variant.\n   * Determines which API items will be included in the report output, based on their tagged release levels.\n   */\n  variant: ApiReportVariant;\n\n  /**\n   * Name of the output report file.\n   * @remarks Relative to the configured report directory path.\n   */\n  fileName: string;\n}\n\n/** Default {@link IConfigApiReport.reportVariants} */\nconst defaultApiReportVariants: readonly ApiReportVariant[] = ['complete'];\n\n/**\n * Default {@link IConfigApiReport.tagsToReport}.\n *\n * @remarks\n * Note that this list is externally documented, and directly affects report output.\n * Also note that the order of tags in this list is significant, as it determines the order of tags in the report.\n * Any changes to this list should be considered breaking.\n */\nconst defaultTagsToReport: Readonly<Record<`@${string}`, boolean>> = {\n  '@sealed': true,\n  '@virtual': true,\n  '@override': true,\n  '@eventProperty': true,\n  '@deprecated': true\n};\n\ninterface IExtractorConfigParameters {\n  projectFolder: string;\n  packageJson: INodePackageJson | undefined;\n  packageFolder: string | undefined;\n  mainEntryPointFilePath: string;\n  bundledPackages: string[];\n  tsconfigFilePath: string;\n  overrideTsconfig: {} | undefined;\n  skipLibCheck: boolean;\n  apiReportEnabled: boolean;\n  reportConfigs: readonly IExtractorConfigApiReport[];\n  reportFolder: string;\n  reportTempFolder: string;\n  apiReportIncludeForgottenExports: boolean;\n  tagsToReport: Readonly<Record<`@${string}`, boolean>>;\n  docModelGenerationOptions: IApiModelGenerationOptions | undefined;\n  apiJsonFilePath: string;\n  docModelIncludeForgottenExports: boolean;\n  projectFolderUrl: string | undefined;\n  rollupEnabled: boolean;\n  untrimmedFilePath: string;\n  alphaTrimmedFilePath: string;\n  betaTrimmedFilePath: string;\n  publicTrimmedFilePath: string;\n  omitTrimmingComments: boolean;\n  tsdocMetadataEnabled: boolean;\n  tsdocMetadataFilePath: string;\n  tsdocConfigFile: TSDocConfigFile;\n  tsdocConfiguration: TSDocConfiguration;\n  newlineKind: NewlineKind;\n  messages: IExtractorMessagesConfig;\n  testMode: boolean;\n  enumMemberOrder: EnumMemberOrder;\n}\n\n/**\n * The `ExtractorConfig` class loads, validates, interprets, and represents the api-extractor.json config file.\n * @sealed\n * @public\n */\nexport class ExtractorConfig {\n  /**\n   * The JSON Schema for API Extractor config file (api-extractor.schema.json).\n   */\n  public static readonly jsonSchema: JsonSchema = JsonSchema.fromLoadedObject(apiExtractorSchema);\n\n  /**\n   * The config file name \"api-extractor.json\".\n   */\n  public static readonly FILENAME: 'api-extractor.json' = 'api-extractor.json';\n\n  /**\n   * The full path to `extends/tsdoc-base.json` which contains the standard TSDoc configuration\n   * for API Extractor.\n   * @internal\n   */\n  public static readonly _tsdocBaseFilePath: string = path.resolve(\n    __dirname,\n    '../../extends/tsdoc-base.json'\n  );\n\n  private static readonly _defaultConfig: Partial<IConfigFile> = JsonFile.load(\n    path.join(__dirname, '../schemas/api-extractor-defaults.json')\n  );\n\n  /** Match all three flavors for type declaration files (.d.ts, .d.mts, .d.cts) */\n  private static readonly _declarationFileExtensionRegExp: RegExp = /\\.d\\.(c|m)?ts$/i;\n\n  /** {@inheritDoc IConfigFile.projectFolder} */\n  public readonly projectFolder: string;\n\n  /**\n   * The parsed package.json file for the working package, or undefined if API Extractor was invoked without\n   * a package.json file.\n   */\n  public readonly packageJson: INodePackageJson | undefined;\n\n  /**\n   * The absolute path of the folder containing the package.json file for the working package, or undefined\n   * if API Extractor was invoked without a package.json file.\n   */\n  public readonly packageFolder: string | undefined;\n\n  /** {@inheritDoc IConfigFile.mainEntryPointFilePath} */\n  public readonly mainEntryPointFilePath: string;\n\n  /** {@inheritDoc IConfigFile.bundledPackages} */\n  public readonly bundledPackages: string[];\n\n  /** {@inheritDoc IConfigCompiler.tsconfigFilePath} */\n  public readonly tsconfigFilePath: string;\n\n  /** {@inheritDoc IConfigCompiler.overrideTsconfig} */\n  public readonly overrideTsconfig: {} | undefined;\n\n  /** {@inheritDoc IConfigCompiler.skipLibCheck} */\n  public readonly skipLibCheck: boolean;\n\n  /** {@inheritDoc IConfigApiReport.enabled} */\n  public readonly apiReportEnabled: boolean;\n\n  /**\n   * List of configurations for report files to be generated.\n   * @remarks Derived from {@link IConfigApiReport.reportFileName} and {@link IConfigApiReport.reportVariants}.\n   */\n  public readonly reportConfigs: readonly IExtractorConfigApiReport[];\n  /** {@inheritDoc IConfigApiReport.reportFolder} */\n  public readonly reportFolder: string;\n  /** {@inheritDoc IConfigApiReport.reportTempFolder} */\n  public readonly reportTempFolder: string;\n  /** {@inheritDoc IConfigApiReport.tagsToReport} */\n  public readonly tagsToReport: Readonly<Record<`@${string}`, boolean>>;\n\n  /**\n   * Gets the file path for the \"complete\" (default) report configuration, if one was specified.\n   * Otherwise, returns an empty string.\n   * @deprecated Use {@link ExtractorConfig.reportConfigs} to access all report configurations.\n   */\n  public get reportFilePath(): string {\n    const completeConfig: IExtractorConfigApiReport | undefined = this._getCompleteReportConfig();\n    return completeConfig === undefined ? '' : path.join(this.reportFolder, completeConfig.fileName);\n  }\n\n  /**\n   * Gets the temp file path for the \"complete\" (default) report configuration, if one was specified.\n   * Otherwise, returns an empty string.\n   * @deprecated Use {@link ExtractorConfig.reportConfigs} to access all report configurations.\n   */\n  public get reportTempFilePath(): string {\n    const completeConfig: IExtractorConfigApiReport | undefined = this._getCompleteReportConfig();\n    return completeConfig === undefined ? '' : path.join(this.reportTempFolder, completeConfig.fileName);\n  }\n\n  /** {@inheritDoc IConfigApiReport.includeForgottenExports} */\n  public readonly apiReportIncludeForgottenExports: boolean;\n\n  /**\n   * If specified, the doc model is enabled and the specified options will be used.\n   * @beta\n   */\n  public readonly docModelGenerationOptions: IApiModelGenerationOptions | undefined;\n  /** {@inheritDoc IConfigDocModel.apiJsonFilePath} */\n  public readonly apiJsonFilePath: string;\n  /** {@inheritDoc IConfigDocModel.includeForgottenExports} */\n  public readonly docModelIncludeForgottenExports: boolean;\n  /** {@inheritDoc IConfigDocModel.projectFolderUrl} */\n  public readonly projectFolderUrl: string | undefined;\n\n  /** {@inheritDoc IConfigDtsRollup.enabled} */\n  public readonly rollupEnabled: boolean;\n  /** {@inheritDoc IConfigDtsRollup.untrimmedFilePath} */\n  public readonly untrimmedFilePath: string;\n  /** {@inheritDoc IConfigDtsRollup.alphaTrimmedFilePath} */\n  public readonly alphaTrimmedFilePath: string;\n  /** {@inheritDoc IConfigDtsRollup.betaTrimmedFilePath} */\n  public readonly betaTrimmedFilePath: string;\n  /** {@inheritDoc IConfigDtsRollup.publicTrimmedFilePath} */\n  public readonly publicTrimmedFilePath: string;\n  /** {@inheritDoc IConfigDtsRollup.omitTrimmingComments} */\n  public readonly omitTrimmingComments: boolean;\n\n  /** {@inheritDoc IConfigTsdocMetadata.enabled} */\n  public readonly tsdocMetadataEnabled: boolean;\n  /** {@inheritDoc IConfigTsdocMetadata.tsdocMetadataFilePath} */\n  public readonly tsdocMetadataFilePath: string;\n\n  /**\n   * The tsdoc.json configuration that will be used when parsing doc comments.\n   */\n  public readonly tsdocConfigFile: TSDocConfigFile;\n\n  /**\n   * The `TSDocConfiguration` loaded from {@link ExtractorConfig.tsdocConfigFile}.\n   */\n  public readonly tsdocConfiguration: TSDocConfiguration;\n\n  /**\n   * Specifies what type of newlines API Extractor should use when writing output files.  By default, the output files\n   * will be written with Windows-style newlines.\n   */\n  public readonly newlineKind: NewlineKind;\n\n  /** {@inheritDoc IConfigFile.messages} */\n  public readonly messages: IExtractorMessagesConfig;\n\n  /** {@inheritDoc IConfigFile.testMode} */\n  public readonly testMode: boolean;\n\n  /** {@inheritDoc IConfigFile.enumMemberOrder} */\n  public readonly enumMemberOrder: EnumMemberOrder;\n\n  private constructor({\n    projectFolder,\n    packageJson,\n    packageFolder,\n    mainEntryPointFilePath,\n    bundledPackages,\n    tsconfigFilePath,\n    overrideTsconfig,\n    skipLibCheck,\n    apiReportEnabled,\n    apiReportIncludeForgottenExports,\n    reportConfigs,\n    reportFolder,\n    reportTempFolder,\n    tagsToReport,\n    docModelGenerationOptions,\n    apiJsonFilePath,\n    docModelIncludeForgottenExports,\n    projectFolderUrl,\n    rollupEnabled,\n    untrimmedFilePath,\n    alphaTrimmedFilePath,\n    betaTrimmedFilePath,\n    publicTrimmedFilePath,\n    omitTrimmingComments,\n    tsdocMetadataEnabled,\n    tsdocMetadataFilePath,\n    tsdocConfigFile,\n    tsdocConfiguration,\n    newlineKind,\n    messages,\n    testMode,\n    enumMemberOrder\n  }: IExtractorConfigParameters) {\n    this.projectFolder = projectFolder;\n    this.packageJson = packageJson;\n    this.packageFolder = packageFolder;\n    this.mainEntryPointFilePath = mainEntryPointFilePath;\n    this.bundledPackages = bundledPackages;\n    this.tsconfigFilePath = tsconfigFilePath;\n    this.overrideTsconfig = overrideTsconfig;\n    this.skipLibCheck = skipLibCheck;\n    this.apiReportEnabled = apiReportEnabled;\n    this.apiReportIncludeForgottenExports = apiReportIncludeForgottenExports;\n    this.reportConfigs = reportConfigs;\n    this.reportFolder = reportFolder;\n    this.reportTempFolder = reportTempFolder;\n    this.tagsToReport = tagsToReport;\n    this.docModelGenerationOptions = docModelGenerationOptions;\n    this.apiJsonFilePath = apiJsonFilePath;\n    this.docModelIncludeForgottenExports = docModelIncludeForgottenExports;\n    this.projectFolderUrl = projectFolderUrl;\n    this.rollupEnabled = rollupEnabled;\n    this.untrimmedFilePath = untrimmedFilePath;\n    this.alphaTrimmedFilePath = alphaTrimmedFilePath;\n    this.betaTrimmedFilePath = betaTrimmedFilePath;\n    this.publicTrimmedFilePath = publicTrimmedFilePath;\n    this.omitTrimmingComments = omitTrimmingComments;\n    this.tsdocMetadataEnabled = tsdocMetadataEnabled;\n    this.tsdocMetadataFilePath = tsdocMetadataFilePath;\n    this.tsdocConfigFile = tsdocConfigFile;\n    this.tsdocConfiguration = tsdocConfiguration;\n    this.newlineKind = newlineKind;\n    this.messages = messages;\n    this.testMode = testMode;\n    this.enumMemberOrder = enumMemberOrder;\n  }\n\n  /**\n   * Returns a JSON-like string representing the `ExtractorConfig` state, which can be printed to a console\n   * for diagnostic purposes.\n   *\n   * @remarks\n   * This is used by the \"--diagnostics\" command-line option.  The string is not intended to be deserialized;\n   * its format may be changed at any time.\n   */\n  public getDiagnosticDump(): string {\n    // Handle the simple JSON-serializable properties using buildJsonDumpObject()\n    const result: object = MessageRouter.buildJsonDumpObject(this, {\n      keyNamesToOmit: ['tsdocConfigFile', 'tsdocConfiguration']\n    });\n\n    // Implement custom formatting for tsdocConfigFile and tsdocConfiguration\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (result as any).tsdocConfigFile = {\n      filePath: this.tsdocConfigFile.filePath,\n      log: this.tsdocConfigFile.log.messages.map((x) => x.toString())\n    };\n\n    return JSON.stringify(result, undefined, 2);\n  }\n\n  /**\n   * Returns a simplified file path for use in error messages.\n   * @internal\n   */\n  public _getShortFilePath(absolutePath: string): string {\n    if (!path.isAbsolute(absolutePath)) {\n      throw new InternalError('Expected absolute path: ' + absolutePath);\n    }\n    if (Path.isUnderOrEqual(absolutePath, this.projectFolder)) {\n      return Path.convertToSlashes(path.relative(this.projectFolder, absolutePath));\n    }\n    return absolutePath;\n  }\n\n  /**\n   * Searches for the api-extractor.json config file associated with the specified starting folder,\n   * and loads the file if found.  This lookup supports\n   * {@link https://www.npmjs.com/package/@rushstack/rig-package | rig packages}.\n   *\n   * @remarks\n   * The search will first look for a package.json file in a parent folder of the starting folder;\n   * if found, that will be used as the base folder instead of the starting folder.  If the config\n   * file is not found in `<baseFolder>/api-extractor.json` or `<baseFolder>/config/api-extractor.json`,\n   * then `<baseFolder/config/rig.json` will be checked to see whether a\n   * {@link https://www.npmjs.com/package/@rushstack/rig-package | rig package} is referenced; if so then\n   * the rig's api-extractor.json file will be used instead.  If a config file is found, it will be loaded\n   * and returned with the `IExtractorConfigPrepareOptions` object. Otherwise, `undefined` is returned\n   * to indicate that API Extractor does not appear to be configured for the specified folder.\n   *\n   * @returns An options object that can be passed to {@link ExtractorConfig.prepare}, or `undefined`\n   * if not api-extractor.json file was found.\n   */\n  public static tryLoadForFolder(\n    options: IExtractorConfigLoadForFolderOptions\n  ): IExtractorConfigPrepareOptions | undefined {\n    const packageJsonLookup: PackageJsonLookup = options.packageJsonLookup || new PackageJsonLookup();\n    const startingFolder: string = options.startingFolder;\n\n    // Figure out which project we're in and look for the config file at the project root\n    const packageJsonFullPath: string | undefined =\n      packageJsonLookup.tryGetPackageJsonFilePathFor(startingFolder);\n    const packageFolder: string | undefined = packageJsonFullPath\n      ? path.dirname(packageJsonFullPath)\n      : undefined;\n\n    // If there is no package, then just use the starting folder\n    const baseFolder: string = packageFolder || startingFolder;\n\n    let projectFolderLookupToken: string | undefined = undefined;\n\n    // First try the standard \"config\" subfolder:\n    let configFilename: string = path.join(baseFolder, 'config', ExtractorConfig.FILENAME);\n    if (FileSystem.exists(configFilename)) {\n      if (FileSystem.exists(path.join(baseFolder, ExtractorConfig.FILENAME))) {\n        throw new Error(`Found conflicting ${ExtractorConfig.FILENAME} files in \".\" and \"./config\" folders`);\n      }\n    } else {\n      // Otherwise try the top-level folder\n      configFilename = path.join(baseFolder, ExtractorConfig.FILENAME);\n\n      if (!FileSystem.exists(configFilename)) {\n        // If We didn't find it in <packageFolder>/api-extractor.json or <packageFolder>/config/api-extractor.json\n        // then check for a rig package\n        if (packageFolder) {\n          let rigConfig: IRigConfig;\n          if (options.rigConfig) {\n            // The caller provided an already solved RigConfig.  Double-check that it is for the right project.\n            if (!Path.isEqual(options.rigConfig.projectFolderPath, packageFolder)) {\n              throw new Error(\n                'The provided ILoadForFolderOptions.rigConfig is for the wrong project folder:\\n' +\n                  '\\nExpected path: ' +\n                  packageFolder +\n                  '\\nProvided path: ' +\n                  options.rigConfig.projectFolderOriginalPath\n              );\n            }\n            rigConfig = options.rigConfig;\n          } else {\n            rigConfig = RigConfig.loadForProjectFolder({\n              projectFolderPath: packageFolder\n            });\n          }\n\n          if (rigConfig.rigFound) {\n            configFilename = path.join(rigConfig.getResolvedProfileFolder(), ExtractorConfig.FILENAME);\n\n            // If the \"projectFolder\" setting isn't specified in api-extractor.json, it defaults to the\n            // \"<lookup>\" token which will probe for the tsconfig.json nearest to the api-extractor.json path.\n            // But this won't work if api-extractor.json belongs to the rig.  So instead \"<lookup>\" should be\n            // the \"<packageFolder>\" that referenced the rig.\n            projectFolderLookupToken = packageFolder;\n          }\n        }\n        if (!FileSystem.exists(configFilename)) {\n          // API Extractor does not seem to be configured for this folder\n          return undefined;\n        }\n      }\n    }\n\n    const configObjectFullPath: string = path.resolve(configFilename);\n    const configObject: IConfigFile = ExtractorConfig.loadFile(configObjectFullPath);\n\n    return {\n      configObject,\n      configObjectFullPath,\n      packageJsonFullPath,\n      projectFolderLookupToken\n    };\n  }\n\n  /**\n   * Loads the api-extractor.json config file from the specified file path, and prepares an `ExtractorConfig` object.\n   *\n   * @remarks\n   * Loads the api-extractor.json config file from the specified file path.   If the \"extends\" field is present,\n   * the referenced file(s) will be merged.  For any omitted fields, the API Extractor default values are merged.\n   *\n   * The result is prepared using `ExtractorConfig.prepare()`.\n   */\n  public static loadFileAndPrepare(configJsonFilePath: string): ExtractorConfig {\n    const configObjectFullPath: string = path.resolve(configJsonFilePath);\n    const configObject: IConfigFile = ExtractorConfig.loadFile(configObjectFullPath);\n\n    const packageJsonLookup: PackageJsonLookup = new PackageJsonLookup();\n    const packageJsonFullPath: string | undefined =\n      packageJsonLookup.tryGetPackageJsonFilePathFor(configObjectFullPath);\n\n    const extractorConfig: ExtractorConfig = ExtractorConfig.prepare({\n      configObject,\n      configObjectFullPath,\n      packageJsonFullPath\n    });\n\n    return extractorConfig;\n  }\n\n  /**\n   * Performs only the first half of {@link ExtractorConfig.loadFileAndPrepare}, providing an opportunity to\n   * modify the object before it is passed to {@link ExtractorConfig.prepare}.\n   *\n   * @remarks\n   * Loads the api-extractor.json config file from the specified file path.   If the \"extends\" field is present,\n   * the referenced file(s) will be merged.  For any omitted fields, the API Extractor default values are merged.\n   */\n  public static loadFile(jsonFilePath: string): IConfigFile {\n    // Set to keep track of config files which have been processed.\n    const visitedPaths: Set<string> = new Set<string>();\n\n    let currentConfigFilePath: string = path.resolve(jsonFilePath);\n    let configObject: Partial<IConfigFile> = {};\n\n    // Lodash merges array values by default, which is unintuitive for config files (and makes it impossible for derived configurations to overwrite arrays).\n    // For example, given a base config containing an array property with value [\"foo\", \"bar\"] and a derived config that specifies [\"baz\"] for that property, lodash will produce [\"baz\", \"bar\"], which is unintuitive.\n    // This customizer function ensures that arrays are always overwritten.\n    const mergeCustomizer: lodash.MergeWithCustomizer = (objValue, srcValue) => {\n      if (Array.isArray(srcValue)) {\n        return srcValue;\n      }\n      // Fall back to default merge behavior.\n      return undefined;\n    };\n\n    try {\n      do {\n        // Check if this file was already processed.\n        if (visitedPaths.has(currentConfigFilePath)) {\n          throw new Error(\n            `The API Extractor \"extends\" setting contains a cycle.` +\n              `  This file is included twice: \"${currentConfigFilePath}\"`\n          );\n        }\n        visitedPaths.add(currentConfigFilePath);\n\n        const currentConfigFolderPath: string = path.dirname(currentConfigFilePath);\n\n        // Load the extractor config defined in extends property.\n        const baseConfig: IConfigFile = JsonFile.load(currentConfigFilePath);\n\n        let extendsField: string = baseConfig.extends || '';\n\n        // Delete the \"extends\" field so it doesn't get merged\n        delete baseConfig.extends;\n\n        if (extendsField) {\n          if (extendsField.match(/^\\.\\.?[\\\\/]/)) {\n            // EXAMPLE:  \"./subfolder/api-extractor-base.json\"\n            extendsField = path.resolve(currentConfigFolderPath, extendsField);\n          } else {\n            // EXAMPLE:  \"my-package/api-extractor-base.json\"\n            //\n            // Resolve \"my-package\" from the perspective of the current folder.\n            try {\n              extendsField = resolve.sync(extendsField, {\n                basedir: currentConfigFolderPath\n              });\n            } catch (e) {\n              throw new Error(`Error resolving NodeJS path \"${extendsField}\": ${(e as Error).message}`);\n            }\n          }\n        }\n\n        // This step has to be performed in advance, since the currentConfigFolderPath information will be lost\n        // after lodash.merge() is performed.\n        ExtractorConfig._resolveConfigFileRelativePaths(baseConfig, currentConfigFolderPath);\n\n        // Merge extractorConfig into baseConfig, mutating baseConfig\n        lodash.mergeWith(baseConfig, configObject, mergeCustomizer);\n        configObject = baseConfig;\n\n        currentConfigFilePath = extendsField;\n      } while (currentConfigFilePath);\n    } catch (e) {\n      throw new Error(`Error loading ${currentConfigFilePath}:\\n` + (e as Error).message);\n    }\n\n    // Lastly, apply the defaults\n    configObject = lodash.mergeWith(\n      lodash.cloneDeep(ExtractorConfig._defaultConfig),\n      configObject,\n      mergeCustomizer\n    );\n\n    ExtractorConfig.jsonSchema.validateObject(configObject, jsonFilePath);\n\n    // The schema validation should ensure that this object conforms to IConfigFile\n    return configObject as IConfigFile;\n  }\n\n  private static _resolveConfigFileRelativePaths(\n    configFile: IConfigFile,\n    currentConfigFolderPath: string\n  ): void {\n    if (configFile.projectFolder) {\n      configFile.projectFolder = ExtractorConfig._resolveConfigFileRelativePath(\n        'projectFolder',\n        configFile.projectFolder,\n        currentConfigFolderPath\n      );\n    }\n\n    if (configFile.mainEntryPointFilePath) {\n      configFile.mainEntryPointFilePath = ExtractorConfig._resolveConfigFileRelativePath(\n        'mainEntryPointFilePath',\n        configFile.mainEntryPointFilePath,\n        currentConfigFolderPath\n      );\n    }\n\n    if (configFile.compiler) {\n      if (configFile.compiler.tsconfigFilePath) {\n        configFile.compiler.tsconfigFilePath = ExtractorConfig._resolveConfigFileRelativePath(\n          'tsconfigFilePath',\n          configFile.compiler.tsconfigFilePath,\n          currentConfigFolderPath\n        );\n      }\n    }\n\n    if (configFile.apiReport) {\n      if (configFile.apiReport.reportFolder) {\n        configFile.apiReport.reportFolder = ExtractorConfig._resolveConfigFileRelativePath(\n          'reportFolder',\n          configFile.apiReport.reportFolder,\n          currentConfigFolderPath\n        );\n      }\n      if (configFile.apiReport.reportTempFolder) {\n        configFile.apiReport.reportTempFolder = ExtractorConfig._resolveConfigFileRelativePath(\n          'reportTempFolder',\n          configFile.apiReport.reportTempFolder,\n          currentConfigFolderPath\n        );\n      }\n    }\n\n    if (configFile.docModel) {\n      if (configFile.docModel.apiJsonFilePath) {\n        configFile.docModel.apiJsonFilePath = ExtractorConfig._resolveConfigFileRelativePath(\n          'apiJsonFilePath',\n          configFile.docModel.apiJsonFilePath,\n          currentConfigFolderPath\n        );\n      }\n    }\n\n    if (configFile.dtsRollup) {\n      if (configFile.dtsRollup.untrimmedFilePath) {\n        configFile.dtsRollup.untrimmedFilePath = ExtractorConfig._resolveConfigFileRelativePath(\n          'untrimmedFilePath',\n          configFile.dtsRollup.untrimmedFilePath,\n          currentConfigFolderPath\n        );\n      }\n      if (configFile.dtsRollup.alphaTrimmedFilePath) {\n        configFile.dtsRollup.alphaTrimmedFilePath = ExtractorConfig._resolveConfigFileRelativePath(\n          'alphaTrimmedFilePath',\n          configFile.dtsRollup.alphaTrimmedFilePath,\n          currentConfigFolderPath\n        );\n      }\n      if (configFile.dtsRollup.betaTrimmedFilePath) {\n        configFile.dtsRollup.betaTrimmedFilePath = ExtractorConfig._resolveConfigFileRelativePath(\n          'betaTrimmedFilePath',\n          configFile.dtsRollup.betaTrimmedFilePath,\n          currentConfigFolderPath\n        );\n      }\n      if (configFile.dtsRollup.publicTrimmedFilePath) {\n        configFile.dtsRollup.publicTrimmedFilePath = ExtractorConfig._resolveConfigFileRelativePath(\n          'publicTrimmedFilePath',\n          configFile.dtsRollup.publicTrimmedFilePath,\n          currentConfigFolderPath\n        );\n      }\n    }\n\n    if (configFile.tsdocMetadata) {\n      if (configFile.tsdocMetadata.tsdocMetadataFilePath) {\n        configFile.tsdocMetadata.tsdocMetadataFilePath = ExtractorConfig._resolveConfigFileRelativePath(\n          'tsdocMetadataFilePath',\n          configFile.tsdocMetadata.tsdocMetadataFilePath,\n          currentConfigFolderPath\n        );\n      }\n    }\n  }\n\n  private static _resolveConfigFileRelativePath(\n    fieldName: string,\n    fieldValue: string,\n    currentConfigFolderPath: string\n  ): string {\n    if (!path.isAbsolute(fieldValue)) {\n      if (fieldValue.indexOf('<projectFolder>') !== 0) {\n        // If the path is not absolute and does not start with \"<projectFolder>\", then resolve it relative\n        // to the folder of the config file that it appears in\n        return path.join(currentConfigFolderPath, fieldValue);\n      }\n    }\n\n    return fieldValue;\n  }\n\n  /**\n   * Prepares an `ExtractorConfig` object using a configuration that is provided as a runtime object,\n   * rather than reading it from disk.  This allows configurations to be constructed programmatically,\n   * loaded from an alternate source, and/or customized after loading.\n   */\n  public static prepare(options: IExtractorConfigPrepareOptions): ExtractorConfig {\n    const filenameForErrors: string = options.configObjectFullPath || 'the configuration object';\n    const configObject: Partial<IConfigFile> = options.configObject;\n\n    if (configObject.extends) {\n      throw new Error(\n        'The IConfigFile.extends field must be expanded before calling ExtractorConfig.prepare()'\n      );\n    }\n\n    if (options.configObjectFullPath) {\n      if (!path.isAbsolute(options.configObjectFullPath)) {\n        throw new Error('The \"configObjectFullPath\" setting must be an absolute path');\n      }\n    }\n\n    ExtractorConfig.jsonSchema.validateObject(configObject, filenameForErrors);\n\n    const packageJsonFullPath: string | undefined = options.packageJsonFullPath;\n    let packageFolder: string | undefined = undefined;\n    let packageJson: INodePackageJson | undefined = undefined;\n\n    if (packageJsonFullPath) {\n      if (!/.json$/i.test(packageJsonFullPath)) {\n        // Catch common mistakes e.g. where someone passes a folder path instead of a file path\n        throw new Error('The \"packageJsonFullPath\" setting does not have a .json file extension');\n      }\n      if (!path.isAbsolute(packageJsonFullPath)) {\n        throw new Error('The \"packageJsonFullPath\" setting must be an absolute path');\n      }\n\n      if (options.packageJson) {\n        packageJson = options.packageJson;\n      } else {\n        const packageJsonLookup: PackageJsonLookup = new PackageJsonLookup();\n        packageJson = packageJsonLookup.loadNodePackageJson(packageJsonFullPath);\n      }\n\n      packageFolder = path.dirname(packageJsonFullPath);\n    }\n\n    // \"tsdocConfigFile\" and \"tsdocConfiguration\" are prepared outside the try-catch block,\n    // so that if exceptions are thrown, it will not get the \"Error parsing api-extractor.json:\" header\n    let extractorConfigParameters: Omit<IExtractorConfigParameters, 'tsdocConfigFile' | 'tsdocConfiguration'>;\n\n    try {\n      if (!configObject.compiler) {\n        // A merged configuration should have this\n        throw new Error('The \"compiler\" section is missing');\n      }\n\n      if (!configObject.projectFolder) {\n        // A merged configuration should have this\n        throw new Error('The \"projectFolder\" setting is missing');\n      }\n\n      let projectFolder: string;\n      if (configObject.projectFolder.trim() === '<lookup>') {\n        if (options.projectFolderLookupToken) {\n          // Use the manually specified \"<lookup>\" value\n          projectFolder = options.projectFolderLookupToken;\n\n          if (!FileSystem.exists(options.projectFolderLookupToken)) {\n            throw new Error(\n              'The specified \"projectFolderLookupToken\" path does not exist: ' +\n                options.projectFolderLookupToken\n            );\n          }\n        } else {\n          if (!options.configObjectFullPath) {\n            throw new Error(\n              'The \"projectFolder\" setting uses the \"<lookup>\" token, but it cannot be expanded because' +\n                ' the \"configObjectFullPath\" setting was not specified'\n            );\n          }\n\n          // \"The default value for `projectFolder` is the token `<lookup>`, which means the folder is determined\n          // by traversing parent folders, starting from the folder containing api-extractor.json, and stopping\n          // at the first folder that contains a tsconfig.json file.  If a tsconfig.json file cannot be found in\n          // this way, then an error will be reported.\"\n\n          let currentFolder: string = path.dirname(options.configObjectFullPath);\n          for (;;) {\n            const tsconfigPath: string = path.join(currentFolder, 'tsconfig.json');\n            if (FileSystem.exists(tsconfigPath)) {\n              projectFolder = currentFolder;\n              break;\n            }\n            const parentFolder: string = path.dirname(currentFolder);\n            if (parentFolder === '' || parentFolder === currentFolder) {\n              throw new Error(\n                'The \"projectFolder\" setting uses the \"<lookup>\" token, but a tsconfig.json file cannot be' +\n                  ' found in this folder or any parent folder.'\n              );\n            }\n            currentFolder = parentFolder;\n          }\n        }\n      } else {\n        ExtractorConfig._rejectAnyTokensInPath(configObject.projectFolder, 'projectFolder');\n\n        if (!FileSystem.exists(configObject.projectFolder)) {\n          throw new Error('The specified \"projectFolder\" path does not exist: ' + configObject.projectFolder);\n        }\n\n        projectFolder = configObject.projectFolder;\n      }\n\n      const tokenContext: IExtractorConfigTokenContext = {\n        unscopedPackageName: 'unknown-package',\n        packageName: 'unknown-package',\n        projectFolder: projectFolder\n      };\n\n      if (packageJson) {\n        tokenContext.packageName = packageJson.name;\n        tokenContext.unscopedPackageName = PackageName.getUnscopedName(packageJson.name);\n      }\n\n      if (!configObject.mainEntryPointFilePath) {\n        // A merged configuration should have this\n        throw new Error('The \"mainEntryPointFilePath\" setting is missing');\n      }\n      const mainEntryPointFilePath: string = ExtractorConfig._resolvePathWithTokens(\n        'mainEntryPointFilePath',\n        configObject.mainEntryPointFilePath,\n        tokenContext\n      );\n\n      if (!ExtractorConfig.hasDtsFileExtension(mainEntryPointFilePath)) {\n        throw new Error(\n          'The \"mainEntryPointFilePath\" value is not a declaration file: ' + mainEntryPointFilePath\n        );\n      }\n\n      if (!options.ignoreMissingEntryPoint && !FileSystem.exists(mainEntryPointFilePath)) {\n        throw new Error('The \"mainEntryPointFilePath\" path does not exist: ' + mainEntryPointFilePath);\n      }\n\n      const bundledPackages: string[] = configObject.bundledPackages || [];\n\n      // Note: we cannot fully validate package name patterns, as the strings may contain wildcards.\n      // We won't know if the entries are valid until we can compare them against the package.json \"dependencies\" contents.\n\n      const tsconfigFilePath: string = ExtractorConfig._resolvePathWithTokens(\n        'tsconfigFilePath',\n        configObject.compiler.tsconfigFilePath,\n        tokenContext\n      );\n\n      if (configObject.compiler.overrideTsconfig === undefined) {\n        if (!tsconfigFilePath) {\n          throw new Error('Either the \"tsconfigFilePath\" or \"overrideTsconfig\" setting must be specified');\n        }\n        if (!FileSystem.exists(tsconfigFilePath)) {\n          throw new Error('The file referenced by \"tsconfigFilePath\" does not exist: ' + tsconfigFilePath);\n        }\n      }\n\n      if (configObject.apiReport?.tagsToReport) {\n        _validateTagsToReport(configObject.apiReport.tagsToReport);\n      }\n\n      const apiReportEnabled: boolean = configObject.apiReport?.enabled ?? false;\n      const apiReportIncludeForgottenExports: boolean =\n        configObject.apiReport?.includeForgottenExports ?? false;\n      let reportFolder: string = tokenContext.projectFolder;\n      let reportTempFolder: string = tokenContext.projectFolder;\n      const reportConfigs: IExtractorConfigApiReport[] = [];\n      let tagsToReport: Record<`@${string}`, boolean> = {};\n      if (apiReportEnabled) {\n        // Undefined case checked above where we assign `apiReportEnabled`\n        const apiReportConfig: IConfigApiReport = configObject.apiReport!;\n\n        const reportFileNameSuffix: string = '.api.md';\n        let reportFileNameBase: string;\n        if (apiReportConfig.reportFileName) {\n          if (\n            apiReportConfig.reportFileName.indexOf('/') >= 0 ||\n            apiReportConfig.reportFileName.indexOf('\\\\') >= 0\n          ) {\n            throw new Error(\n              `The \"reportFileName\" setting contains invalid characters: \"${apiReportConfig.reportFileName}\"`\n            );\n          }\n\n          if (!apiReportConfig.reportFileName.endsWith(reportFileNameSuffix)) {\n            // `.api.md` extension was not specified. Use provided file name base as is.\n            reportFileNameBase = apiReportConfig.reportFileName;\n          } else {\n            // The system previously asked users to specify their filenames in a form containing the `.api.md` extension.\n            // This guidance has changed, but to maintain backwards compatibility, we will temporarily support input\n            // that ends with the `.api.md` extension specially, by stripping it out.\n            // This should be removed in version 8, possibly replaced with an explicit error to help users\n            // migrate their configs.\n            reportFileNameBase = apiReportConfig.reportFileName.slice(0, -reportFileNameSuffix.length);\n          }\n        } else {\n          // Default value\n          reportFileNameBase = '<unscopedPackageName>';\n        }\n\n        const reportVariantKinds: readonly ApiReportVariant[] =\n          apiReportConfig.reportVariants ?? defaultApiReportVariants;\n\n        for (const reportVariantKind of reportVariantKinds) {\n          // Omit the variant kind from the \"complete\" report file name for simplicity and for backwards compatibility.\n          const fileNameWithTokens: string = `${reportFileNameBase}${\n            reportVariantKind === 'complete' ? '' : `.${reportVariantKind}`\n          }${reportFileNameSuffix}`;\n          const normalizedFileName: string = ExtractorConfig._expandStringWithTokens(\n            'reportFileName',\n            fileNameWithTokens,\n            tokenContext\n          );\n\n          reportConfigs.push({\n            fileName: normalizedFileName,\n            variant: reportVariantKind\n          });\n        }\n\n        if (apiReportConfig.reportFolder) {\n          reportFolder = ExtractorConfig._resolvePathWithTokens(\n            'reportFolder',\n            apiReportConfig.reportFolder,\n            tokenContext\n          );\n        }\n\n        if (apiReportConfig.reportTempFolder) {\n          reportTempFolder = ExtractorConfig._resolvePathWithTokens(\n            'reportTempFolder',\n            apiReportConfig.reportTempFolder,\n            tokenContext\n          );\n        }\n\n        tagsToReport = {\n          ...defaultTagsToReport,\n          ...apiReportConfig.tagsToReport\n        };\n      }\n\n      let docModelGenerationOptions: IApiModelGenerationOptions | undefined = undefined;\n      let apiJsonFilePath: string = '';\n      let docModelIncludeForgottenExports: boolean = false;\n      let projectFolderUrl: string | undefined;\n      if (configObject.docModel?.enabled) {\n        apiJsonFilePath = ExtractorConfig._resolvePathWithTokens(\n          'apiJsonFilePath',\n          configObject.docModel.apiJsonFilePath,\n          tokenContext\n        );\n        docModelIncludeForgottenExports = !!configObject.docModel.includeForgottenExports;\n        projectFolderUrl = configObject.docModel.projectFolderUrl;\n\n        const releaseTagsToTrim: Set<ReleaseTag> = new Set<ReleaseTag>();\n        const releaseTagsToTrimOption: string[] = configObject.docModel.releaseTagsToTrim || ['@internal'];\n        for (const releaseTagToTrim of releaseTagsToTrimOption) {\n          let releaseTag: ReleaseTag;\n          switch (releaseTagToTrim) {\n            case '@internal': {\n              releaseTag = ReleaseTag.Internal;\n              break;\n            }\n\n            case '@alpha': {\n              releaseTag = ReleaseTag.Alpha;\n              break;\n            }\n\n            case '@beta': {\n              releaseTag = ReleaseTag.Beta;\n              break;\n            }\n\n            case '@public': {\n              releaseTag = ReleaseTag.Public;\n              break;\n            }\n\n            default: {\n              throw new Error(`The release tag \"${releaseTagToTrim}\" is not supported`);\n            }\n          }\n\n          releaseTagsToTrim.add(releaseTag);\n        }\n\n        docModelGenerationOptions = {\n          releaseTagsToTrim\n        };\n      }\n\n      let tsdocMetadataEnabled: boolean = false;\n      let tsdocMetadataFilePath: string = '';\n      if (configObject.tsdocMetadata) {\n        tsdocMetadataEnabled = !!configObject.tsdocMetadata.enabled;\n\n        if (tsdocMetadataEnabled) {\n          tsdocMetadataFilePath = configObject.tsdocMetadata.tsdocMetadataFilePath || '';\n\n          if (tsdocMetadataFilePath.trim() === '<lookup>') {\n            if (!packageJson) {\n              throw new Error(\n                'The \"<lookup>\" token cannot be used with the \"tsdocMetadataFilePath\" setting because' +\n                  ' the \"packageJson\" option was not provided'\n              );\n            }\n            if (!packageJsonFullPath) {\n              throw new Error(\n                'The \"<lookup>\" token cannot be used with \"tsdocMetadataFilePath\" because' +\n                  'the \"packageJsonFullPath\" option was not provided'\n              );\n            }\n            tsdocMetadataFilePath = PackageMetadataManager.resolveTsdocMetadataPath(\n              path.dirname(packageJsonFullPath),\n              packageJson\n            );\n          } else {\n            tsdocMetadataFilePath = ExtractorConfig._resolvePathWithTokens(\n              'tsdocMetadataFilePath',\n              configObject.tsdocMetadata.tsdocMetadataFilePath,\n              tokenContext\n            );\n          }\n\n          if (!tsdocMetadataFilePath) {\n            throw new Error(\n              'The \"tsdocMetadata.enabled\" setting is enabled,' +\n                ' but \"tsdocMetadataFilePath\" is not specified'\n            );\n          }\n        }\n      }\n\n      let rollupEnabled: boolean = false;\n      let untrimmedFilePath: string = '';\n      let betaTrimmedFilePath: string = '';\n      let alphaTrimmedFilePath: string = '';\n      let publicTrimmedFilePath: string = '';\n      let omitTrimmingComments: boolean = false;\n\n      if (configObject.dtsRollup) {\n        rollupEnabled = !!configObject.dtsRollup.enabled;\n        untrimmedFilePath = ExtractorConfig._resolvePathWithTokens(\n          'untrimmedFilePath',\n          configObject.dtsRollup.untrimmedFilePath,\n          tokenContext\n        );\n        alphaTrimmedFilePath = ExtractorConfig._resolvePathWithTokens(\n          'alphaTrimmedFilePath',\n          configObject.dtsRollup.alphaTrimmedFilePath,\n          tokenContext\n        );\n        betaTrimmedFilePath = ExtractorConfig._resolvePathWithTokens(\n          'betaTrimmedFilePath',\n          configObject.dtsRollup.betaTrimmedFilePath,\n          tokenContext\n        );\n        publicTrimmedFilePath = ExtractorConfig._resolvePathWithTokens(\n          'publicTrimmedFilePath',\n          configObject.dtsRollup.publicTrimmedFilePath,\n          tokenContext\n        );\n        omitTrimmingComments = !!configObject.dtsRollup.omitTrimmingComments;\n      }\n\n      let newlineKind: NewlineKind;\n      switch (configObject.newlineKind) {\n        case 'lf':\n          newlineKind = NewlineKind.Lf;\n          break;\n        case 'os':\n          newlineKind = NewlineKind.OsDefault;\n          break;\n        default:\n          newlineKind = NewlineKind.CrLf;\n          break;\n      }\n\n      const enumMemberOrder: EnumMemberOrder = configObject.enumMemberOrder ?? EnumMemberOrder.ByName;\n\n      extractorConfigParameters = {\n        projectFolder: projectFolder,\n        packageJson,\n        packageFolder,\n        mainEntryPointFilePath,\n        bundledPackages,\n        tsconfigFilePath,\n        overrideTsconfig: configObject.compiler.overrideTsconfig,\n        skipLibCheck: !!configObject.compiler.skipLibCheck,\n        apiReportEnabled,\n        reportConfigs,\n        reportFolder,\n        reportTempFolder,\n        apiReportIncludeForgottenExports,\n        tagsToReport,\n        docModelGenerationOptions,\n        apiJsonFilePath,\n        docModelIncludeForgottenExports,\n        projectFolderUrl,\n        rollupEnabled,\n        untrimmedFilePath,\n        alphaTrimmedFilePath,\n        betaTrimmedFilePath,\n        publicTrimmedFilePath,\n        omitTrimmingComments,\n        tsdocMetadataEnabled,\n        tsdocMetadataFilePath,\n        newlineKind,\n        messages: configObject.messages || {},\n        testMode: !!configObject.testMode,\n        enumMemberOrder\n      };\n    } catch (e) {\n      throw new Error(`Error parsing ${filenameForErrors}:\\n` + (e as Error).message);\n    }\n\n    let tsdocConfigFile: TSDocConfigFile | undefined = options.tsdocConfigFile;\n\n    if (!tsdocConfigFile) {\n      // Example: \"my-project/tsdoc.json\"\n      let packageTSDocConfigPath: string = TSDocConfigFile.findConfigPathForFolder(\n        extractorConfigParameters.projectFolder\n      );\n\n      if (!packageTSDocConfigPath || !FileSystem.exists(packageTSDocConfigPath)) {\n        // If the project does not have a tsdoc.json config file, then use API Extractor's base file.\n        packageTSDocConfigPath = ExtractorConfig._tsdocBaseFilePath;\n        if (!FileSystem.exists(packageTSDocConfigPath)) {\n          throw new InternalError('Unable to load the built-in TSDoc config file: ' + packageTSDocConfigPath);\n        }\n      }\n      tsdocConfigFile = TSDocConfigFile.loadFile(packageTSDocConfigPath);\n    }\n\n    // IMPORTANT: After calling TSDocConfigFile.loadFile(), we need to check for errors.\n    if (tsdocConfigFile.hasErrors) {\n      throw new Error(tsdocConfigFile.getErrorSummary());\n    }\n\n    const tsdocConfiguration: TSDocConfiguration = new TSDocConfiguration();\n    tsdocConfigFile.configureParser(tsdocConfiguration);\n\n    // IMPORTANT: After calling TSDocConfigFile.configureParser(), we need to check for errors a second time.\n    if (tsdocConfigFile.hasErrors) {\n      throw new Error(tsdocConfigFile.getErrorSummary());\n    }\n\n    return new ExtractorConfig({ ...extractorConfigParameters, tsdocConfigFile, tsdocConfiguration });\n  }\n\n  /**\n   * Gets the report configuration for the \"complete\" (default) report configuration, if one was specified.\n   */\n  private _getCompleteReportConfig(): IExtractorConfigApiReport | undefined {\n    return this.reportConfigs.find((x) => x.variant === 'complete');\n  }\n\n  private static _resolvePathWithTokens(\n    fieldName: string,\n    value: string | undefined,\n    tokenContext: IExtractorConfigTokenContext\n  ): string {\n    value = ExtractorConfig._expandStringWithTokens(fieldName, value, tokenContext);\n    if (value !== '') {\n      value = path.resolve(tokenContext.projectFolder, value);\n    }\n    return value;\n  }\n\n  private static _expandStringWithTokens(\n    fieldName: string,\n    value: string | undefined,\n    tokenContext: IExtractorConfigTokenContext\n  ): string {\n    value = value ? value.trim() : '';\n    if (value !== '') {\n      value = Text.replaceAll(value, '<unscopedPackageName>', tokenContext.unscopedPackageName);\n      value = Text.replaceAll(value, '<packageName>', tokenContext.packageName);\n\n      const projectFolderToken: string = '<projectFolder>';\n      if (value.indexOf(projectFolderToken) === 0) {\n        // Replace \"<projectFolder>\" at the start of a string\n        value = path.join(tokenContext.projectFolder, value.substr(projectFolderToken.length));\n      }\n\n      if (value.indexOf(projectFolderToken) >= 0) {\n        // If after all replacements, \"<projectFolder>\" appears somewhere in the string, report an error\n        throw new Error(\n          `The \"${fieldName}\" value incorrectly uses the \"<projectFolder>\" token.` +\n            ` It must appear at the start of the string.`\n        );\n      }\n\n      if (value.indexOf('<lookup>') >= 0) {\n        throw new Error(`The \"${fieldName}\" value incorrectly uses the \"<lookup>\" token`);\n      }\n      ExtractorConfig._rejectAnyTokensInPath(value, fieldName);\n    }\n    return value;\n  }\n\n  /**\n   * Returns true if the specified file path has the \".d.ts\" file extension.\n   */\n  public static hasDtsFileExtension(filePath: string): boolean {\n    return ExtractorConfig._declarationFileExtensionRegExp.test(filePath);\n  }\n\n  /**\n   * Given a path string that may have originally contained expandable tokens such as `<projectFolder>\"`\n   * this reports an error if any token-looking substrings remain after expansion (e.g. `c:\\blah\\<invalid>\\blah`).\n   */\n  private static _rejectAnyTokensInPath(value: string, fieldName: string): void {\n    if (value.indexOf('<') < 0 && value.indexOf('>') < 0) {\n      return;\n    }\n\n    // Can we determine the name of a token?\n    const tokenRegExp: RegExp = /(\\<[^<]*?\\>)/;\n    const match: RegExpExecArray | null = tokenRegExp.exec(value);\n    if (match) {\n      throw new Error(`The \"${fieldName}\" value contains an unrecognized token \"${match[1]}\"`);\n    }\n    throw new Error(`The \"${fieldName}\" value contains extra token characters (\"<\" or \">\"): ${value}`);\n  }\n}\n\nconst releaseTags: Set<string> = new Set(['@public', '@alpha', '@beta', '@internal']);\n\n/**\n * Validate {@link ExtractorConfig.tagsToReport}.\n */\nfunction _validateTagsToReport(\n  tagsToReport: Record<string, boolean>\n): asserts tagsToReport is Record<`@${string}`, boolean> {\n  const includedReleaseTags: string[] = [];\n  const invalidTags: [string, string][] = []; // tag name, error\n  for (const tag of Object.keys(tagsToReport)) {\n    if (releaseTags.has(tag)) {\n      // If a release tags is specified, regardless of whether it is enabled, we will throw an error.\n      // Release tags must not be specified.\n      includedReleaseTags.push(tag);\n    }\n\n    // If the tag is invalid, generate an error string from the inner error message.\n    try {\n      TSDocTagDefinition.validateTSDocTagName(tag);\n    } catch (error) {\n      invalidTags.push([tag, (error as Error).message]);\n    }\n  }\n\n  const errorMessages: string[] = [];\n  for (const includedReleaseTag of includedReleaseTags) {\n    errorMessages.push(\n      `${includedReleaseTag}: Release tags are always included in API reports and must not be specified`\n    );\n  }\n  for (const [invalidTag, innerError] of invalidTags) {\n    errorMessages.push(`${invalidTag}: ${innerError}`);\n  }\n\n  if (errorMessages.length > 0) {\n    const errorMessage: string = [\n      `\"tagsToReport\" contained one or more invalid tags:`,\n      ...errorMessages\n    ].join('\\n\\t- ');\n    throw new Error(errorMessage);\n  }\n}\n"]}
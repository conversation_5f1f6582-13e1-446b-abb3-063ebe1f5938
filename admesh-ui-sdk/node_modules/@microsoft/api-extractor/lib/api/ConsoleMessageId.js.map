{"version": 3, "file": "ConsoleMessageId.js", "sourceRoot": "", "sources": ["../../src/api/ConsoleMessageId.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;;;;;;;;GASG;AACH,IAAY,gBA0EX;AA1ED,WAAY,gBAAgB;IAC1B;;OAEG;IACH,iDAA6B,CAAA;IAE7B;;;OAGG;IACH,6EAAyD,CAAA;IAEzD;;OAEG;IACH,gFAA4D,CAAA;IAE5D;;OAEG;IACH,uEAAmD,CAAA;IAEnD;;OAEG;IACH,0EAAsD,CAAA;IAEtD;;OAEG;IACH,mEAA+C,CAAA;IAE/C;;OAEG;IACH,mEAA+C,CAAA;IAE/C;;;;;;;;;;OAUG;IACH,wEAAoD,CAAA;IAEpD;;OAEG;IACH,iEAA6C,CAAA;IAE7C;;OAEG;IACH,uEAAmD,CAAA;IAEnD;;OAEG;IACH,mEAA+C,CAAA;IAE/C;;OAEG;IACH,gFAA4D,CAAA;IAE5D;;OAEG;IACH,uDAAmC,CAAA;AACrC,CAAC,EA1EW,gBAAgB,gCAAhB,gBAAgB,QA0E3B", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\n/**\n * Unique identifiers for console messages reported by API Extractor.\n *\n * @remarks\n *\n * These strings are possible values for the {@link ExtractorMessage.messageId} property\n * when the `ExtractorMessage.category` is {@link ExtractorMessageCategory.Console}.\n *\n * @public\n */\nexport enum ConsoleMessageId {\n  /**\n   * \"Analysis will use the bundled TypeScript version ___\"\n   */\n  Preamble = 'console-preamble',\n\n  /**\n   * \"The target project appears to use TypeScript ___ which is newer than the bundled compiler engine;\n   * consider upgrading API Extractor.\"\n   */\n  CompilerVersionNotice = 'console-compiler-version-notice',\n\n  /**\n   * \"Using custom TSDoc config from ___\"\n   */\n  UsingCustomTSDocConfig = 'console-using-custom-tsdoc-config',\n\n  /**\n   * \"Found metadata in ___\"\n   */\n  FoundTSDocMetadata = 'console-found-tsdoc-metadata',\n\n  /**\n   * \"Writing: ___\"\n   */\n  WritingDocModelFile = 'console-writing-doc-model-file',\n\n  /**\n   * \"Writing package typings: ___\"\n   */\n  WritingDtsRollup = 'console-writing-dts-rollup',\n\n  /**\n   * \"Generating ___ API report: ___\"\n   */\n  WritingApiReport = 'console-writing-api-report',\n\n  /**\n   * \"You have changed the public API signature for this project.\n   * Please copy the file ___ to ___, or perform a local build (which does this automatically).\n   * See the Git repo documentation for more info.\"\n   *\n   * OR\n   *\n   * \"The API report file is missing.\n   * Please copy the file ___ to ___, or perform a local build (which does this automatically).\n   * See the Git repo documentation for more info.\"\n   */\n  ApiReportNotCopied = 'console-api-report-not-copied',\n\n  /**\n   * \"You have changed the public API signature for this project.  Updating ___\"\n   */\n  ApiReportCopied = 'console-api-report-copied',\n\n  /**\n   * \"The API report is up to date: ___\"\n   */\n  ApiReportUnchanged = 'console-api-report-unchanged',\n\n  /**\n   * \"The API report file was missing, so a new file was created. Please add this file to Git: ___\"\n   */\n  ApiReportCreated = 'console-api-report-created',\n\n  /**\n   * \"Unable to create the API report file. Please make sure the target folder exists: ___\"\n   */\n  ApiReportFolderMissing = 'console-api-report-folder-missing',\n\n  /**\n   * Used for the information printed when the \"--diagnostics\" flag is enabled.\n   */\n  Diagnostics = 'console-diagnostics'\n}\n"]}
{"version": 3, "file": "CompilerState.js", "sourceRoot": "", "sources": ["../../src/api/CompilerState.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,2CAA6B;AAC7B,+CAAiC;AAEjC,oEAAwD;AAExD,uDAAoD;AAEpD,kDAA+C;AAgB/C;;;;;GAKG;AACH,MAAa,aAAa;IAMxB,YAAoB,UAAyB;QAC3C,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAClB,eAAgC,EAChC,OAAqC;QAErC,IAAI,QAAQ,GAAmB,eAAe,CAAC,gBAAgB,CAAC;QAChE,IAAI,cAAc,GAAW,eAAe,CAAC,aAAa,CAAC;QAC3D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,kDAAkD;YAClD,QAAQ,GAAG,4BAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAC3D,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAChF,CAAC;QAED,MAAM,WAAW,GAAyB,EAAE,CAAC,0BAA0B,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAE1G,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,IAAI,eAAe,CAAC,YAAY,EAAE,CAAC;YACtE,WAAW,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;YACxC,OAAO,CAAC,GAAG,CACT,mBAAQ,CAAC,IAAI,CACX,qFAAqF;gBACnF,0BAA0B,CAC7B,CACF,CAAC;QACJ,CAAC;QAED,MAAM,cAAc,GAAa,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC;QACtG,IAAI,OAAO,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAC7C,cAAc,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;QACxD,CAAC;QAED,6EAA6E;QAC7E,MAAM,iBAAiB,GAAa,aAAa,CAAC,6BAA6B,CAAC,cAAc,CAAC,CAAC;QAEhG,MAAM,YAAY,GAAoB,aAAa,CAAC,mBAAmB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAE9F,MAAM,OAAO,GAAe,EAAE,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAEnG,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,SAAS,GAAW,EAAE,CAAC,4BAA4B,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACnG,MAAM,IAAI,KAAK,CAAC,wCAAwC,SAAS,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,IAAI,aAAa,CAAC;YACvB,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;OAaG;IACK,MAAM,CAAC,6BAA6B,CAAC,cAAwB;QACnE,MAAM,iBAAiB,GAAa,EAAE,CAAC;QAEvC,MAAM,SAAS,GAAgB,IAAI,GAAG,EAAU,CAAC;QAEjD,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;YAC3C,MAAM,gBAAgB,GAAW,aAAa,CAAC,WAAW,EAAE,CAAC;YAC7D,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACrC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBAEhC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;oBACpC,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,aAAa,CAAC,CAAC;gBAC1E,CAAC;gBAED,IAAI,iCAAe,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;oBACvD,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAChC,WAAiC,EACjC,OAA4C;QAE5C,sDAAsD;QACtD,MAAM,YAAY,GAAoB,EAAE,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEjF,6FAA6F;QAC7F,wFAAwF;QACxF,MAAM,mBAAmB,GAAoB,EAAE,GAAG,YAAY,EAAE,CAAC;QAEjE,IAAI,OAAO,IAAI,OAAO,CAAC,wBAAwB,EAAE,CAAC;YAChD,8BAA8B;YAC9B,MAAM,2BAA2B,GAAW,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/F,YAAY,CAAC,qBAAqB,GAAG,GAAG,EAAE,CAAC,2BAA2B,CAAC;QACzE,CAAC;QAED,oCAAoC;QACpC,8CAA8C;QAC9C,MAAM,cAAc,GAAyB,IAAI,GAAG,EAAmB,CAAC;QAExE,oCAAoC;QACpC,oCAAoC;QACpC,MAAM,mBAAmB,GAAW,uBAAuB,CAAC;QAE5D,YAAY,CAAC,UAAU,GAAG,CAAC,QAAgB,EAAW,EAAE;YACtD,wFAAwF;YACxF,6FAA6F;YAC7F,gGAAgG;YAChG,2FAA2F;YAC3F,4BAA4B;YAE5B,2FAA2F;YAC3F,IAAI,CAAC,iCAAe,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnD,yEAAyE;gBACzE,MAAM,KAAK,GAA2B,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzE,IAAI,KAAK,EAAE,CAAC;oBACV,iCAAiC;oBACjC,MAAM,oBAAoB,GAAW,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC9C,iBAAiB;oBACjB,MAAM,aAAa,GAAW,KAAK,CAAC,CAAC,CAAC,CAAC;oBAEvC,QAAQ,aAAa,CAAC,iBAAiB,EAAE,EAAE,CAAC;wBAC1C,KAAK,KAAK,CAAC;wBACX,KAAK,MAAM,CAAC;wBACZ,KAAK,KAAK,CAAC;wBACX,KAAK,MAAM;4BACT,gGAAgG;4BAChG,MAAM,WAAW,GAAW,GAAG,oBAAoB,OAAO,CAAC;4BAE3D,IAAI,aAAa,GAAwB,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;4BACzE,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gCAChC,aAAa,GAAG,mBAAmB,CAAC,UAAW,CAAC,WAAW,CAAC,CAAC;gCAC7D,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;4BACjD,CAAC;4BAED,IAAI,aAAa,EAAE,CAAC;gCAClB,6EAA6E;gCAC7E,6FAA6F;gCAC7F,OAAO,KAAK,CAAC;4BACf,CAAC;4BACD,MAAM;oBACV,CAAC;gBACH,CAAC;YACH,CAAC;YAED,6CAA6C;YAC7C,OAAO,mBAAmB,CAAC,UAAW,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC,CAAC;QAEF,OAAO,YAAY,CAAC;IACtB,CAAC;CACF;AAxKD,sCAwKC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See L<PERSON>EN<PERSON> in the project root for license information.\n\nimport * as path from 'path';\nimport * as ts from 'typescript';\n\nimport { JsonFile } from '@rushstack/node-core-library';\n\nimport { ExtractorConfig } from './ExtractorConfig';\nimport type { IExtractorInvokeOptions } from './Extractor';\nimport { Colorize } from '@rushstack/terminal';\n\n/**\n * Options for {@link CompilerState.create}\n * @public\n */\nexport interface ICompilerStateCreateOptions {\n  /** {@inheritDoc IExtractorInvokeOptions.typescriptCompilerFolder} */\n  typescriptCompilerFolder?: string;\n\n  /**\n   * Additional .d.ts files to include in the analysis.\n   */\n  additionalEntryPoints?: string[];\n}\n\n/**\n * This class represents the TypeScript compiler state.  This allows an optimization where multiple invocations\n * of API Extractor can reuse the same TypeScript compiler analysis.\n *\n * @public\n */\nexport class CompilerState {\n  /**\n   * The TypeScript compiler's `Program` object, which represents a complete scope of analysis.\n   */\n  public readonly program: unknown;\n\n  private constructor(properties: CompilerState) {\n    this.program = properties.program;\n  }\n\n  /**\n   * Create a compiler state for use with the specified `IExtractorInvokeOptions`.\n   */\n  public static create(\n    extractorConfig: ExtractorConfig,\n    options?: ICompilerStateCreateOptions\n  ): CompilerState {\n    let tsconfig: {} | undefined = extractorConfig.overrideTsconfig;\n    let configBasePath: string = extractorConfig.projectFolder;\n    if (!tsconfig) {\n      // If it wasn't overridden, then load it from disk\n      tsconfig = JsonFile.load(extractorConfig.tsconfigFilePath);\n      configBasePath = path.resolve(path.dirname(extractorConfig.tsconfigFilePath));\n    }\n\n    const commandLine: ts.ParsedCommandLine = ts.parseJsonConfigFileContent(tsconfig, ts.sys, configBasePath);\n\n    if (!commandLine.options.skipLibCheck && extractorConfig.skipLibCheck) {\n      commandLine.options.skipLibCheck = true;\n      console.log(\n        Colorize.cyan(\n          'API Extractor was invoked with skipLibCheck. This is not recommended and may cause ' +\n            'incorrect type analysis.'\n        )\n      );\n    }\n\n    const inputFilePaths: string[] = commandLine.fileNames.concat(extractorConfig.mainEntryPointFilePath);\n    if (options && options.additionalEntryPoints) {\n      inputFilePaths.push(...options.additionalEntryPoints);\n    }\n\n    // Append the entry points and remove any non-declaration files from the list\n    const analysisFilePaths: string[] = CompilerState._generateFilePathsForAnalysis(inputFilePaths);\n\n    const compilerHost: ts.CompilerHost = CompilerState._createCompilerHost(commandLine, options);\n\n    const program: ts.Program = ts.createProgram(analysisFilePaths, commandLine.options, compilerHost);\n\n    if (commandLine.errors.length > 0) {\n      const errorText: string = ts.flattenDiagnosticMessageText(commandLine.errors[0].messageText, '\\n');\n      throw new Error(`Error parsing tsconfig.json content: ${errorText}`);\n    }\n\n    return new CompilerState({\n      program\n    });\n  }\n\n  /**\n   * Given a list of absolute file paths, return a list containing only the declaration\n   * files.  Duplicates are also eliminated.\n   *\n   * @remarks\n   * The tsconfig.json settings specify the compiler's input (a set of *.ts source files,\n   * plus some *.d.ts declaration files used for legacy typings).  However API Extractor\n   * analyzes the compiler's output (a set of *.d.ts entry point files, plus any legacy\n   * typings).  This requires API Extractor to generate a special file list when it invokes\n   * the compiler.\n   *\n   * Duplicates are removed so that entry points can be appended without worrying whether they\n   * may already appear in the tsconfig.json file list.\n   */\n  private static _generateFilePathsForAnalysis(inputFilePaths: string[]): string[] {\n    const analysisFilePaths: string[] = [];\n\n    const seenFiles: Set<string> = new Set<string>();\n\n    for (const inputFilePath of inputFilePaths) {\n      const inputFileToUpper: string = inputFilePath.toUpperCase();\n      if (!seenFiles.has(inputFileToUpper)) {\n        seenFiles.add(inputFileToUpper);\n\n        if (!path.isAbsolute(inputFilePath)) {\n          throw new Error('Input file is not an absolute path: ' + inputFilePath);\n        }\n\n        if (ExtractorConfig.hasDtsFileExtension(inputFilePath)) {\n          analysisFilePaths.push(inputFilePath);\n        }\n      }\n    }\n\n    return analysisFilePaths;\n  }\n\n  private static _createCompilerHost(\n    commandLine: ts.ParsedCommandLine,\n    options: IExtractorInvokeOptions | undefined\n  ): ts.CompilerHost {\n    // Create a default CompilerHost that we will override\n    const compilerHost: ts.CompilerHost = ts.createCompilerHost(commandLine.options);\n\n    // Save a copy of the original members.  Note that \"compilerHost\" cannot be the copy, because\n    // createCompilerHost() captures that instance in a closure that is used by the members.\n    const defaultCompilerHost: ts.CompilerHost = { ...compilerHost };\n\n    if (options && options.typescriptCompilerFolder) {\n      // Prevent a closure parameter\n      const typescriptCompilerLibFolder: string = path.join(options.typescriptCompilerFolder, 'lib');\n      compilerHost.getDefaultLibLocation = () => typescriptCompilerLibFolder;\n    }\n\n    // Used by compilerHost.fileExists()\n    // .d.ts file path --> whether the file exists\n    const dtsExistsCache: Map<string, boolean> = new Map<string, boolean>();\n\n    // Used by compilerHost.fileExists()\n    // Example: \"c:/folder/file.part.ts\"\n    const fileExtensionRegExp: RegExp = /^(.+)(\\.[a-z0-9_]+)$/i;\n\n    compilerHost.fileExists = (fileName: string): boolean => {\n      // In certain deprecated setups, the compiler may write its output files (.js and .d.ts)\n      // in the same folder as the corresponding input file (.ts or .tsx).  When following imports,\n      // API Extractor wants to analyze the .d.ts file; however recent versions of the compiler engine\n      // will instead choose the .ts file.  To work around this, we hook fileExists() to hide the\n      // existence of those files.\n\n      // Is \"fileName\" a .d.ts file?  The double extension \".d.ts\" needs to be matched specially.\n      if (!ExtractorConfig.hasDtsFileExtension(fileName)) {\n        // It's not a .d.ts file.  Is the file extension a potential source file?\n        const match: RegExpExecArray | null = fileExtensionRegExp.exec(fileName);\n        if (match) {\n          // Example: \"c:/folder/file.part\"\n          const pathWithoutExtension: string = match[1];\n          // Example: \".ts\"\n          const fileExtension: string = match[2];\n\n          switch (fileExtension.toLocaleLowerCase()) {\n            case '.ts':\n            case '.tsx':\n            case '.js':\n            case '.jsx':\n              // Yes, this is a possible source file.  Is there a corresponding .d.ts file in the same folder?\n              const dtsFileName: string = `${pathWithoutExtension}.d.ts`;\n\n              let dtsFileExists: boolean | undefined = dtsExistsCache.get(dtsFileName);\n              if (dtsFileExists === undefined) {\n                dtsFileExists = defaultCompilerHost.fileExists!(dtsFileName);\n                dtsExistsCache.set(dtsFileName, dtsFileExists);\n              }\n\n              if (dtsFileExists) {\n                // fileName is a potential source file and a corresponding .d.ts file exists.\n                // Thus, API Extractor should ignore this file (so the .d.ts file will get analyzed instead).\n                return false;\n              }\n              break;\n          }\n        }\n      }\n\n      // Fall through to the default implementation\n      return defaultCompilerHost.fileExists!(fileName);\n    };\n\n    return compilerHost;\n  }\n}\n"]}
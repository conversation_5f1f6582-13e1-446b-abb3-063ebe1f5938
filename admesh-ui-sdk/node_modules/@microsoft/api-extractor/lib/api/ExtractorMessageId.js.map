{"version": 3, "file": "ExtractorMessageId.js", "sourceRoot": "", "sources": ["../../src/api/ExtractorMessageId.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;;;;;;;;GASG;AACH,IAAY,kBA8GX;AA9GD,WAAY,kBAAkB;IAC5B;;OAEG;IACH,8DAAwC,CAAA;IAExC;;;;;;;;;;;;;;;;OAgBG;IACH,sDAAgC,CAAA;IAEhC;;OAEG;IACH,wEAAkD,CAAA;IAElD;;OAEG;IACH,8EAAwD,CAAA;IAExD;;OAEG;IACH,kEAA4C,CAAA;IAE5C;;OAEG;IACH,sEAAgD,CAAA;IAEhD;;OAEG;IACH,6DAAuC,CAAA;IAEvC;;OAEG;IACH,kFAA4D,CAAA;IAE5D;;OAEG;IACH,+EAAyD,CAAA;IAEzD;;OAEG;IACH,oFAA8D,CAAA;IAE9D;;OAEG;IACH,iFAA2D,CAAA;IAE3D;;OAEG;IACH,0FAAoE,CAAA;IAEpE;;;;;;;OAOG;IACH,gFAA0D,CAAA;IAE1D;;OAEG;IACH,gEAA0C,CAAA;IAE1C;;OAEG;IACH,2DAAqC,CAAA;IAErC;;OAEG;IACH,4DAAsC,CAAA;IAEtC;;OAEG;IACH,yDAAmC,CAAA;IAEnC;;;OAGG;IACH,qEAA+C,CAAA;AACjD,CAAC,EA9GW,kBAAkB,kCAAlB,kBAAkB,QA8G7B;AAEY,QAAA,sBAAsB,GAAgB,IAAI,GAAG,CAAS;IACjE,sBAAsB;IACtB,iBAAiB;IACjB,2BAA2B;IAC3B,8BAA8B;IAC9B,wBAAwB;IACxB,0BAA0B;IAC1B,qBAAqB;IACrB,gCAAgC;IAChC,+BAA+B;IAC/B,iCAAiC;IACjC,gCAAgC;IAChC,oCAAoC;IACpC,+BAA+B;IAC/B,uBAAuB;IACvB,oBAAoB;IACpB,qBAAqB;IACrB,mBAAmB;IACnB,0BAA0B;CAC3B,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\n/**\n * Unique identifiers for messages reported by API Extractor during its analysis.\n *\n * @remarks\n *\n * These strings are possible values for the {@link ExtractorMessage.messageId} property\n * when the `ExtractorMessage.category` is {@link ExtractorMessageCategory.Extractor}.\n *\n * @public\n */\nexport enum ExtractorMessageId {\n  /**\n   * \"The doc comment should not contain more than one release tag.\"\n   */\n  ExtraReleaseTag = 'ae-extra-release-tag',\n\n  /**\n   * \"Missing documentation for ___.\"\n   * @remarks\n   * The `ae-undocumented` message is only generated if the API report feature is enabled.\n   *\n   * Because the API report file already annotates undocumented items with `// (undocumented)`,\n   * the `ae-undocumented` message is not logged by default.  To see it, add a setting such as:\n   * ```json\n   * \"messages\": {\n   *   \"extractorMessageReporting\": {\n   *     \"ae-undocumented\": {\n   *       \"logLevel\": \"warning\"\n   *     }\n   *   }\n   *  }\n   * ```\n   */\n  Undocumented = 'ae-undocumented',\n\n  /**\n   * \"This symbol has another declaration with a different release tag.\"\n   */\n  DifferentReleaseTags = 'ae-different-release-tags',\n\n  /**\n   * \"The symbol ___ is marked as ___, but its signature references ___ which is marked as ___.\"\n   */\n  IncompatibleReleaseTags = 'ae-incompatible-release-tags',\n\n  /**\n   * \"___ is part of the package's API, but it is missing a release tag (`@alpha`, `@beta`, `@public`, or `@internal`).\"\n   */\n  MissingReleaseTag = 'ae-missing-release-tag',\n\n  /**\n   * \"The `@packageDocumentation` comment must appear at the top of entry point *.d.ts file.\"\n   */\n  MisplacedPackageTag = 'ae-misplaced-package-tag',\n\n  /**\n   * \"The symbol ___ needs to be exported by the entry point ___.\"\n   */\n  ForgottenExport = 'ae-forgotten-export',\n\n  /**\n   * \"The name ___ should be prefixed with an underscore because the declaration is marked as `@internal`.\"\n   */\n  InternalMissingUnderscore = 'ae-internal-missing-underscore',\n\n  /**\n   * \"Mixed release tags are not allowed for ___ because one of its declarations is marked as `@internal`.\"\n   */\n  InternalMixedReleaseTag = 'ae-internal-mixed-release-tag',\n\n  /**\n   * \"The `@preapproved` tag cannot be applied to ___ because it is not a supported declaration type.\"\n   */\n  PreapprovedUnsupportedType = 'ae-preapproved-unsupported-type',\n\n  /**\n   * \"The `@preapproved` tag cannot be applied to ___ without an `@internal` release tag.\"\n   */\n  PreapprovedBadReleaseTag = 'ae-preapproved-bad-release-tag',\n\n  /**\n   * \"The `@inheritDoc` reference could not be resolved.\"\n   */\n  UnresolvedInheritDocReference = 'ae-unresolved-inheritdoc-reference',\n\n  /**\n   * \"The `@inheritDoc` tag needs a TSDoc declaration reference; signature matching is not supported yet.\"\n   *\n   * @privateRemarks\n   * In the future, we will implement signature matching so that you can write `{@inheritDoc}` and API Extractor\n   * will find a corresponding member from a base class (or implemented interface).  Until then, the tag\n   * always needs an explicit declaration reference such as `{@inhertDoc MyBaseClass.sameMethod}`.\n   */\n  UnresolvedInheritDocBase = 'ae-unresolved-inheritdoc-base',\n\n  /**\n   * \"The `@inheritDoc` tag for ___ refers to its own declaration.\"\n   */\n  CyclicInheritDoc = 'ae-cyclic-inherit-doc',\n\n  /**\n   * \"The `@link` reference could not be resolved.\"\n   */\n  UnresolvedLink = 'ae-unresolved-link',\n\n  /**\n   * \"The doc comment for the property ___ must appear on the getter, not the setter.\"\n   */\n  SetterWithDocs = 'ae-setter-with-docs',\n\n  /**\n   * \"The property ___ has a setter but no getter.\"\n   */\n  MissingGetter = 'ae-missing-getter',\n\n  /**\n   * \"Incorrect file type; API Extractor expects to analyze compiler outputs with the .d.ts file extension.\n   * Troubleshooting tips: `https://api-extractor.com/link/dts-error`\"\n   */\n  WrongInputFileType = 'ae-wrong-input-file-type'\n}\n\nexport const allExtractorMessageIds: Set<string> = new Set<string>([\n  'ae-extra-release-tag',\n  'ae-undocumented',\n  'ae-different-release-tags',\n  'ae-incompatible-release-tags',\n  'ae-missing-release-tag',\n  'ae-misplaced-package-tag',\n  'ae-forgotten-export',\n  'ae-internal-missing-underscore',\n  'ae-internal-mixed-release-tag',\n  'ae-preapproved-unsupported-type',\n  'ae-preapproved-bad-release-tag',\n  'ae-unresolved-inheritdoc-reference',\n  'ae-unresolved-inheritdoc-base',\n  'ae-cyclic-inherit-doc',\n  'ae-unresolved-link',\n  'ae-setter-with-docs',\n  'ae-missing-getter',\n  'ae-wrong-input-file-type'\n]);\n"]}
{"version": 3, "file": "Extractor.js", "sourceRoot": "", "sources": ["../../src/api/Extractor.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,2CAA6B;AAC7B,+CAAiC;AACjC,+CAAiC;AACjC,iDAAmC;AACnC,oEAOsC;AAEtC,uDAAoF;AACpF,sDAAmD;AACnD,yEAAqF;AACrF,uEAAoE;AAEpE,yEAAsE;AACtE,+EAA4E;AAC5E,wEAAqE;AACrE,wEAAqE;AACrE,mDAAgD;AAEhD,8DAA2D;AAC3D,yDAAsD;AACtD,0DAA0D;AAC1D,4DAAyD;AA+DzD;;;;GAIG;AACH,MAAa,eAAe;IA4C1B,gBAAgB;IAChB,YAAmB,UAA2B;QAC5C,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;QAC9C,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QAClD,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;QACtC,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;IAC9C,CAAC;CACF;AArDD,0CAqDC;AAED;;;GAGG;AACH,MAAa,SAAS;IACpB;;OAEG;IACI,MAAM,KAAK,OAAO;QACvB,OAAO,SAAS,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,WAAW;QAC3B,OAAO,SAAS,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC;IAC1C,CAAC;IAEO,MAAM,CAAC,eAAe;QAC5B,OAAO,qCAAiB,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,mBAAmB,CAC/B,cAAsB,EACtB,OAAiC;QAEjC,MAAM,eAAe,GAAoB,iCAAe,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QAE5F,OAAO,SAAS,CAAC,MAAM,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,eAAgC,EAAE,OAAiC;QACtF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;QAED,MAAM,UAAU,GAAY,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC;QAExD,IAAI,aAAwC,CAAC;QAC7C,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,6BAAa,CAAC,MAAM,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,YAAY,GAAiB,IAAI,2BAAY,EAAE,CAAC;QAEtD,MAAM,aAAa,GAAkB,IAAI,6BAAa,CAAC;YACrD,oBAAoB,EAAE,eAAe,CAAC,aAAa;YACnD,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,cAAc,EAAE,eAAe,CAAC,QAAQ,IAAI,EAAE;YAC9C,mBAAmB,EAAE,CAAC,CAAC,OAAO,CAAC,mBAAmB;YAClD,eAAe,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe;YAC1C,kBAAkB,EAAE,eAAe,CAAC,kBAAkB;YACtD,YAAY;SACb,CAAC,CAAC;QAEH,IAAI,eAAe,CAAC,eAAe,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;YAC9F,IAAI,CAAC,wBAAI,CAAC,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC,QAAQ,EAAE,iCAAe,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAChG,aAAa,CAAC,UAAU,CACtB,mCAAgB,CAAC,sBAAsB,EACvC,iCAAiC,GAAG,eAAe,CAAC,eAAe,CAAC,QAAQ,CAC7E,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;QAEjE,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;YAClC,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAChC,aAAa,CAAC,mBAAmB,CAAC,gCAAgC,CAAC,CAAC;YACpE,aAAa,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACjE,aAAa,CAAC,mBAAmB,EAAE,CAAC;YAEpC,aAAa,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;YACtD,MAAM,yBAAyB,GAAW,6BAAa,CAAC,mBAAmB,CACxE,aAAa,CAAC,OAAsB,CAAC,kBAAkB,EAAE,CAC3D,CAAC;YACF,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,yBAAyB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;YACrF,aAAa,CAAC,mBAAmB,EAAE,CAAC;YAEpC,aAAa,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,CAAC;YACzD,kEAAkE;YAClE,MAAM,kBAAkB,GAAoB,8BAAe,CAAC,cAAc,CACxE,eAAe,CAAC,kBAAkB,CACnC,CAAC;YACF,MAAM,qBAAqB,GAAW,6BAAa,CAAC,mBAAmB,CACrE,kBAAkB,CAAC,YAAY,EAAE,CAClC,CAAC;YACF,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;YACjF,aAAa,CAAC,mBAAmB,EAAE,CAAC;QACtC,CAAC;QAED,MAAM,SAAS,GAAc,IAAI,qBAAS,CAAC;YACzC,OAAO,EAAE,aAAa,CAAC,OAAqB;YAC5C,aAAa;YACb,eAAe;YACf,YAAY;SACb,CAAC,CAAC;QAEH,SAAS,CAAC,OAAO,EAAE,CAAC;QAEpB,uCAAkB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACtC,uCAAkB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtC,MAAM,YAAY,GAAsB,IAAI,qCAAiB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QAC1F,MAAM,UAAU,GAAe,YAAY,CAAC,eAAe,EAAE,CAAC;QAE9D,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;YAClC,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,4CAA4C;QAC/E,CAAC;QAED,IAAI,YAAY,CAAC,eAAe,EAAE,CAAC;YACjC,aAAa,CAAC,UAAU,CACtB,mCAAgB,CAAC,mBAAmB,EACpC,WAAW,GAAG,eAAe,CAAC,eAAe,CAC9C,CAAC;YACF,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,eAAe,EAAE;gBACzD,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,WAAW,EAAE,SAAS,CAAC,OAAO;gBAE9B,iBAAiB,EAAE,eAAe,CAAC,WAAW;gBAC9C,kBAAkB,EAAE,IAAI;gBACxB,QAAQ,EAAE,eAAe,CAAC,QAAQ;aACnC,CAAC,CAAC;QACL,CAAC;QAED,SAAS,cAAc,CAAC,YAAuC;YAC7D,OAAO,SAAS,CAAC,eAAe,CAC9B,SAAS,EACT,eAAe,EACf,aAAa,EACb,eAAe,CAAC,gBAAgB,EAChC,eAAe,CAAC,YAAY,EAC5B,YAAY,EACZ,UAAU,CACX,CAAC;QACJ,CAAC;QAED,IAAI,gBAAgB,GAAY,KAAK,CAAC;QACtC,IAAI,eAAe,CAAC,gBAAgB,EAAE,CAAC;YACrC,KAAK,MAAM,YAAY,IAAI,eAAe,CAAC,aAAa,EAAE,CAAC;gBACzD,gBAAgB,GAAG,cAAc,CAAC,YAAY,CAAC,IAAI,gBAAgB,CAAC;YACtE,CAAC;QACH,CAAC;QAED,IAAI,eAAe,CAAC,aAAa,EAAE,CAAC;YAClC,SAAS,CAAC,sBAAsB,CAC9B,SAAS,EACT,eAAe,CAAC,qBAAqB,EACrC,kCAAa,CAAC,aAAa,EAC3B,eAAe,CAAC,WAAW,CAC5B,CAAC;YACF,SAAS,CAAC,sBAAsB,CAC9B,SAAS,EACT,eAAe,CAAC,oBAAoB,EACpC,kCAAa,CAAC,YAAY,EAC1B,eAAe,CAAC,WAAW,CAC5B,CAAC;YACF,SAAS,CAAC,sBAAsB,CAC9B,SAAS,EACT,eAAe,CAAC,mBAAmB,EACnC,kCAAa,CAAC,WAAW,EACzB,eAAe,CAAC,WAAW,CAC5B,CAAC;YACF,SAAS,CAAC,sBAAsB,CAC9B,SAAS,EACT,eAAe,CAAC,iBAAiB,EACjC,kCAAa,CAAC,eAAe,EAC7B,eAAe,CAAC,WAAW,CAC5B,CAAC;QACJ,CAAC;QAED,IAAI,eAAe,CAAC,oBAAoB,EAAE,CAAC;YACzC,sDAAsD;YACtD,+CAAsB,CAAC,sBAAsB,CAC3C,eAAe,CAAC,qBAAqB,EACrC,eAAe,CAAC,WAAW,CAC5B,CAAC;QACJ,CAAC;QAED,0DAA0D;QAC1D,aAAa,CAAC,iCAAiC,EAAE,CAAC;QAElD,oBAAoB;QACpB,IAAI,SAAkB,CAAC;QACvB,IAAI,UAAU,EAAE,CAAC;YACf,qEAAqE;YACrE,SAAS,GAAG,aAAa,CAAC,UAAU,KAAK,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,oEAAoE;YACpE,SAAS,GAAG,aAAa,CAAC,UAAU,GAAG,aAAa,CAAC,YAAY,KAAK,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,IAAI,eAAe,CAAC;YACzB,aAAa;YACb,eAAe;YACf,SAAS;YACT,gBAAgB,EAAE,gBAAgB;YAClC,UAAU,EAAE,aAAa,CAAC,UAAU;YACpC,YAAY,EAAE,aAAa,CAAC,YAAY;SACzC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACK,MAAM,CAAC,eAAe,CAC5B,SAAoB,EACpB,eAAgC,EAChC,aAA4B,EAC5B,uBAA+B,EAC/B,mBAA2B,EAC3B,YAAuC,EACvC,UAAmB;QAEnB,IAAI,gBAAgB,GAAY,KAAK,CAAC;QAEtC,MAAM,mBAAmB,GAAW,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;QACjG,MAAM,wBAAwB,GAAW,eAAe,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;QAEhG,MAAM,qBAAqB,GAAW,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC/F,MAAM,0BAA0B,GAAW,eAAe,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;QAEpG,SAAS,CAAC,aAAa,CAAC,UAAU,CAChC,mCAAgB,CAAC,gBAAgB,EACjC,cAAc,YAAY,CAAC,OAAO,gBAAgB,qBAAqB,EAAE,CAC1E,CAAC;QAEF,MAAM,sBAAsB,GAAW,uCAAkB,CAAC,yBAAyB,CACjF,SAAS,EACT,YAAY,CAAC,OAAO,CACrB,CAAC;QAEF,wBAAwB;QACxB,8BAAU,CAAC,SAAS,CAAC,mBAAmB,EAAE,sBAAsB,EAAE;YAChE,kBAAkB,EAAE,IAAI;YACxB,kBAAkB,EAAE,eAAe,CAAC,WAAW;SAChD,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,8BAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,EAAE,CAAC;YAC7C,MAAM,wBAAwB,GAAW,8BAAU,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;YAEpF,IACE,CAAC,uCAAkB,CAAC,4BAA4B,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,EAClG,CAAC;gBACD,gBAAgB,GAAG,IAAI,CAAC;gBAExB,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,wEAAwE;oBACxE,aAAa,CAAC,UAAU,CACtB,mCAAgB,CAAC,kBAAkB,EACnC,sDAAsD;wBACpD,0BAA0B,wBAAwB,SAAS,0BAA0B,IAAI;wBACzF,4DAA4D;wBAC5D,gDAAgD,CACnD,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,uDAAuD;oBACvD,aAAa,CAAC,UAAU,CACtB,mCAAgB,CAAC,eAAe,EAChC,iEAAiE,0BAA0B,EAAE,CAC9F,CAAC;oBAEF,8BAAU,CAAC,SAAS,CAAC,qBAAqB,EAAE,sBAAsB,EAAE;wBAClE,kBAAkB,EAAE,IAAI;wBACxB,kBAAkB,EAAE,eAAe,CAAC,WAAW;qBAChD,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,UAAU,CACtB,mCAAgB,CAAC,kBAAkB,EACnC,iCAAiC,wBAAwB,EAAE,CAC5D,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,+FAA+F;YAC/F,EAAE;YACF,0GAA0G;YAC1G,uGAAuG;YACvG,4DAA4D;YAC5D,gBAAgB,GAAG,IAAI,CAAC;YAExB,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,wEAAwE;gBACxE,aAAa,CAAC,UAAU,CACtB,mCAAgB,CAAC,kBAAkB,EACnC,iCAAiC;oBAC/B,0BAA0B,wBAAwB,SAAS,0BAA0B,IAAI;oBACzF,4DAA4D;oBAC5D,gDAAgD,CACnD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,uBAAuB,GAAW,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;gBAC5E,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,uBAAuB,CAAC,EAAE,CAAC;oBAChD,aAAa,CAAC,QAAQ,CACpB,mCAAgB,CAAC,sBAAsB,EACvC,oFAAoF;wBAClF,uBAAuB,CAC1B,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,8BAAU,CAAC,SAAS,CAAC,qBAAqB,EAAE,sBAAsB,EAAE;wBAClE,kBAAkB,EAAE,eAAe,CAAC,WAAW;qBAChD,CAAC,CAAC;oBACH,aAAa,CAAC,UAAU,CACtB,mCAAgB,CAAC,gBAAgB,EACjC,4FAA4F;wBAC1F,qBAAqB,CACxB,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEO,MAAM,CAAC,2BAA2B,CACxC,eAAgC,EAChC,aAA4B;QAE5B,aAAa,CAAC,OAAO,CACnB,mCAAgB,CAAC,QAAQ,EACzB,oDAAoD,EAAE,CAAC,OAAO,EAAE,CACjE,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,cAAc,GAAW,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE;gBACxD,OAAO,EAAE,eAAe,CAAC,aAAa;gBACtC,gBAAgB,EAAE,KAAK;aACxB,CAAC,CAAC;YACH,MAAM,iBAAiB,GAAsB,IAAI,qCAAiB,EAAE,CAAC;YACrE,MAAM,WAAW,GACf,iBAAiB,CAAC,yBAAyB,CAAC,cAAc,CAAC,CAAC;YAC9D,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5E,oDAAoD;gBACpD,MAAM,QAAQ,GAAW,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;gBAClD,MAAM,QAAQ,GAAW,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;gBAElD,MAAM,UAAU,GAAW,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAC7D,MAAM,UAAU,GAAW,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAE7D,IAAI,UAAU,GAAG,QAAQ,IAAI,CAAC,UAAU,KAAK,QAAQ,IAAI,UAAU,GAAG,QAAQ,CAAC,EAAE,CAAC;oBAChF,aAAa,CAAC,OAAO,CACnB,mCAAgB,CAAC,qBAAqB,EACtC,oDAAoD,WAAW,CAAC,OAAO,0BAA0B;wBAC/F,6DAA6D,CAChE,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,kFAAkF;QACpF,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,sBAAsB,CACnC,SAAoB,EACpB,UAAkB,EAClB,OAAsB,EACtB,WAAwB;QAExB,IAAI,UAAU,KAAK,EAAE,EAAE,CAAC;YACtB,SAAS,CAAC,aAAa,CAAC,UAAU,CAChC,mCAAgB,CAAC,gBAAgB,EACjC,4BAA4B,UAAU,EAAE,CACzC,CAAC;YACF,uCAAkB,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;CACF;AA5XD,8BA4XC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as path from 'path';\nimport * as semver from 'semver';\nimport * as ts from 'typescript';\nimport * as resolve from 'resolve';\nimport {\n  FileSystem,\n  type NewlineKind,\n  PackageJsonLookup,\n  type IPackageJson,\n  type INodePackageJson,\n  Path\n} from '@rushstack/node-core-library';\n\nimport { ExtractorConfig, type IExtractorConfigApiReport } from './ExtractorConfig';\nimport { Collector } from '../collector/Collector';\nimport { DtsRollupGenerator, DtsRollupKind } from '../generators/DtsRollupGenerator';\nimport { ApiModelGenerator } from '../generators/ApiModelGenerator';\nimport type { ApiPackage } from '@microsoft/api-extractor-model';\nimport { ApiReportGenerator } from '../generators/ApiReportGenerator';\nimport { PackageMetadataManager } from '../analyzer/PackageMetadataManager';\nimport { ValidationEnhancer } from '../enhancers/ValidationEnhancer';\nimport { DocCommentEnhancer } from '../enhancers/DocCommentEnhancer';\nimport { CompilerState } from './CompilerState';\nimport type { ExtractorMessage } from './ExtractorMessage';\nimport { MessageRouter } from '../collector/MessageRouter';\nimport { ConsoleMessageId } from './ConsoleMessageId';\nimport { TSDocConfigFile } from '@microsoft/tsdoc-config';\nimport { SourceMapper } from '../collector/SourceMapper';\n\n/**\n * Runtime options for Extractor.\n *\n * @public\n */\nexport interface IExtractorInvokeOptions {\n  /**\n   * An optional TypeScript compiler state.  This allows an optimization where multiple invocations of API Extractor\n   * can reuse the same TypeScript compiler analysis.\n   */\n  compilerState?: CompilerState;\n\n  /**\n   * Indicates that API Extractor is running as part of a local build, e.g. on developer's\n   * machine.\n   *\n   * @remarks\n   * This disables certain validation that would normally be performed for a ship/production build. For example,\n   * the *.api.md report file is automatically updated in a local build.\n   *\n   * The default value is false.\n   */\n  localBuild?: boolean;\n\n  /**\n   * If true, API Extractor will include {@link ExtractorLogLevel.Verbose} messages in its output.\n   */\n  showVerboseMessages?: boolean;\n\n  /**\n   * If true, API Extractor will print diagnostic information used for troubleshooting problems.\n   * These messages will be included as {@link ExtractorLogLevel.Verbose} output.\n   *\n   * @remarks\n   * Setting `showDiagnostics=true` forces `showVerboseMessages=true`.\n   */\n  showDiagnostics?: boolean;\n\n  /**\n   * Specifies an alternate folder path to be used when loading the TypeScript system typings.\n   *\n   * @remarks\n   * API Extractor uses its own TypeScript compiler engine to analyze your project.  If your project\n   * is built with a significantly different TypeScript version, sometimes API Extractor may report compilation\n   * errors due to differences in the system typings (e.g. lib.dom.d.ts).  You can use the \"--typescriptCompilerFolder\"\n   * option to specify the folder path where you installed the TypeScript package, and API Extractor's compiler will\n   * use those system typings instead.\n   */\n  typescriptCompilerFolder?: string;\n\n  /**\n   * An optional callback function that will be called for each `ExtractorMessage` before it is displayed by\n   * API Extractor.  The callback can customize the message, handle it, or discard it.\n   *\n   * @remarks\n   * If a `messageCallback` is not provided, then by default API Extractor will print the messages to\n   * the STDERR/STDOUT console.\n   */\n  messageCallback?: (message: ExtractorMessage) => void;\n}\n\n/**\n * This object represents the outcome of an invocation of API Extractor.\n *\n * @public\n */\nexport class ExtractorResult {\n  /**\n   * The TypeScript compiler state that was used.\n   */\n  public readonly compilerState: CompilerState;\n\n  /**\n   * The API Extractor configuration that was used.\n   */\n  public readonly extractorConfig: ExtractorConfig;\n\n  /**\n   * Whether the invocation of API Extractor was successful.  For example, if `succeeded` is false, then the build task\n   * would normally return a nonzero process exit code, indicating that the operation failed.\n   *\n   * @remarks\n   *\n   * Normally the operation \"succeeds\" if `errorCount` and `warningCount` are both zero.  However if\n   * {@link IExtractorInvokeOptions.localBuild} is `true`, then the operation \"succeeds\" if `errorCount` is zero\n   * (i.e. warnings are ignored).\n   */\n  public readonly succeeded: boolean;\n\n  /**\n   * Returns true if the API report was found to have changed.\n   */\n  public readonly apiReportChanged: boolean;\n\n  /**\n   * Reports the number of errors encountered during analysis.\n   *\n   * @remarks\n   * This does not count exceptions, where unexpected issues prematurely abort the operation.\n   */\n  public readonly errorCount: number;\n\n  /**\n   * Reports the number of warnings encountered during analysis.\n   *\n   * @remarks\n   * This does not count warnings that are emitted in the API report file.\n   */\n  public readonly warningCount: number;\n\n  /** @internal */\n  public constructor(properties: ExtractorResult) {\n    this.compilerState = properties.compilerState;\n    this.extractorConfig = properties.extractorConfig;\n    this.succeeded = properties.succeeded;\n    this.apiReportChanged = properties.apiReportChanged;\n    this.errorCount = properties.errorCount;\n    this.warningCount = properties.warningCount;\n  }\n}\n\n/**\n * The starting point for invoking the API Extractor tool.\n * @public\n */\nexport class Extractor {\n  /**\n   * Returns the version number of the API Extractor NPM package.\n   */\n  public static get version(): string {\n    return Extractor._getPackageJson().version;\n  }\n\n  /**\n   * Returns the package name of the API Extractor NPM package.\n   */\n  public static get packageName(): string {\n    return Extractor._getPackageJson().name;\n  }\n\n  private static _getPackageJson(): IPackageJson {\n    return PackageJsonLookup.loadOwnPackageJson(__dirname);\n  }\n\n  /**\n   * Load the api-extractor.json config file from the specified path, and then invoke API Extractor.\n   */\n  public static loadConfigAndInvoke(\n    configFilePath: string,\n    options?: IExtractorInvokeOptions\n  ): ExtractorResult {\n    const extractorConfig: ExtractorConfig = ExtractorConfig.loadFileAndPrepare(configFilePath);\n\n    return Extractor.invoke(extractorConfig, options);\n  }\n\n  /**\n   * Invoke API Extractor using an already prepared `ExtractorConfig` object.\n   */\n  public static invoke(extractorConfig: ExtractorConfig, options?: IExtractorInvokeOptions): ExtractorResult {\n    if (!options) {\n      options = {};\n    }\n\n    const localBuild: boolean = options.localBuild || false;\n\n    let compilerState: CompilerState | undefined;\n    if (options.compilerState) {\n      compilerState = options.compilerState;\n    } else {\n      compilerState = CompilerState.create(extractorConfig, options);\n    }\n\n    const sourceMapper: SourceMapper = new SourceMapper();\n\n    const messageRouter: MessageRouter = new MessageRouter({\n      workingPackageFolder: extractorConfig.packageFolder,\n      messageCallback: options.messageCallback,\n      messagesConfig: extractorConfig.messages || {},\n      showVerboseMessages: !!options.showVerboseMessages,\n      showDiagnostics: !!options.showDiagnostics,\n      tsdocConfiguration: extractorConfig.tsdocConfiguration,\n      sourceMapper\n    });\n\n    if (extractorConfig.tsdocConfigFile.filePath && !extractorConfig.tsdocConfigFile.fileNotFound) {\n      if (!Path.isEqual(extractorConfig.tsdocConfigFile.filePath, ExtractorConfig._tsdocBaseFilePath)) {\n        messageRouter.logVerbose(\n          ConsoleMessageId.UsingCustomTSDocConfig,\n          'Using custom TSDoc config from ' + extractorConfig.tsdocConfigFile.filePath\n        );\n      }\n    }\n\n    this._checkCompilerCompatibility(extractorConfig, messageRouter);\n\n    if (messageRouter.showDiagnostics) {\n      messageRouter.logDiagnostic('');\n      messageRouter.logDiagnosticHeader('Final prepared ExtractorConfig');\n      messageRouter.logDiagnostic(extractorConfig.getDiagnosticDump());\n      messageRouter.logDiagnosticFooter();\n\n      messageRouter.logDiagnosticHeader('Compiler options');\n      const serializedCompilerOptions: object = MessageRouter.buildJsonDumpObject(\n        (compilerState.program as ts.Program).getCompilerOptions()\n      );\n      messageRouter.logDiagnostic(JSON.stringify(serializedCompilerOptions, undefined, 2));\n      messageRouter.logDiagnosticFooter();\n\n      messageRouter.logDiagnosticHeader('TSDoc configuration');\n      // Convert the TSDocConfiguration into a tsdoc.json representation\n      const combinedConfigFile: TSDocConfigFile = TSDocConfigFile.loadFromParser(\n        extractorConfig.tsdocConfiguration\n      );\n      const serializedTSDocConfig: object = MessageRouter.buildJsonDumpObject(\n        combinedConfigFile.saveToObject()\n      );\n      messageRouter.logDiagnostic(JSON.stringify(serializedTSDocConfig, undefined, 2));\n      messageRouter.logDiagnosticFooter();\n    }\n\n    const collector: Collector = new Collector({\n      program: compilerState.program as ts.Program,\n      messageRouter,\n      extractorConfig,\n      sourceMapper\n    });\n\n    collector.analyze();\n\n    DocCommentEnhancer.analyze(collector);\n    ValidationEnhancer.analyze(collector);\n\n    const modelBuilder: ApiModelGenerator = new ApiModelGenerator(collector, extractorConfig);\n    const apiPackage: ApiPackage = modelBuilder.buildApiPackage();\n\n    if (messageRouter.showDiagnostics) {\n      messageRouter.logDiagnostic(''); // skip a line after any diagnostic messages\n    }\n\n    if (modelBuilder.docModelEnabled) {\n      messageRouter.logVerbose(\n        ConsoleMessageId.WritingDocModelFile,\n        'Writing: ' + extractorConfig.apiJsonFilePath\n      );\n      apiPackage.saveToJsonFile(extractorConfig.apiJsonFilePath, {\n        toolPackage: Extractor.packageName,\n        toolVersion: Extractor.version,\n\n        newlineConversion: extractorConfig.newlineKind,\n        ensureFolderExists: true,\n        testMode: extractorConfig.testMode\n      });\n    }\n\n    function writeApiReport(reportConfig: IExtractorConfigApiReport): boolean {\n      return Extractor._writeApiReport(\n        collector,\n        extractorConfig,\n        messageRouter,\n        extractorConfig.reportTempFolder,\n        extractorConfig.reportFolder,\n        reportConfig,\n        localBuild\n      );\n    }\n\n    let anyReportChanged: boolean = false;\n    if (extractorConfig.apiReportEnabled) {\n      for (const reportConfig of extractorConfig.reportConfigs) {\n        anyReportChanged = writeApiReport(reportConfig) || anyReportChanged;\n      }\n    }\n\n    if (extractorConfig.rollupEnabled) {\n      Extractor._generateRollupDtsFile(\n        collector,\n        extractorConfig.publicTrimmedFilePath,\n        DtsRollupKind.PublicRelease,\n        extractorConfig.newlineKind\n      );\n      Extractor._generateRollupDtsFile(\n        collector,\n        extractorConfig.alphaTrimmedFilePath,\n        DtsRollupKind.AlphaRelease,\n        extractorConfig.newlineKind\n      );\n      Extractor._generateRollupDtsFile(\n        collector,\n        extractorConfig.betaTrimmedFilePath,\n        DtsRollupKind.BetaRelease,\n        extractorConfig.newlineKind\n      );\n      Extractor._generateRollupDtsFile(\n        collector,\n        extractorConfig.untrimmedFilePath,\n        DtsRollupKind.InternalRelease,\n        extractorConfig.newlineKind\n      );\n    }\n\n    if (extractorConfig.tsdocMetadataEnabled) {\n      // Write the tsdoc-metadata.json file for this project\n      PackageMetadataManager.writeTsdocMetadataFile(\n        extractorConfig.tsdocMetadataFilePath,\n        extractorConfig.newlineKind\n      );\n    }\n\n    // Show all the messages that we collected during analysis\n    messageRouter.handleRemainingNonConsoleMessages();\n\n    // Determine success\n    let succeeded: boolean;\n    if (localBuild) {\n      // For a local build, fail if there were errors (but ignore warnings)\n      succeeded = messageRouter.errorCount === 0;\n    } else {\n      // For a production build, fail if there were any errors or warnings\n      succeeded = messageRouter.errorCount + messageRouter.warningCount === 0;\n    }\n\n    return new ExtractorResult({\n      compilerState,\n      extractorConfig,\n      succeeded,\n      apiReportChanged: anyReportChanged,\n      errorCount: messageRouter.errorCount,\n      warningCount: messageRouter.warningCount\n    });\n  }\n\n  /**\n   * Generates the API report at the specified release level, writes it to the specified file path, and compares\n   * the output to the existing report (if one exists).\n   *\n   * @param reportTempDirectoryPath - The path to the directory under which the temp report file will be written prior\n   * to comparison with an existing report.\n   * @param reportDirectoryPath - The path to the directory under which the existing report file is located, and to\n   * which the new report will be written post-comparison.\n   * @param reportConfig - API report configuration, including its file name and {@link ApiReportVariant}.\n   *\n   * @returns Whether or not the newly generated report differs from the existing report (if one exists).\n   */\n  private static _writeApiReport(\n    collector: Collector,\n    extractorConfig: ExtractorConfig,\n    messageRouter: MessageRouter,\n    reportTempDirectoryPath: string,\n    reportDirectoryPath: string,\n    reportConfig: IExtractorConfigApiReport,\n    localBuild: boolean\n  ): boolean {\n    let apiReportChanged: boolean = false;\n\n    const actualApiReportPath: string = path.resolve(reportTempDirectoryPath, reportConfig.fileName);\n    const actualApiReportShortPath: string = extractorConfig._getShortFilePath(actualApiReportPath);\n\n    const expectedApiReportPath: string = path.resolve(reportDirectoryPath, reportConfig.fileName);\n    const expectedApiReportShortPath: string = extractorConfig._getShortFilePath(expectedApiReportPath);\n\n    collector.messageRouter.logVerbose(\n      ConsoleMessageId.WritingApiReport,\n      `Generating ${reportConfig.variant} API report: ${expectedApiReportPath}`\n    );\n\n    const actualApiReportContent: string = ApiReportGenerator.generateReviewFileContent(\n      collector,\n      reportConfig.variant\n    );\n\n    // Write the actual file\n    FileSystem.writeFile(actualApiReportPath, actualApiReportContent, {\n      ensureFolderExists: true,\n      convertLineEndings: extractorConfig.newlineKind\n    });\n\n    // Compare it against the expected file\n    if (FileSystem.exists(expectedApiReportPath)) {\n      const expectedApiReportContent: string = FileSystem.readFile(expectedApiReportPath);\n\n      if (\n        !ApiReportGenerator.areEquivalentApiFileContents(actualApiReportContent, expectedApiReportContent)\n      ) {\n        apiReportChanged = true;\n\n        if (!localBuild) {\n          // For a production build, issue a warning that will break the CI build.\n          messageRouter.logWarning(\n            ConsoleMessageId.ApiReportNotCopied,\n            'You have changed the API signature for this project.' +\n              ` Please copy the file \"${actualApiReportShortPath}\" to \"${expectedApiReportShortPath}\",` +\n              ` or perform a local build (which does this automatically).` +\n              ` See the Git repo documentation for more info.`\n          );\n        } else {\n          // For a local build, just copy the file automatically.\n          messageRouter.logWarning(\n            ConsoleMessageId.ApiReportCopied,\n            `You have changed the API signature for this project. Updating ${expectedApiReportShortPath}`\n          );\n\n          FileSystem.writeFile(expectedApiReportPath, actualApiReportContent, {\n            ensureFolderExists: true,\n            convertLineEndings: extractorConfig.newlineKind\n          });\n        }\n      } else {\n        messageRouter.logVerbose(\n          ConsoleMessageId.ApiReportUnchanged,\n          `The API report is up to date: ${actualApiReportShortPath}`\n        );\n      }\n    } else {\n      // The target file does not exist, so we are setting up the API review file for the first time.\n      //\n      // NOTE: People sometimes make a mistake where they move a project and forget to update the \"reportFolder\"\n      // setting, which causes a new file to silently get written to the wrong place.  This can be confusing.\n      // Thus we treat the initial creation of the file specially.\n      apiReportChanged = true;\n\n      if (!localBuild) {\n        // For a production build, issue a warning that will break the CI build.\n        messageRouter.logWarning(\n          ConsoleMessageId.ApiReportNotCopied,\n          'The API report file is missing.' +\n            ` Please copy the file \"${actualApiReportShortPath}\" to \"${expectedApiReportShortPath}\",` +\n            ` or perform a local build (which does this automatically).` +\n            ` See the Git repo documentation for more info.`\n        );\n      } else {\n        const expectedApiReportFolder: string = path.dirname(expectedApiReportPath);\n        if (!FileSystem.exists(expectedApiReportFolder)) {\n          messageRouter.logError(\n            ConsoleMessageId.ApiReportFolderMissing,\n            'Unable to create the API report file. Please make sure the target folder exists:\\n' +\n              expectedApiReportFolder\n          );\n        } else {\n          FileSystem.writeFile(expectedApiReportPath, actualApiReportContent, {\n            convertLineEndings: extractorConfig.newlineKind\n          });\n          messageRouter.logWarning(\n            ConsoleMessageId.ApiReportCreated,\n            'The API report file was missing, so a new file was created. Please add this file to Git:\\n' +\n              expectedApiReportPath\n          );\n        }\n      }\n    }\n    return apiReportChanged;\n  }\n\n  private static _checkCompilerCompatibility(\n    extractorConfig: ExtractorConfig,\n    messageRouter: MessageRouter\n  ): void {\n    messageRouter.logInfo(\n      ConsoleMessageId.Preamble,\n      `Analysis will use the bundled TypeScript version ${ts.version}`\n    );\n\n    try {\n      const typescriptPath: string = resolve.sync('typescript', {\n        basedir: extractorConfig.projectFolder,\n        preserveSymlinks: false\n      });\n      const packageJsonLookup: PackageJsonLookup = new PackageJsonLookup();\n      const packageJson: INodePackageJson | undefined =\n        packageJsonLookup.tryLoadNodePackageJsonFor(typescriptPath);\n      if (packageJson && packageJson.version && semver.valid(packageJson.version)) {\n        // Consider a newer MINOR release to be incompatible\n        const ourMajor: number = semver.major(ts.version);\n        const ourMinor: number = semver.minor(ts.version);\n\n        const theirMajor: number = semver.major(packageJson.version);\n        const theirMinor: number = semver.minor(packageJson.version);\n\n        if (theirMajor > ourMajor || (theirMajor === ourMajor && theirMinor > ourMinor)) {\n          messageRouter.logInfo(\n            ConsoleMessageId.CompilerVersionNotice,\n            `*** The target project appears to use TypeScript ${packageJson.version} which is newer than the` +\n              ` bundled compiler engine; consider upgrading API Extractor.`\n          );\n        }\n      }\n    } catch (e) {\n      // The compiler detection heuristic is not expected to work in many configurations\n    }\n  }\n\n  private static _generateRollupDtsFile(\n    collector: Collector,\n    outputPath: string,\n    dtsKind: DtsRollupKind,\n    newlineKind: NewlineKind\n  ): void {\n    if (outputPath !== '') {\n      collector.messageRouter.logVerbose(\n        ConsoleMessageId.WritingDtsRollup,\n        `Writing package typings: ${outputPath}`\n      );\n      DtsRollupGenerator.writeTypingsFile(collector, outputPath, dtsKind, newlineKind);\n    }\n  }\n}\n"]}
{"version": 3, "file": "IConfigFile.js", "sourceRoot": "", "sources": ["../../src/api/IConfigFile.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\nimport type { EnumMemberOrder } from '@microsoft/api-extractor-model';\nimport type { ExtractorLogLevel } from './ExtractorLogLevel';\n\n/**\n * Determines how the TypeScript compiler engine will be invoked by API Extractor.\n *\n * @remarks\n * This is part of the {@link IConfigFile} structure.\n *\n * @public\n */\nexport interface IConfigCompiler {\n  /**\n   * Specifies the path to the tsconfig.json file to be used by API Extractor when analyzing the project.\n   *\n   * @remarks\n   * The path is resolved relative to the folder of the config file that contains the setting; to change this,\n   * prepend a folder token such as `<projectFolder>`.\n   *\n   * Note: This setting will be ignored if `overrideTsconfig` is used.\n   */\n  tsconfigFilePath?: string;\n\n  /**\n   * Provides a compiler configuration that will be used instead of reading the tsconfig.json file from disk.\n   *\n   * @remarks\n   * The value must conform to the TypeScript tsconfig schema:\n   *\n   * http://json.schemastore.org/tsconfig\n   *\n   * If omitted, then the tsconfig.json file will instead be read from the projectFolder.\n   */\n  overrideTsconfig?: {};\n\n  /**\n   * This option causes the compiler to be invoked with the `--skipLibCheck` option.\n   *\n   * @remarks\n   * This option is not recommended and may cause API Extractor to produce incomplete or incorrect declarations,\n   * but it may be required when dependencies contain declarations that are incompatible with the TypeScript engine\n   * that API Extractor uses for its analysis.  Where possible, the underlying issue should be fixed rather than\n   * relying on skipLibCheck.\n   */\n  skipLibCheck?: boolean;\n}\n\n/**\n * The allowed variations of API reports.\n *\n * @public\n */\nexport type ApiReportVariant = 'public' | 'beta' | 'alpha' | 'complete';\n\n/**\n * Configures how the API report files (*.api.md) will be generated.\n *\n * @remarks\n * This is part of the {@link IConfigFile} structure.\n *\n * @public\n */\nexport interface IConfigApiReport {\n  /**\n   * Whether to generate an API report.\n   */\n  enabled: boolean;\n\n  /**\n   * The base filename for the API report files, to be combined with {@link IConfigApiReport.reportFolder} or\n   * {@link IConfigApiReport.reportTempFolder} to produce the full file path.\n   *\n   * @remarks\n   * The `reportFileName` should not include any path separators such as `\\` or `/`.  The `reportFileName` should\n   * not include a file extension, since API Extractor will automatically append an appropriate file extension such\n   * as `.api.md`.  If the {@link IConfigApiReport.reportVariants} setting is used, then the file extension includes\n   * the variant name, for example `my-report.public.api.md` or `my-report.beta.api.md`. The `complete` variant always\n   * uses the simple extension `my-report.api.md`.\n   *\n   * Previous versions of API Extractor required `reportFileName` to include the `.api.md` extension explicitly;\n   * for backwards compatibility, that is still accepted but will be discarded before applying the above rules.\n   *\n   * @defaultValue `<unscopedPackageName>`\n   */\n  reportFileName?: string;\n\n  /**\n   * The set of report variants to generate.\n   *\n   * @remarks\n   * To support different approval requirements for different API levels, multiple \"variants\" of the API report can\n   * be generated.  The `reportVariants` setting specifies a list of variants to be generated.  If omitted,\n   * by default only the `complete` variant will be generated, which includes all `@internal`, `@alpha`, `@beta`,\n   * and `@public` items.  Other possible variants are `alpha` (`@alpha` + `@beta` + `@public`),\n   * `beta` (`@beta` + `@public`), and `public` (`@public only`).\n   *\n   * The resulting API report file names will be derived from the {@link IConfigApiReport.reportFileName}.\n   *\n   * @defaultValue `[ \"complete\" ]`\n   */\n  reportVariants?: ApiReportVariant[];\n\n  /**\n   * Specifies the folder where the API report file is written.  The file name portion is determined by\n   * the `reportFileName` setting.\n   *\n   * @remarks\n   * The API report file is normally tracked by Git.  Changes to it can be used to trigger a branch policy,\n   * e.g. for an API review.\n   *\n   * The path is resolved relative to the folder of the config file that contains the setting; to change this,\n   * prepend a folder token such as `<projectFolder>`.\n   */\n  reportFolder?: string;\n\n  /**\n   * Specifies the folder where the temporary report file is written.  The file name portion is determined by\n   * the `reportFileName` setting.\n   *\n   * @remarks\n   * After the temporary file is written to disk, it is compared with the file in the `reportFolder`.\n   * If they are different, a production build will fail.\n   *\n   * The path is resolved relative to the folder of the config file that contains the setting; to change this,\n   * prepend a folder token such as `<projectFolder>`.\n   */\n  reportTempFolder?: string;\n\n  /**\n   * Whether \"forgotten exports\" should be included in the API report file.\n   *\n   * @remarks\n   * Forgotten exports are declarations flagged with `ae-forgotten-export` warnings. See\n   * https://api-extractor.com/pages/messages/ae-forgotten-export/ to learn more.\n   *\n   * @defaultValue `false`\n   */\n  includeForgottenExports?: boolean;\n\n  /**\n   * Specifies a list of {@link https://tsdoc.org/ | TSDoc} tags that should be reported in the API report file for\n   * items whose documentation contains them.\n   *\n   * @remarks\n   * Tag names must begin with `@`.\n   *\n   * This list may include standard TSDoc tags as well as custom ones.\n   * For more information on defining custom TSDoc tags, see\n   * {@link https://api-extractor.com/pages/configs/tsdoc_json/#defining-your-own-tsdoc-tags | here}.\n   *\n   * Note that an item's release tag will always reported; this behavior cannot be overridden.\n   *\n   * @defaultValue `@sealed`, `@virtual`, `@override`, `@eventProperty`, and `@deprecated`\n   *\n   * @example Omitting default tags\n   * To omit the `@sealed` and `@virtual` tags from API reports, you would specify `tagsToReport` as follows:\n   * ```json\n   * \"tagsToReport\": {\n   *  \"@sealed\": false,\n   *  \"@virtual\": false\n   * }\n   * ```\n   *\n   * @example Including additional tags\n   * To include additional tags to the set included in API reports, you could specify `tagsToReport` like this:\n   * ```json\n   * \"tagsToReport\": {\n   *  \"@customTag\": true\n   * }\n   * ```\n   * This will result in `@customTag` being included in addition to the default tags.\n   */\n  tagsToReport?: Readonly<Record<`@${string}`, boolean>>;\n}\n\n/**\n * The allowed release tags that can be used to mark API items.\n * @public\n */\nexport type ReleaseTagForTrim = '@internal' | '@alpha' | '@beta' | '@public';\n\n/**\n * Configures how the doc model file (*.api.json) will be generated.\n *\n * @remarks\n * This is part of the {@link IConfigFile} structure.\n *\n * @public\n */\nexport interface IConfigDocModel {\n  /**\n   * Whether to generate a doc model file.\n   */\n  enabled: boolean;\n\n  /**\n   * The output path for the doc model file.  The file extension should be \".api.json\".\n   *\n   * @remarks\n   * The path is resolved relative to the folder of the config file that contains the setting; to change this,\n   * prepend a folder token such as `<projectFolder>`.\n   */\n  apiJsonFilePath?: string;\n\n  /**\n   * Whether \"forgotten exports\" should be included in the doc model file.\n   *\n   * @remarks\n   * Forgotten exports are declarations flagged with `ae-forgotten-export` warnings. See\n   * https://api-extractor.com/pages/messages/ae-forgotten-export/ to learn more.\n   *\n   * @defaultValue `false`\n   */\n  includeForgottenExports?: boolean;\n\n  /**\n   * The base URL where the project's source code can be viewed on a website such as GitHub or\n   * Azure DevOps. This URL path corresponds to the `<projectFolder>` path on disk.\n   *\n   * @remarks\n   * This URL is concatenated with the file paths serialized to the doc model to produce URL file paths to individual API items.\n   * For example, if the `projectFolderUrl` is \"https://github.com/microsoft/rushstack/tree/main/apps/api-extractor\" and an API\n   * item's file path is \"api/ExtractorConfig.ts\", the full URL file path would be\n   * \"https://github.com/microsoft/rushstack/tree/main/apps/api-extractor/api/ExtractorConfig.js\".\n   *\n   * Can be omitted if you don't need source code links in your API documentation reference.\n   */\n  projectFolderUrl?: string;\n\n  /**\n   * Specifies a list of release tags that will be trimmed from the doc model.\n   *\n   * @defaultValue `[\"@internal\"]`\n   */\n  releaseTagsToTrim?: ReleaseTagForTrim[];\n}\n\n/**\n * Configures how the .d.ts rollup file will be generated.\n *\n * @remarks\n * This is part of the {@link IConfigFile} structure.\n *\n * @public\n */\nexport interface IConfigDtsRollup {\n  /**\n   * Whether to generate the .d.ts rollup file.\n   */\n  enabled: boolean;\n\n  /**\n   * Specifies the output path for a .d.ts rollup file to be generated without any trimming.\n   *\n   * @remarks\n   * This file will include all declarations that are exported by the main entry point.\n   *\n   * If the path is an empty string, then this file will not be written.\n   *\n   * The path is resolved relative to the folder of the config file that contains the setting; to change this,\n   * prepend a folder token such as `<projectFolder>`.\n   */\n  untrimmedFilePath?: string;\n\n  /**\n   * Specifies the output path for a .d.ts rollup file to be generated with trimming for an \"alpha\" release.\n   *\n   * @remarks\n   * This file will include only declarations that are marked as `@public`, `@beta`, or `@alpha`.\n   *\n   * The path is resolved relative to the folder of the config file that contains the setting; to change this,\n   * prepend a folder token such as `<projectFolder>`.\n   */\n  alphaTrimmedFilePath?: string;\n\n  /**\n   * Specifies the output path for a .d.ts rollup file to be generated with trimming for a \"beta\" release.\n   *\n   * @remarks\n   * This file will include only declarations that are marked as `@public` or `@beta`.\n   *\n   * The path is resolved relative to the folder of the config file that contains the setting; to change this,\n   * prepend a folder token such as `<projectFolder>`.\n   */\n  betaTrimmedFilePath?: string;\n\n  /**\n   * Specifies the output path for a .d.ts rollup file to be generated with trimming for a \"public\" release.\n   *\n   * @remarks\n   * This file will include only declarations that are marked as `@public`.\n   *\n   * If the path is an empty string, then this file will not be written.\n   *\n   * The path is resolved relative to the folder of the config file that contains the setting; to change this,\n   * prepend a folder token such as `<projectFolder>`.\n   */\n  publicTrimmedFilePath?: string;\n\n  /**\n   * When a declaration is trimmed, by default it will be replaced by a code comment such as\n   * \"Excluded from this release type: exampleMember\".  Set \"omitTrimmingComments\" to true to remove the\n   * declaration completely.\n   */\n  omitTrimmingComments?: boolean;\n}\n\n/**\n * Configures how the tsdoc-metadata.json file will be generated.\n *\n * @remarks\n * This is part of the {@link IConfigFile} structure.\n *\n * @public\n */\nexport interface IConfigTsdocMetadata {\n  /**\n   * Whether to generate the tsdoc-metadata.json file.\n   */\n  enabled: boolean;\n\n  /**\n   * Specifies where the TSDoc metadata file should be written.\n   *\n   * @remarks\n   * The path is resolved relative to the folder of the config file that contains the setting; to change this,\n   * prepend a folder token such as `<projectFolder>`.\n   *\n   * The default value is `<lookup>`, which causes the path to be automatically inferred from the `tsdocMetadata`,\n   * `typings` or `main` fields of the project's package.json.  If none of these fields are set, the lookup\n   * falls back to `tsdoc-metadata.json` in the package folder.\n   */\n  tsdocMetadataFilePath?: string;\n}\n\n/**\n * Configures reporting for a given message identifier.\n *\n * @remarks\n * This is part of the {@link IConfigFile} structure.\n *\n * @public\n */\nexport interface IConfigMessageReportingRule {\n  /**\n   * Specifies whether the message should be written to the the tool's output log.\n   *\n   * @remarks\n   * Note that the `addToApiReportFile` property may supersede this option.\n   */\n  logLevel: ExtractorLogLevel;\n\n  /**\n   * When `addToApiReportFile` is true:  If API Extractor is configured to write an API report file (.api.md),\n   * then the message will be written inside that file; otherwise, the message is instead logged according to\n   * the `logLevel` option.\n   */\n  addToApiReportFile?: boolean;\n}\n\n/**\n * Specifies a table of reporting rules for different message identifiers, and also the default rule used for\n * identifiers that do not appear in the table.\n *\n * @remarks\n * This is part of the {@link IConfigFile} structure.\n *\n * @public\n */\nexport interface IConfigMessageReportingTable {\n  /**\n   * The key is a message identifier for the associated type of message, or \"default\" to specify the default policy.\n   * For example, the key might be `TS2551` (a compiler message), `tsdoc-link-tag-unescaped-text` (a TSDOc message),\n   * or `ae-extra-release-tag` (a message related to the API Extractor analysis).\n   */\n  [messageId: string]: IConfigMessageReportingRule;\n}\n\n/**\n * Configures how API Extractor reports error and warning messages produced during analysis.\n *\n * @remarks\n * This is part of the {@link IConfigFile} structure.\n *\n * @public\n */\nexport interface IExtractorMessagesConfig {\n  /**\n   * Configures handling of diagnostic messages generating the TypeScript compiler while analyzing the\n   * input .d.ts files.\n   */\n  compilerMessageReporting?: IConfigMessageReportingTable;\n\n  /**\n   * Configures handling of messages reported by API Extractor during its analysis.\n   */\n  extractorMessageReporting?: IConfigMessageReportingTable;\n\n  /**\n   * Configures handling of messages reported by the TSDoc parser when analyzing code comments.\n   */\n  tsdocMessageReporting?: IConfigMessageReportingTable;\n}\n\n/**\n * Configuration options for the API Extractor tool.  These options can be constructed programmatically\n * or loaded from the api-extractor.json config file using the {@link ExtractorConfig} class.\n *\n * @public\n */\nexport interface IConfigFile {\n  /**\n   * Optionally specifies another JSON config file that this file extends from.  This provides a way for\n   * standard settings to be shared across multiple projects.\n   *\n   * @remarks\n   * If the path starts with `./` or `../`, the path is resolved relative to the folder of the file that contains\n   * the `extends` field.  Otherwise, the first path segment is interpreted as an NPM package name, and will be\n   * resolved using NodeJS `require()`.\n   */\n  extends?: string;\n\n  /**\n   * Determines the `<projectFolder>` token that can be used with other config file settings.  The project folder\n   * typically contains the tsconfig.json and package.json config files, but the path is user-defined.\n   *\n   * @remarks\n   *\n   * The path is resolved relative to the folder of the config file that contains the setting.\n   *\n   * The default value for `projectFolder` is the token `<lookup>`, which means the folder is determined using\n   * the following heuristics:\n   *\n   * If the config/rig.json system is used (as defined by {@link https://www.npmjs.com/package/@rushstack/rig-package\n   * | @rushstack/rig-package}), then the `<lookup>` value will be the package folder that referenced the rig.\n   *\n   * Otherwise, the `<lookup>` value is determined by traversing parent folders, starting from the folder containing\n   * api-extractor.json, and stopping at the first folder that contains a tsconfig.json file.  If a tsconfig.json file\n   * cannot be found in this way, then an error will be reported.\n   */\n  projectFolder?: string;\n\n  /**\n   * Specifies the .d.ts file to be used as the starting point for analysis.  API Extractor\n   * analyzes the symbols exported by this module.\n   *\n   * @remarks\n   *\n   * The file extension must be \".d.ts\" and not \".ts\".\n   * The path is resolved relative to the \"projectFolder\" location.\n   */\n  mainEntryPointFilePath: string;\n\n  /**\n   * A list of NPM package names whose exports should be treated as part of this package.\n   *\n   * @remarks\n   * Also supports glob patterns.\n   * Note: glob patterns will **only** be resolved against dependencies listed in the project's package.json file.\n   *\n   * * This is both a safety and a performance precaution.\n   *\n   * Exact package names will be applied against any dependency encountered while walking the type graph, regardless of\n   * dependencies listed in the package.json.\n   *\n   * @example\n   *\n   * Suppose that Webpack is used to generate a distributed bundle for the project `library1`,\n   * and another NPM package `library2` is embedded in this bundle.  Some types from `library2` may become part\n   * of the exported API for `library1`, but by default API Extractor would generate a .d.ts rollup that explicitly\n   * imports `library2`.  To avoid this, we can specify:\n   *\n   * ```js\n   *   \"bundledPackages\": [ \"library2\" ],\n   * ```\n   *\n   * This would direct API Extractor to embed those types directly in the .d.ts rollup, as if they had been\n   * local files for `library1`.\n   */\n  bundledPackages?: string[];\n\n  /**\n   * Specifies what type of newlines API Extractor should use when writing output files.\n   *\n   * @remarks\n   * By default, the output files will be written with Windows-style newlines.\n   * To use POSIX-style newlines, specify \"lf\" instead.\n   * To use the OS's default newline kind, specify \"os\".\n   */\n  newlineKind?: 'crlf' | 'lf' | 'os';\n\n  /**\n   * Set to true when invoking API Extractor's test harness.\n   * @remarks\n   * When `testMode` is true, the `toolVersion` field in the .api.json file is assigned an empty string\n   * to prevent spurious diffs in output files tracked for tests.\n   */\n  testMode?: boolean;\n\n  /**\n   * Specifies how API Extractor sorts members of an enum when generating the .api.json file.\n   *\n   * @remarks\n   * By default, the output files will be sorted alphabetically, which is \"by-name\".\n   * To keep the ordering in the source code, specify \"preserve\".\n   *\n   * @defaultValue `by-name`\n   */\n  enumMemberOrder?: EnumMemberOrder;\n\n  /**\n   * {@inheritDoc IConfigCompiler}\n   */\n  compiler?: IConfigCompiler;\n\n  /**\n   * {@inheritDoc IConfigApiReport}\n   */\n  apiReport?: IConfigApiReport;\n\n  /**\n   * {@inheritDoc IConfigDocModel}\n   */\n  docModel?: IConfigDocModel;\n\n  /**\n   * {@inheritDoc IConfigDtsRollup}\n   * @beta\n   */\n  dtsRollup?: IConfigDtsRollup;\n\n  /**\n   * {@inheritDoc IConfigTsdocMetadata}\n   * @beta\n   */\n  tsdocMetadata?: IConfigTsdocMetadata;\n\n  /**\n   * {@inheritDoc IExtractorMessagesConfig}\n   */\n  messages?: IExtractorMessagesConfig;\n}\n"]}
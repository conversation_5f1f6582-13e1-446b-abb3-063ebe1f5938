{"version": 3, "file": "ExtractorLogLevel.js", "sourceRoot": "", "sources": ["../../src/api/ExtractorLogLevel.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;;;;;;GAOG;AACH,IAAY,iBAqCX;AArCD,WAAY,iBAAiB;IAC3B;;;;;OAKG;IACH,oCAAe,CAAA;IAEf;;;;;;OAMG;IACH,wCAAmB,CAAA;IAEnB;;;;;;OAMG;IACH,kCAAa,CAAA;IAEb;;;OAGG;IACH,wCAAmB,CAAA;IAEnB;;OAEG;IACH,kCAAa,CAAA;AACf,CAAC,EArCW,iBAAiB,iCAAjB,iBAAiB,QAqC5B", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\n/**\n * Used with {@link IConfigMessageReportingRule.logLevel} and {@link IExtractorInvokeOptions.messageCallback}.\n *\n * @remarks\n * This is part of the {@link IConfigFile} structure.\n *\n * @public\n */\nexport enum ExtractorLogLevel {\n  /**\n   * The message will be displayed as an error.\n   *\n   * @remarks\n   * Errors typically cause the build to fail and return a nonzero exit code.\n   */\n  Error = 'error',\n\n  /**\n   * The message will be displayed as an warning.\n   *\n   * @remarks\n   * Warnings typically cause a production build fail and return a nonzero exit code.  For a non-production build\n   * (e.g. using the `--local` option with `api-extractor run`), the warning is displayed but the build will not fail.\n   */\n  Warning = 'warning',\n\n  /**\n   * The message will be displayed as an informational message.\n   *\n   * @remarks\n   * Informational messages may contain newlines to ensure nice formatting of the output,\n   * however word-wrapping is the responsibility of the message handler.\n   */\n  Info = 'info',\n\n  /**\n   * The message will be displayed only when \"verbose\" output is requested, e.g. using the `--verbose`\n   * command line option.\n   */\n  Verbose = 'verbose',\n\n  /**\n   * The message will be discarded entirely.\n   */\n  None = 'none'\n}\n"]}
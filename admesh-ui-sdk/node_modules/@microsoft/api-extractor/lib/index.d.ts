/**
 * API Extractor helps with validation, documentation, and reviewing of the exported API for a TypeScript library.
 * The `@microsoft/api-extractor` package provides the command-line tool.  It also exposes a developer API that you
 * can use to invoke API Extractor programmatically.
 *
 * @packageDocumentation
 */
export { ConsoleMessageId } from './api/ConsoleMessageId';
export { CompilerState, type ICompilerStateCreateOptions } from './api/CompilerState';
export { Extractor, type IExtractorInvokeOptions, ExtractorResult } from './api/Extractor';
export { type IExtractorConfigApiReport, type IExtractorConfigPrepareOptions, type IExtractorConfigLoadForFolderOptions, ExtractorConfig } from './api/ExtractorConfig';
export type { IApiModelGenerationOptions } from './generators/ApiModelGenerator';
export { ExtractorLogLevel } from './api/ExtractorLogLevel';
export { ExtractorMessage, type IExtractorMessageProperties, ExtractorMessageCategory } from './api/ExtractorMessage';
export { ExtractorMessageId } from './api/ExtractorMessageId';
export type { ApiReportVariant, IConfigCompiler, IConfigApiReport, IConfigDocModel, IConfigDtsRollup, IConfigTsdocMetadata, IConfigMessageReportingRule, IConfigMessageReportingTable, IExtractorMessagesConfig, IConfigFile, ReleaseTagForTrim } from './api/IConfigFile';
//# sourceMappingURL=index.d.ts.map
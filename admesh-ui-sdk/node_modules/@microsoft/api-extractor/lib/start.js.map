{"version": 3, "file": "start.js", "sourceRoot": "", "sources": ["../src/start.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,uCAAyB;AACzB,kDAA+C;AAE/C,2EAAwE;AACxE,+CAA4C;AAE5C,OAAO,CAAC,GAAG,CACT,EAAE,CAAC,GAAG;IACJ,mBAAQ,CAAC,IAAI,CACX,iBAAiB,qBAAS,CAAC,OAAO,GAAG,GAAG,mBAAQ,CAAC,IAAI,CAAC,+BAA+B,CAAC,GAAG,EAAE,CAAC,GAAG,CAChG,CACJ,CAAC;AAEF,MAAM,MAAM,GAA4B,IAAI,iDAAuB,EAAE,CAAC;AAEtE,MAAM,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IACpC,OAAO,CAAC,KAAK,CAAC,mBAAQ,CAAC,GAAG,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC,CAAC;IACtE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\nimport * as os from 'os';\nimport { Colorize } from '@rushstack/terminal';\n\nimport { ApiExtractorCommandLine } from './cli/ApiExtractorCommandLine';\nimport { Extractor } from './api/Extractor';\n\nconsole.log(\n  os.EOL +\n    Colorize.bold(\n      `api-extractor ${Extractor.version} ` + Colorize.cyan(' - https://api-extractor.com/') + os.EOL\n    )\n);\n\nconst parser: ApiExtractorCommandLine = new ApiExtractorCommandLine();\n\nparser.executeAsync().catch((error) => {\n  console.error(Colorize.red(`An unexpected error occurred: ${error}`));\n  process.exit(1);\n});\n"]}
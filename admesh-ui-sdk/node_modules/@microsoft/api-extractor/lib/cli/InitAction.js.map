{"version": 3, "file": "InitAction.js", "sourceRoot": "", "sources": ["../../src/cli/InitAction.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,2CAA6B;AAC7B,oEAA0D;AAC1D,gEAA+D;AAC/D,kDAA+C;AAG/C,4DAAyD;AAEzD,MAAa,UAAW,SAAQ,mCAAiB;IAC/C,YAAmB,MAA+B;QAChD,KAAK,CAAC;YACJ,UAAU,EAAE,MAAM;YAClB,OAAO,EAAE,aAAa,iCAAe,CAAC,QAAQ,cAAc;YAC5D,aAAa,EACX,iFAAiF;gBACjF,IAAI,iCAAe,CAAC,QAAQ,0EAA0E;gBACtG,qDAAqD;SACxD,CAAC,CAAC;IACL,CAAC;IAEkB,KAAK,CAAC,cAAc;QACrC,MAAM,aAAa,GAAW,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,wCAAwC,CAAC,CAAC;QAChG,MAAM,cAAc,GAAW,IAAI,CAAC,OAAO,CAAC,iCAAe,CAAC,QAAQ,CAAC,CAAC;QAEtE,IAAI,8BAAU,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,mBAAQ,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,cAAc,GAAG,IAAI,CAAC,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,mBAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,cAAc,CAAC,CAAC;QAC/D,8BAAU,CAAC,QAAQ,CAAC;YAClB,UAAU,EAAE,aAAa;YACzB,eAAe,EAAE,cAAc;SAChC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CACT,qFAAqF;YACnF,oDAAoD,CACvD,CAAC;IACJ,CAAC;CACF;AAjCD,gCAiCC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as path from 'path';\nimport { FileSystem } from '@rushstack/node-core-library';\nimport { CommandLineAction } from '@rushstack/ts-command-line';\nimport { Colorize } from '@rushstack/terminal';\n\nimport type { ApiExtractorCommandLine } from './ApiExtractorCommandLine';\nimport { ExtractorConfig } from '../api/ExtractorConfig';\n\nexport class InitAction extends CommandLineAction {\n  public constructor(parser: ApiExtractorCommandLine) {\n    super({\n      actionName: 'init',\n      summary: `Create an ${ExtractorConfig.FILENAME} config file`,\n      documentation:\n        `Use this command when setting up API Extractor for a new project.  It writes an` +\n        ` ${ExtractorConfig.FILENAME} config file template with code comments that describe all the settings.` +\n        ` The file will be written in the current directory.`\n    });\n  }\n\n  protected override async onExecuteAsync(): Promise<void> {\n    const inputFilePath: string = path.resolve(__dirname, '../schemas/api-extractor-template.json');\n    const outputFilePath: string = path.resolve(ExtractorConfig.FILENAME);\n\n    if (FileSystem.exists(outputFilePath)) {\n      console.log(Colorize.red('The output file already exists:'));\n      console.log('\\n  ' + outputFilePath + '\\n');\n      throw new Error('Unable to write output file');\n    }\n\n    console.log(Colorize.green('Writing file: ') + outputFilePath);\n    FileSystem.copyFile({\n      sourcePath: inputFilePath,\n      destinationPath: outputFilePath\n    });\n\n    console.log(\n      '\\nThe recommended location for this file is in the project\\'s \"config\" subfolder,\\n' +\n        'or else in the top-level folder with package.json.'\n    );\n  }\n}\n"]}
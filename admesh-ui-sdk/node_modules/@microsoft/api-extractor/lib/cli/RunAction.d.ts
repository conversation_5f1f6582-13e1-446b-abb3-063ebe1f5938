import { CommandLineAction } from '@rushstack/ts-command-line';
import type { ApiExtractorCommandLine } from './ApiExtractorCommandLine';
export declare class RunAction extends CommandLineAction {
    private readonly _configFileParameter;
    private readonly _localParameter;
    private readonly _verboseParameter;
    private readonly _diagnosticsParameter;
    private readonly _typescriptCompilerFolder;
    constructor(parser: ApiExtractorCommandLine);
    protected onExecuteAsync(): Promise<void>;
}
//# sourceMappingURL=RunAction.d.ts.map
{"version": 3, "file": "DtsRollupGenerator.js", "sourceRoot": "", "sources": ["../../src/generators/DtsRollupGenerator.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+BAA+B;AAE/B,+CAAiC;AACjC,oEAA2F;AAC3F,wEAA4D;AAG5D,qEAAkE;AAClE,2CAAsF;AACtF,qDAAkD;AAElD,+DAA4D;AAE5D,qDAAkD;AAElD,qDAAkD;AAClD,qDAAkD;AAElD,uEAAoE;AAEpE,yFAAsF;AAGtF;;GAEG;AACH,IAAY,aA2BX;AA3BD,WAAY,aAAa;IACvB;;;OAGG;IACH,uEAAe,CAAA;IAEf;;;;OAIG;IACH,iEAAY,CAAA;IAEZ;;;;OAIG;IACH,+DAAW,CAAA;IAEX;;;;OAIG;IACH,mEAAa,CAAA;AACf,CAAC,EA3BW,aAAa,6BAAb,aAAa,QA2BxB;AAED,MAAa,kBAAkB;IAC7B;;;;OAIG;IACI,MAAM,CAAC,gBAAgB,CAC5B,SAAoB,EACpB,WAAmB,EACnB,OAAsB,EACtB,WAAwB;QAExB,MAAM,MAAM,GAAmB,IAAI,+BAAc,EAAE,CAAC;QACpD,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAEhC,kBAAkB,CAAC,2BAA2B,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAE3E,8BAAU,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE;YACnD,kBAAkB,EAAE,WAAW;YAC/B,kBAAkB,EAAE,IAAI;SACzB,CAAC,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,2BAA2B,CACxC,SAAoB,EACpB,MAAsB,EACtB,OAAsB;QAEtB,gEAAgE;QAChE,IAAI,SAAS,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAChD,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,kBAAkB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACrF,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAChC,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,mCAAmC;QACnC,KAAK,MAAM,sBAAsB,IAAI,SAAS,CAAC,0BAA0B,EAAE,CAAC;YAC1E,gIAAgI;YAChI,MAAM,CAAC,SAAS,CAAC,yBAAyB,sBAAsB,MAAM,CAAC,CAAC;QAC1E,CAAC;QACD,KAAK,MAAM,qBAAqB,IAAI,SAAS,CAAC,yBAAyB,EAAE,CAAC;YACxE,MAAM,CAAC,SAAS,CAAC,uBAAuB,qBAAqB,MAAM,CAAC,CAAC;QACvE,CAAC;QACD,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAE3B,mBAAmB;QACnB,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxC,IAAI,MAAM,CAAC,SAAS,YAAY,qBAAS,EAAE,CAAC;gBAC1C,oEAAoE;gBACpE,yFAAyF;gBACzF,wGAAwG;gBACxG,oEAAoE;gBACpE,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;gBAC9C,+BAAc,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QACD,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAE3B,gCAAgC;QAChC,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxC,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;YAC9C,MAAM,cAAc,GAA+B,SAAS,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC;YACrG,MAAM,sBAAsB,GAAe,cAAc;gBACvD,CAAC,CAAC,cAAc,CAAC,sBAAsB;gBACvC,CAAC,CAAC,gCAAU,CAAC,IAAI,CAAC;YAEpB,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,EAAE,OAAO,CAAC,EAAE,CAAC;gBACpE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC;oBACpD,MAAM,CAAC,iBAAiB,EAAE,CAAC;oBAC3B,MAAM,CAAC,SAAS,CAAC,uCAAuC,MAAM,CAAC,WAAW,KAAK,CAAC,CAAC;gBACnF,CAAC;gBACD,SAAS;YACX,CAAC;YAED,IAAI,SAAS,YAAY,qBAAS,EAAE,CAAC;gBACnC,2CAA2C;gBAC3C,KAAK,MAAM,cAAc,IAAI,SAAS,CAAC,eAAe,IAAI,EAAE,EAAE,CAAC;oBAC7D,MAAM,eAAe,GAAoB,SAAS,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;oBAExF,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,mBAAmB,EAAE,OAAO,CAAC,EAAE,CAAC;wBACjF,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC;4BACpD,MAAM,CAAC,iBAAiB,EAAE,CAAC;4BAC3B,MAAM,CAAC,SAAS,CAAC,mDAAmD,MAAM,CAAC,WAAW,KAAK,CAAC,CAAC;wBAC/F,CAAC;wBACD,SAAS;oBACX,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,GAAS,IAAI,WAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;wBACxD,kBAAkB,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;wBACjF,MAAM,CAAC,iBAAiB,EAAE,CAAC;wBAC3B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;wBAC/B,MAAM,CAAC,aAAa,EAAE,CAAC;oBACzB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,SAAS,YAAY,uCAAkB,EAAE,CAAC;gBAC5C,MAAM,mBAAmB,GAAyB,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;gBAEhG,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;oBACrC,2BAA2B;oBAC3B,MAAM,IAAI,iCAAa,CAAC,0CAA0C,CAAC,CAAC;gBACtE,CAAC;gBAED,IAAI,mBAAmB,CAAC,2BAA2B,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;oBAC7D,iFAAiF;oBACjF,MAAM,IAAI,KAAK,CACb,OAAO,MAAM,CAAC,WAAW,sEAAsE;wBAC7F,yDAA2B,CAAC,iBAAiB,CAAC,SAAS,CAAC,WAAW,CAAC,CACvE,CAAC;gBACJ,CAAC;gBAED,2EAA2E;gBAC3E,EAAE;gBACF,iCAAiC;gBACjC,gBAAgB;gBAChB,aAAa;gBACb,YAAY;gBACZ,SAAS;gBACT,OAAO;gBACP,EAAE;gBACF,8FAA8F;gBAC9F,kFAAkF;gBAElF,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBAC9B,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBAC1B,CAAC;gBACD,MAAM,CAAC,SAAS,CAAC,qBAAqB,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC;gBAE9D,2FAA2F;gBAC3F,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBAC7B,MAAM,CAAC,cAAc,EAAE,CAAC;gBAExB,MAAM,aAAa,GAAa,EAAE,CAAC;gBACnC,KAAK,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,IAAI,mBAAmB,CAAC,qBAAqB,EAAE,CAAC;oBACvF,MAAM,eAAe,GACnB,SAAS,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;oBAClD,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;wBAClC,2BAA2B;wBAC3B,0FAA0F;wBAC1F,MAAM,IAAI,iCAAa,CACrB,oCAAoC,MAAM,CAAC,WAAW,IAAI,cAAc,CAAC,SAAS,EAAE,CACrF,CAAC;oBACJ,CAAC;oBAED,6FAA6F;oBAC7F,4FAA4F;oBAC5F,MAAM,sBAAsB,GAC1B,SAAS,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAAC;oBACzD,MAAM,8BAA8B,GAAe,sBAAsB;wBACvE,CAAC,CAAC,sBAAsB,CAAC,sBAAsB;wBAC/C,CAAC,CAAC,gCAAU,CAAC,IAAI,CAAC;oBACpB,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,8BAA8B,EAAE,OAAO,CAAC,EAAE,CAAC;wBAC5E,SAAS;oBACX,CAAC;oBAED,IAAI,eAAe,CAAC,WAAW,KAAK,YAAY,EAAE,CAAC;wBACjD,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;oBAClD,CAAC;yBAAM,CAAC;wBACN,aAAa,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,WAAW,OAAO,YAAY,EAAE,CAAC,CAAC;oBAC1E,CAAC;gBACH,CAAC;gBACD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBAE5C,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,0BAA0B;gBACjD,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,qCAAqC;YAC9D,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;oBAC5C,+BAAc,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;gBAC7D,CAAC;YACH,CAAC;YAED,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,+BAAc,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAElD,qGAAqG;QACrG,iDAAiD;QACjD,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAC3B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,WAAW,CACxB,SAAoB,EACpB,IAAU,EACV,MAAuB,EACvB,cAA8B,EAC9B,OAAsB;QAEtB,MAAM,YAAY,GAAqB,IAAI,CAAC,eAAe,CAAC;QAE5D,IAAI,eAAe,GAAY,IAAI,CAAC;QACpC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY;gBAC7B,6FAA6F;gBAC7F,8DAA8D;gBAC9D,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,2CAA2C,CAAC,EAAE,CAAC;oBAC3E,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC9B,CAAC;gBAED,yDAAyD;gBACzD,eAAe,GAAG,KAAK,CAAC;gBACxB,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;YACjC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC;YAClC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;gBAC/B,kFAAkF;gBAClF,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;YACpC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;YAChC,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;YACpC,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;YACjC,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe;gBAChC,8CAA8C;gBAC9C,IAAI,iBAAiB,GAAW,EAAE,CAAC;gBAEnC,kFAAkF;gBAClF,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;oBAC3B,iBAAiB,IAAI,UAAU,CAAC;gBAClC,CAAC;gBAED,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBAC9B,iBAAiB,GAAG,SAAS,GAAG,iBAAiB,CAAC;gBACpD,CAAC;gBAED,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;oBACnE,2FAA2F;oBAC3F,uCAAuC;oBACvC,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,iBAAiB,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC1F,CAAC;qBAAM,CAAC;oBACN,gDAAgD;oBAChD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC1E,CAAC;gBACD,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;gBACpC,4CAA4C;gBAC5C,2GAA2G;gBAC3G,iGAAiG;gBACjG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACjB,6EAA6E;oBAC7E,0EAA0E;oBAC1E,qEAAqE;oBACrE,EAAE;oBACF,qFAAqF;oBACrF,gFAAgF;oBAChF,4CAA4C;oBAC5C,MAAM,IAAI,GAA2C,qCAAiB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE;wBAC9F,EAAE,CAAC,UAAU,CAAC,uBAAuB;wBACrC,EAAE,CAAC,UAAU,CAAC,mBAAmB;qBAClC,CAAC,CAAC;oBACH,IAAI,CAAC,IAAI,EAAE,CAAC;wBACV,iEAAiE;wBACjE,MAAM,IAAI,iCAAa,CAAC,kCAAkC,CAAC,CAAC;oBAC9D,CAAC;oBACD,MAAM,UAAU,GAAW,IAAI;yBAC5B,aAAa,EAAE;yBACf,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACpE,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC9E,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,CAAC;oBAE/B,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;wBAC9B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;oBAClE,CAAC;oBAED,MAAM,mBAAmB,GAAwB,SAAS,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;oBACpG,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;wBAC3C,+FAA+F;wBAC/F,6FAA6F;wBAC7F,yCAAyC;wBACzC,IAAI,eAAe,GAAW,mBAAmB,CAAC,kBAAkB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;wBAC5F,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;4BACvC,eAAe,IAAI,IAAI,CAAC;wBAC1B,CAAC;wBACD,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,4BAAqB,CAAC,UAAU,CAAC;wBACtE,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;oBACxE,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,CAAC;oBACC,MAAM,gBAAgB,GAAgC,SAAS,CAAC,mBAAmB,CACjF,IAAI,CAAC,IAAqB,CAC3B,CAAC;oBAEF,IAAI,gBAAgB,EAAE,CAAC;wBACrB,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;4BAClC,2BAA2B;4BAC3B,MAAM,IAAI,iCAAa,CAAC,0CAA0C,CAAC,CAAC;wBACtE,CAAC;wBAED,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,gBAAgB,CAAC,WAAW,CAAC;wBACxD,iBAAiB;wBACjB,2CAA2C;oBAC7C,CAAC;yBAAM,CAAC;wBACN,iBAAiB;wBACjB,4CAA4C;oBAC9C,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,+BAAc,CAAC,oBAAoB,CACjC,SAAS,EACT,IAAI,EACJ,cAAc,EACd,CAAC,SAAS,EAAE,mBAAmB,EAAE,EAAE;oBACjC,kBAAkB,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBAC7F,CAAC,CACF,CAAC;gBACF,MAAM;QACV,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACpB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,IAAI,mBAAmB,GAAmB,cAAc,CAAC;gBAEzD,4BAA4B;gBAC5B,IAAI,OAAO,GAAY,KAAK,CAAC;gBAC7B,IAAI,+BAAc,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrD,mBAAmB,GAAG,SAAS,CAAC,cAAc,CAAC,4BAA4B,CACzE,KAAK,CAAC,IAAI,EACV,cAAc,CACf,CAAC;oBACF,MAAM,UAAU,GACd,SAAS,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC,mBAAmB,CAAC;oBAE1E,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC;wBACxD,IAAI,UAAU,GAAS,KAAK,CAAC;wBAE7B,kGAAkG;wBAClG,WAAW;wBACX,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC;4BACrD,MAAM,iBAAiB,GAAqB,KAAK,CAAC,eAAe,CAC/D,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAChC,CAAC;4BACF,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;gCACpC,UAAU,GAAG,iBAAiB,CAAC;4BACjC,CAAC;wBACH,CAAC;wBAED,MAAM,YAAY,GAAqB,UAAU,CAAC,YAAY,CAAC;wBAE/D,6BAA6B;wBAC7B,MAAM,IAAI,GAAW,mBAAmB,CAAC,SAAS,CAAC,SAAS,CAAC;wBAC7D,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC;wBAEjC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC;4BACpD,YAAY,CAAC,MAAM,GAAG,uCAAuC,IAAI,KAAK,CAAC;wBACzE,CAAC;6BAAM,CAAC;4BACN,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC;wBAC3B,CAAC;wBACD,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC;wBAEzB,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACnC,yEAAyE;4BACzE,uCAAuC;4BACvC,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;wBACtF,CAAC;wBAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;4BAC3B,iFAAiF;4BACjF,sCAAsC;4BACtC,IAAI,UAAU,CAAC,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gCAC7D,0DAA0D;gCAC1D,YAAY,CAAC,MAAM,IAAI,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC;gCACxD,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;4BAChD,CAAC;wBACH,CAAC;wBAED,OAAO,GAAG,IAAI,CAAC;oBACjB,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,kBAAkB,CAAC,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBACzF,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,UAAsB,EAAE,OAAsB;QACpF,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,aAAa,CAAC,eAAe;gBAChC,OAAO,IAAI,CAAC;YACd,KAAK,aAAa,CAAC,YAAY;gBAC7B,OAAO,CACL,UAAU,KAAK,gCAAU,CAAC,KAAK;oBAC/B,UAAU,KAAK,gCAAU,CAAC,IAAI;oBAC9B,UAAU,KAAK,gCAAU,CAAC,MAAM;oBAChC,uFAAuF;oBACvF,UAAU,KAAK,gCAAU,CAAC,IAAI,CAC/B,CAAC;YACJ,KAAK,aAAa,CAAC,WAAW;gBAC5B,OAAO,CACL,UAAU,KAAK,gCAAU,CAAC,IAAI;oBAC9B,UAAU,KAAK,gCAAU,CAAC,MAAM;oBAChC,uFAAuF;oBACvF,UAAU,KAAK,gCAAU,CAAC,IAAI,CAC/B,CAAC;YACJ,KAAK,aAAa,CAAC,aAAa;gBAC9B,OAAO,UAAU,KAAK,gCAAU,CAAC,MAAM,IAAI,UAAU,KAAK,gCAAU,CAAC,IAAI,CAAC;YAC5E;gBACE,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;CACF;AAtaD,gDAsaC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\n/* eslint-disable no-bitwise */\n\nimport * as ts from 'typescript';\nimport { FileSystem, type NewlineKind, InternalError } from '@rushstack/node-core-library';\nimport { ReleaseTag } from '@microsoft/api-extractor-model';\n\nimport type { Collector } from '../collector/Collector';\nimport { TypeScriptHelpers } from '../analyzer/TypeScriptHelpers';\nimport { IndentDocCommentScope, Span, type SpanModification } from '../analyzer/Span';\nimport { AstImport } from '../analyzer/AstImport';\nimport type { CollectorEntity } from '../collector/CollectorEntity';\nimport { AstDeclaration } from '../analyzer/AstDeclaration';\nimport type { ApiItemMetadata } from '../collector/ApiItemMetadata';\nimport { AstSymbol } from '../analyzer/AstSymbol';\nimport type { SymbolMetadata } from '../collector/SymbolMetadata';\nimport { IndentedWriter } from './IndentedWriter';\nimport { DtsEmitHelpers } from './DtsEmitHelpers';\nimport type { DeclarationMetadata } from '../collector/DeclarationMetadata';\nimport { AstNamespaceImport } from '../analyzer/AstNamespaceImport';\nimport type { IAstModuleExportInfo } from '../analyzer/AstModule';\nimport { SourceFileLocationFormatter } from '../analyzer/SourceFileLocationFormatter';\nimport type { AstEntity } from '../analyzer/AstEntity';\n\n/**\n * Used with DtsRollupGenerator.writeTypingsFile()\n */\nexport enum DtsRollupKind {\n  /**\n   * Generate a *.d.ts file for an internal release, or for the trimming=false mode.\n   * This output file will contain all definitions that are reachable from the entry point.\n   */\n  InternalRelease,\n\n  /**\n   * Generate a *.d.ts file for a preview release.\n   * This output file will contain all definitions that are reachable from the entry point,\n   * except definitions marked as \\@internal.\n   */\n  AlphaRelease,\n\n  /**\n   * Generate a *.d.ts file for a preview release.\n   * This output file will contain all definitions that are reachable from the entry point,\n   * except definitions marked as \\@alpha or \\@internal.\n   */\n  BetaRelease,\n\n  /**\n   * Generate a *.d.ts file for a public release.\n   * This output file will contain all definitions that are reachable from the entry point,\n   * except definitions marked as \\@beta, \\@alpha, or \\@internal.\n   */\n  PublicRelease\n}\n\nexport class DtsRollupGenerator {\n  /**\n   * Generates the typings file and writes it to disk.\n   *\n   * @param dtsFilename    - The *.d.ts output filename\n   */\n  public static writeTypingsFile(\n    collector: Collector,\n    dtsFilename: string,\n    dtsKind: DtsRollupKind,\n    newlineKind: NewlineKind\n  ): void {\n    const writer: IndentedWriter = new IndentedWriter();\n    writer.trimLeadingSpaces = true;\n\n    DtsRollupGenerator._generateTypingsFileContent(collector, writer, dtsKind);\n\n    FileSystem.writeFile(dtsFilename, writer.toString(), {\n      convertLineEndings: newlineKind,\n      ensureFolderExists: true\n    });\n  }\n\n  private static _generateTypingsFileContent(\n    collector: Collector,\n    writer: IndentedWriter,\n    dtsKind: DtsRollupKind\n  ): void {\n    // Emit the @packageDocumentation comment at the top of the file\n    if (collector.workingPackage.tsdocParserContext) {\n      writer.trimLeadingSpaces = false;\n      writer.writeLine(collector.workingPackage.tsdocParserContext.sourceRange.toString());\n      writer.trimLeadingSpaces = true;\n      writer.ensureSkippedLine();\n    }\n\n    // Emit the triple slash directives\n    for (const typeDirectiveReference of collector.dtsTypeReferenceDirectives) {\n      // https://github.com/microsoft/TypeScript/blob/611ebc7aadd7a44a4c0447698bfda9222a78cb66/src/compiler/declarationEmitter.ts#L162\n      writer.writeLine(`/// <reference types=\"${typeDirectiveReference}\" />`);\n    }\n    for (const libDirectiveReference of collector.dtsLibReferenceDirectives) {\n      writer.writeLine(`/// <reference lib=\"${libDirectiveReference}\" />`);\n    }\n    writer.ensureSkippedLine();\n\n    // Emit the imports\n    for (const entity of collector.entities) {\n      if (entity.astEntity instanceof AstImport) {\n        // Note: it isn't valid to trim imports based on their release tags.\n        // E.g. class Foo (`@public`) extends interface Bar (`@beta`) from some external library.\n        // API-Extractor cannot trim `import { Bar } from \"external-library\"` when generating its public rollup,\n        // or the export of `Foo` would include a broken reference to `Bar`.\n        const astImport: AstImport = entity.astEntity;\n        DtsEmitHelpers.emitImport(writer, entity, astImport);\n      }\n    }\n    writer.ensureSkippedLine();\n\n    // Emit the regular declarations\n    for (const entity of collector.entities) {\n      const astEntity: AstEntity = entity.astEntity;\n      const symbolMetadata: SymbolMetadata | undefined = collector.tryFetchMetadataForAstEntity(astEntity);\n      const maxEffectiveReleaseTag: ReleaseTag = symbolMetadata\n        ? symbolMetadata.maxEffectiveReleaseTag\n        : ReleaseTag.None;\n\n      if (!this._shouldIncludeReleaseTag(maxEffectiveReleaseTag, dtsKind)) {\n        if (!collector.extractorConfig.omitTrimmingComments) {\n          writer.ensureSkippedLine();\n          writer.writeLine(`/* Excluded from this release type: ${entity.nameForEmit} */`);\n        }\n        continue;\n      }\n\n      if (astEntity instanceof AstSymbol) {\n        // Emit all the declarations for this entry\n        for (const astDeclaration of astEntity.astDeclarations || []) {\n          const apiItemMetadata: ApiItemMetadata = collector.fetchApiItemMetadata(astDeclaration);\n\n          if (!this._shouldIncludeReleaseTag(apiItemMetadata.effectiveReleaseTag, dtsKind)) {\n            if (!collector.extractorConfig.omitTrimmingComments) {\n              writer.ensureSkippedLine();\n              writer.writeLine(`/* Excluded declaration from this release type: ${entity.nameForEmit} */`);\n            }\n            continue;\n          } else {\n            const span: Span = new Span(astDeclaration.declaration);\n            DtsRollupGenerator._modifySpan(collector, span, entity, astDeclaration, dtsKind);\n            writer.ensureSkippedLine();\n            span.writeModifiedText(writer);\n            writer.ensureNewLine();\n          }\n        }\n      }\n\n      if (astEntity instanceof AstNamespaceImport) {\n        const astModuleExportInfo: IAstModuleExportInfo = astEntity.fetchAstModuleExportInfo(collector);\n\n        if (entity.nameForEmit === undefined) {\n          // This should never happen\n          throw new InternalError('referencedEntry.nameForEmit is undefined');\n        }\n\n        if (astModuleExportInfo.starExportedExternalModules.size > 0) {\n          // We could support this, but we would need to find a way to safely represent it.\n          throw new Error(\n            `The ${entity.nameForEmit} namespace import includes a start export, which is not supported:\\n` +\n              SourceFileLocationFormatter.formatDeclaration(astEntity.declaration)\n          );\n        }\n\n        // Emit a synthetic declaration for the namespace.  It will look like this:\n        //\n        //    declare namespace example {\n        //      export {\n        //        f1,\n        //        f2\n        //      }\n        //    }\n        //\n        // Note that we do not try to relocate f1()/f2() to be inside the namespace because other type\n        // signatures may reference them directly (without using the namespace qualifier).\n\n        writer.ensureSkippedLine();\n        if (entity.shouldInlineExport) {\n          writer.write('export ');\n        }\n        writer.writeLine(`declare namespace ${entity.nameForEmit} {`);\n\n        // all local exports of local imported module are just references to top-level declarations\n        writer.increaseIndent();\n        writer.writeLine('export {');\n        writer.increaseIndent();\n\n        const exportClauses: string[] = [];\n        for (const [exportedName, exportedEntity] of astModuleExportInfo.exportedLocalEntities) {\n          const collectorEntity: CollectorEntity | undefined =\n            collector.tryGetCollectorEntity(exportedEntity);\n          if (collectorEntity === undefined) {\n            // This should never happen\n            // top-level exports of local imported module should be added as collector entities before\n            throw new InternalError(\n              `Cannot find collector entity for ${entity.nameForEmit}.${exportedEntity.localName}`\n            );\n          }\n\n          // If the entity's declaration won't be included, then neither should the namespace export it\n          // This fixes the issue encountered here: https://github.com/microsoft/rushstack/issues/2791\n          const exportedSymbolMetadata: SymbolMetadata | undefined =\n            collector.tryFetchMetadataForAstEntity(exportedEntity);\n          const exportedMaxEffectiveReleaseTag: ReleaseTag = exportedSymbolMetadata\n            ? exportedSymbolMetadata.maxEffectiveReleaseTag\n            : ReleaseTag.None;\n          if (!this._shouldIncludeReleaseTag(exportedMaxEffectiveReleaseTag, dtsKind)) {\n            continue;\n          }\n\n          if (collectorEntity.nameForEmit === exportedName) {\n            exportClauses.push(collectorEntity.nameForEmit);\n          } else {\n            exportClauses.push(`${collectorEntity.nameForEmit} as ${exportedName}`);\n          }\n        }\n        writer.writeLine(exportClauses.join(',\\n'));\n\n        writer.decreaseIndent();\n        writer.writeLine('}'); // end of \"export { ... }\"\n        writer.decreaseIndent();\n        writer.writeLine('}'); // end of \"declare namespace { ... }\"\n      }\n\n      if (!entity.shouldInlineExport) {\n        for (const exportName of entity.exportNames) {\n          DtsEmitHelpers.emitNamedExport(writer, exportName, entity);\n        }\n      }\n\n      writer.ensureSkippedLine();\n    }\n\n    DtsEmitHelpers.emitStarExports(writer, collector);\n\n    // Emit \"export { }\" which is a special directive that prevents consumers from importing declarations\n    // that don't have an explicit \"export\" modifier.\n    writer.ensureSkippedLine();\n    writer.writeLine('export { }');\n  }\n\n  /**\n   * Before writing out a declaration, _modifySpan() applies various fixups to make it nice.\n   */\n  private static _modifySpan(\n    collector: Collector,\n    span: Span,\n    entity: CollectorEntity,\n    astDeclaration: AstDeclaration,\n    dtsKind: DtsRollupKind\n  ): void {\n    const previousSpan: Span | undefined = span.previousSibling;\n\n    let recurseChildren: boolean = true;\n    switch (span.kind) {\n      case ts.SyntaxKind.JSDocComment:\n        // If the @packageDocumentation comment seems to be attached to one of the regular API items,\n        // omit it.  It gets explictly emitted at the top of the file.\n        if (span.node.getText().match(/(?:\\s|\\*)@packageDocumentation(?:\\s|\\*)/gi)) {\n          span.modification.skipAll();\n        }\n\n        // For now, we don't transform JSDoc comment nodes at all\n        recurseChildren = false;\n        break;\n\n      case ts.SyntaxKind.ExportKeyword:\n      case ts.SyntaxKind.DefaultKeyword:\n      case ts.SyntaxKind.DeclareKeyword:\n        // Delete any explicit \"export\" or \"declare\" keywords -- we will re-add them below\n        span.modification.skipAll();\n        break;\n\n      case ts.SyntaxKind.InterfaceKeyword:\n      case ts.SyntaxKind.ClassKeyword:\n      case ts.SyntaxKind.EnumKeyword:\n      case ts.SyntaxKind.NamespaceKeyword:\n      case ts.SyntaxKind.ModuleKeyword:\n      case ts.SyntaxKind.TypeKeyword:\n      case ts.SyntaxKind.FunctionKeyword:\n        // Replace the stuff we possibly deleted above\n        let replacedModifiers: string = '';\n\n        // Add a declare statement for root declarations (but not for nested declarations)\n        if (!astDeclaration.parent) {\n          replacedModifiers += 'declare ';\n        }\n\n        if (entity.shouldInlineExport) {\n          replacedModifiers = 'export ' + replacedModifiers;\n        }\n\n        if (previousSpan && previousSpan.kind === ts.SyntaxKind.SyntaxList) {\n          // If there is a previous span of type SyntaxList, then apply it before any other modifiers\n          // (e.g. \"abstract\") that appear there.\n          previousSpan.modification.prefix = replacedModifiers + previousSpan.modification.prefix;\n        } else {\n          // Otherwise just stick it in front of this span\n          span.modification.prefix = replacedModifiers + span.modification.prefix;\n        }\n        break;\n\n      case ts.SyntaxKind.VariableDeclaration:\n        // Is this a top-level variable declaration?\n        // (The logic below does not apply to variable declarations that are part of an explicit \"namespace\" block,\n        // since the compiler prefers not to emit \"declare\" or \"export\" keywords for those declarations.)\n        if (!span.parent) {\n          // The VariableDeclaration node is part of a VariableDeclarationList, however\n          // the Entry.followedSymbol points to the VariableDeclaration part because\n          // multiple definitions might share the same VariableDeclarationList.\n          //\n          // Since we are emitting a separate declaration for each one, we need to look upwards\n          // in the ts.Node tree and write a copy of the enclosing VariableDeclarationList\n          // content (e.g. \"var\" from \"var x=1, y=2\").\n          const list: ts.VariableDeclarationList | undefined = TypeScriptHelpers.matchAncestor(span.node, [\n            ts.SyntaxKind.VariableDeclarationList,\n            ts.SyntaxKind.VariableDeclaration\n          ]);\n          if (!list) {\n            // This should not happen unless the compiler API changes somehow\n            throw new InternalError('Unsupported variable declaration');\n          }\n          const listPrefix: string = list\n            .getSourceFile()\n            .text.substring(list.getStart(), list.declarations[0].getStart());\n          span.modification.prefix = 'declare ' + listPrefix + span.modification.prefix;\n          span.modification.suffix = ';';\n\n          if (entity.shouldInlineExport) {\n            span.modification.prefix = 'export ' + span.modification.prefix;\n          }\n\n          const declarationMetadata: DeclarationMetadata = collector.fetchDeclarationMetadata(astDeclaration);\n          if (declarationMetadata.tsdocParserContext) {\n            // Typically the comment for a variable declaration is attached to the outer variable statement\n            // (which may possibly contain multiple variable declarations), so it's not part of the Span.\n            // Instead we need to manually inject it.\n            let originalComment: string = declarationMetadata.tsdocParserContext.sourceRange.toString();\n            if (!/\\r?\\n\\s*$/.test(originalComment)) {\n              originalComment += '\\n';\n            }\n            span.modification.indentDocComment = IndentDocCommentScope.PrefixOnly;\n            span.modification.prefix = originalComment + span.modification.prefix;\n          }\n        }\n        break;\n\n      case ts.SyntaxKind.Identifier:\n        {\n          const referencedEntity: CollectorEntity | undefined = collector.tryGetEntityForNode(\n            span.node as ts.Identifier\n          );\n\n          if (referencedEntity) {\n            if (!referencedEntity.nameForEmit) {\n              // This should never happen\n              throw new InternalError('referencedEntry.nameForEmit is undefined');\n            }\n\n            span.modification.prefix = referencedEntity.nameForEmit;\n            // For debugging:\n            // span.modification.prefix += '/*R=FIX*/';\n          } else {\n            // For debugging:\n            // span.modification.prefix += '/*R=KEEP*/';\n          }\n        }\n        break;\n\n      case ts.SyntaxKind.ImportType:\n        DtsEmitHelpers.modifyImportTypeSpan(\n          collector,\n          span,\n          astDeclaration,\n          (childSpan, childAstDeclaration) => {\n            DtsRollupGenerator._modifySpan(collector, childSpan, entity, childAstDeclaration, dtsKind);\n          }\n        );\n        break;\n    }\n\n    if (recurseChildren) {\n      for (const child of span.children) {\n        let childAstDeclaration: AstDeclaration = astDeclaration;\n\n        // Should we trim this node?\n        let trimmed: boolean = false;\n        if (AstDeclaration.isSupportedSyntaxKind(child.kind)) {\n          childAstDeclaration = collector.astSymbolTable.getChildAstDeclarationByNode(\n            child.node,\n            astDeclaration\n          );\n          const releaseTag: ReleaseTag =\n            collector.fetchApiItemMetadata(childAstDeclaration).effectiveReleaseTag;\n\n          if (!this._shouldIncludeReleaseTag(releaseTag, dtsKind)) {\n            let nodeToTrim: Span = child;\n\n            // If we are trimming a variable statement, then we need to trim the outer VariableDeclarationList\n            // as well.\n            if (child.kind === ts.SyntaxKind.VariableDeclaration) {\n              const variableStatement: Span | undefined = child.findFirstParent(\n                ts.SyntaxKind.VariableStatement\n              );\n              if (variableStatement !== undefined) {\n                nodeToTrim = variableStatement;\n              }\n            }\n\n            const modification: SpanModification = nodeToTrim.modification;\n\n            // Yes, trim it and stop here\n            const name: string = childAstDeclaration.astSymbol.localName;\n            modification.omitChildren = true;\n\n            if (!collector.extractorConfig.omitTrimmingComments) {\n              modification.prefix = `/* Excluded from this release type: ${name} */`;\n            } else {\n              modification.prefix = '';\n            }\n            modification.suffix = '';\n\n            if (nodeToTrim.children.length > 0) {\n              // If there are grandchildren, then keep the last grandchild's separator,\n              // since it often has useful whitespace\n              modification.suffix = nodeToTrim.children[nodeToTrim.children.length - 1].separator;\n            }\n\n            if (nodeToTrim.nextSibling) {\n              // If the thing we are trimming is followed by a comma, then trim the comma also.\n              // An example would be an enum member.\n              if (nodeToTrim.nextSibling.kind === ts.SyntaxKind.CommaToken) {\n                // Keep its separator since it often has useful whitespace\n                modification.suffix += nodeToTrim.nextSibling.separator;\n                nodeToTrim.nextSibling.modification.skipAll();\n              }\n            }\n\n            trimmed = true;\n          }\n        }\n\n        if (!trimmed) {\n          DtsRollupGenerator._modifySpan(collector, child, entity, childAstDeclaration, dtsKind);\n        }\n      }\n    }\n  }\n\n  private static _shouldIncludeReleaseTag(releaseTag: ReleaseTag, dtsKind: DtsRollupKind): boolean {\n    switch (dtsKind) {\n      case DtsRollupKind.InternalRelease:\n        return true;\n      case DtsRollupKind.AlphaRelease:\n        return (\n          releaseTag === ReleaseTag.Alpha ||\n          releaseTag === ReleaseTag.Beta ||\n          releaseTag === ReleaseTag.Public ||\n          // NOTE: If the release tag is \"None\", then we don't have enough information to trim it\n          releaseTag === ReleaseTag.None\n        );\n      case DtsRollupKind.BetaRelease:\n        return (\n          releaseTag === ReleaseTag.Beta ||\n          releaseTag === ReleaseTag.Public ||\n          // NOTE: If the release tag is \"None\", then we don't have enough information to trim it\n          releaseTag === ReleaseTag.None\n        );\n      case DtsRollupKind.PublicRelease:\n        return releaseTag === ReleaseTag.Public || releaseTag === ReleaseTag.None;\n      default:\n        throw new Error(`${DtsRollupKind[dtsKind]} is not implemented`);\n    }\n  }\n}\n"]}
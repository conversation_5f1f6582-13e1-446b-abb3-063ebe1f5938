{"version": 3, "file": "DtsEmitHelpers.js", "sourceRoot": "", "sources": ["../../src/generators/DtsEmitHelpers.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+CAAiC;AAEjC,oEAA6D;AAE7D,qDAAiE;AACjE,+DAA4D;AAI5D,yFAAsF;AAEtF;;GAEG;AACH,MAAa,cAAc;IAClB,MAAM,CAAC,UAAU,CACtB,MAAsB,EACtB,eAAgC,EAChC,SAAoB;QAEpB,MAAM,YAAY,GAAW,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC;QAEvF,QAAQ,SAAS,CAAC,UAAU,EAAE,CAAC;YAC7B,KAAK,yBAAa,CAAC,aAAa;gBAC9B,IAAI,eAAe,CAAC,WAAW,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;oBACzD,MAAM,CAAC,KAAK,CAAC,GAAG,YAAY,iBAAiB,eAAe,CAAC,WAAW,IAAI,CAAC,CAAC;gBAChF,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,KAAK,CAAC,GAAG,YAAY,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;gBAC1D,CAAC;gBACD,MAAM,CAAC,SAAS,CAAC,UAAU,SAAS,CAAC,UAAU,IAAI,CAAC,CAAC;gBACrD,MAAM;YACR,KAAK,yBAAa,CAAC,WAAW;gBAC5B,IAAI,eAAe,CAAC,WAAW,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;oBACzD,MAAM,CAAC,KAAK,CAAC,GAAG,YAAY,MAAM,SAAS,CAAC,UAAU,IAAI,CAAC,CAAC;gBAC9D,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,KAAK,CAAC,GAAG,YAAY,MAAM,SAAS,CAAC,UAAU,OAAO,eAAe,CAAC,WAAW,IAAI,CAAC,CAAC;gBAChG,CAAC;gBACD,MAAM,CAAC,SAAS,CAAC,UAAU,SAAS,CAAC,UAAU,IAAI,CAAC,CAAC;gBACrD,MAAM;YACR,KAAK,yBAAa,CAAC,UAAU;gBAC3B,MAAM,CAAC,SAAS,CACd,GAAG,YAAY,SAAS,eAAe,CAAC,WAAW,UAAU,SAAS,CAAC,UAAU,IAAI,CACtF,CAAC;gBACF,MAAM;YACR,KAAK,yBAAa,CAAC,YAAY;gBAC7B,MAAM,CAAC,SAAS,CACd,GAAG,YAAY,IAAI,eAAe,CAAC,WAAW,eAAe,SAAS,CAAC,UAAU,KAAK,CACvF,CAAC;gBACF,MAAM;YACR,KAAK,yBAAa,CAAC,UAAU;gBAC3B,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;oBAC1B,MAAM,CAAC,SAAS,CACd,GAAG,YAAY,SAAS,eAAe,CAAC,WAAW,UAAU,SAAS,CAAC,UAAU,IAAI,CACtF,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,MAAM,aAAa,GAAW,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjE,IAAI,eAAe,CAAC,WAAW,KAAK,aAAa,EAAE,CAAC;wBAClD,MAAM,CAAC,KAAK,CAAC,GAAG,YAAY,MAAM,aAAa,IAAI,CAAC,CAAC;oBACvD,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,KAAK,CAAC,GAAG,YAAY,MAAM,aAAa,OAAO,eAAe,CAAC,WAAW,IAAI,CAAC,CAAC;oBACzF,CAAC;oBACD,MAAM,CAAC,SAAS,CAAC,UAAU,SAAS,CAAC,UAAU,IAAI,CAAC,CAAC;gBACvD,CAAC;gBACD,MAAM;YACR;gBACE,MAAM,IAAI,iCAAa,CAAC,6BAA6B,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,eAAe,CAC3B,MAAsB,EACtB,UAAkB,EAClB,eAAgC;QAEhC,IAAI,UAAU,KAAK,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,kBAAkB,eAAe,CAAC,WAAW,GAAG,CAAC,CAAC;QACrE,CAAC;aAAM,IAAI,eAAe,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;YACtD,MAAM,CAAC,SAAS,CAAC,YAAY,eAAe,CAAC,WAAW,OAAO,UAAU,IAAI,CAAC,CAAC;QACjF,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,SAAS,CAAC,YAAY,UAAU,IAAI,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,MAAsB,EAAE,SAAoB;QACxE,IAAI,SAAS,CAAC,+BAA+B,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzD,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,KAAK,MAAM,8BAA8B,IAAI,SAAS,CAAC,+BAA+B,EAAE,CAAC;gBACvF,MAAM,CAAC,SAAS,CAAC,kBAAkB,8BAA8B,IAAI,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,oBAAoB,CAChC,SAAoB,EACpB,IAAU,EACV,cAA8B,EAC9B,gBAAgF;;QAEhF,MAAM,IAAI,GAAsB,IAAI,CAAC,IAAyB,CAAC;QAC/D,MAAM,gBAAgB,GAAgC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAE1F,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;gBAClC,2BAA2B;gBAE3B,MAAM,IAAI,iCAAa,CAAC,0CAA0C,CAAC,CAAC;YACtE,CAAC;YAED,IAAI,iBAAiB,GAAW,EAAE,CAAC;YAEnC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxD,kEAAkE;gBAClE,MAAM,gBAAgB,GAAW,IAAI,CAAC,QAAQ,CAAC,SAAS,CACtD,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CACnE,CAAC;gBACF,MAAM,mBAAmB,GAAW,IAAI,CAAC,QAAQ,CAAC,SAAS,CACzD,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CACtE,CAAC;gBAEF,IAAI,gBAAgB,GAAG,CAAC,IAAI,mBAAmB,IAAI,gBAAgB,EAAE,CAAC;oBACpE,MAAM,IAAI,iCAAa,CACrB,2BAA2B,IAAI,CAAC,OAAO,EAAE,IAAI;wBAC3C,yDAA2B,CAAC,iBAAiB,CAAC,IAAI,CAAC,CACtD,CAAC;gBACJ,CAAC;gBAED,MAAM,kBAAkB,GAAW,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC;gBAElG,wDAAwD;gBACxD,kBAAkB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;oBACvC,MAAM,mBAAmB,GAAmB,+BAAc,CAAC,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAAC;wBAC9F,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,4BAA4B,CAAC,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC;wBACvF,CAAC,CAAC,cAAc,CAAC;oBAEnB,gBAAgB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;gBACnD,CAAC,CAAC,CAAC;gBAEH,MAAM,oBAAoB,GAAa,kBAAkB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAC1E,SAAS,CAAC,eAAe,EAAE,CAC5B,CAAC;gBACF,iBAAiB,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAC7D,CAAC;YAED,MAAM,cAAc,GAAW,MAAA,MAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,0CAAG,CAAC,CAAC,mCAAI,EAAE,CAAC;YAExE,IACE,gBAAgB,CAAC,SAAS,YAAY,qBAAS;gBAC/C,gBAAgB,CAAC,SAAS,CAAC,UAAU,KAAK,yBAAa,CAAC,UAAU;gBAClE,gBAAgB,CAAC,SAAS,CAAC,UAAU,EACrC,CAAC;gBACD,gFAAgF;gBAChF,mEAAmE;gBACnE,MAAM,cAAc,GAAW,MAAA,MAAA,IAAI,CAAC,SAAS,0CAAE,OAAO,EAAE,mCAAI,EAAE,CAAC;gBAC/D,MAAM,qBAAqB,GAAW,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAClE,4BAA4B;gBAC5B,MAAM,oBAAoB,GACxB,qBAAqB,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAEpF,MAAM,WAAW,GAAW,GAAG,gBAAgB,CAAC,WAAW,GAAG,oBAAoB,GAAG,iBAAiB,GAAG,cAAc,EAAE,CAAC;gBAE1H,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,4CAA4C;gBAC5C,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,gBAAgB,CAAC,WAAW,GAAG,iBAAiB,GAAG,cAAc,EAAE,CAAC;YACpG,CAAC;QACH,CAAC;IACH,CAAC;CACF;AA3JD,wCA2JC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as ts from 'typescript';\n\nimport { InternalError } from '@rushstack/node-core-library';\nimport type { CollectorEntity } from '../collector/CollectorEntity';\nimport { AstImport, AstImportKind } from '../analyzer/AstImport';\nimport { AstDeclaration } from '../analyzer/AstDeclaration';\nimport type { Collector } from '../collector/Collector';\nimport type { Span } from '../analyzer/Span';\nimport type { IndentedWriter } from './IndentedWriter';\nimport { SourceFileLocationFormatter } from '../analyzer/SourceFileLocationFormatter';\n\n/**\n * Some common code shared between DtsRollupGenerator and ApiReportGenerator.\n */\nexport class DtsEmitHelpers {\n  public static emitImport(\n    writer: IndentedWriter,\n    collectorEntity: CollectorEntity,\n    astImport: AstImport\n  ): void {\n    const importPrefix: string = astImport.isTypeOnlyEverywhere ? 'import type' : 'import';\n\n    switch (astImport.importKind) {\n      case AstImportKind.DefaultImport:\n        if (collectorEntity.nameForEmit !== astImport.exportName) {\n          writer.write(`${importPrefix} { default as ${collectorEntity.nameForEmit} }`);\n        } else {\n          writer.write(`${importPrefix} ${astImport.exportName}`);\n        }\n        writer.writeLine(` from '${astImport.modulePath}';`);\n        break;\n      case AstImportKind.NamedImport:\n        if (collectorEntity.nameForEmit === astImport.exportName) {\n          writer.write(`${importPrefix} { ${astImport.exportName} }`);\n        } else {\n          writer.write(`${importPrefix} { ${astImport.exportName} as ${collectorEntity.nameForEmit} }`);\n        }\n        writer.writeLine(` from '${astImport.modulePath}';`);\n        break;\n      case AstImportKind.StarImport:\n        writer.writeLine(\n          `${importPrefix} * as ${collectorEntity.nameForEmit} from '${astImport.modulePath}';`\n        );\n        break;\n      case AstImportKind.EqualsImport:\n        writer.writeLine(\n          `${importPrefix} ${collectorEntity.nameForEmit} = require('${astImport.modulePath}');`\n        );\n        break;\n      case AstImportKind.ImportType:\n        if (!astImport.exportName) {\n          writer.writeLine(\n            `${importPrefix} * as ${collectorEntity.nameForEmit} from '${astImport.modulePath}';`\n          );\n        } else {\n          const topExportName: string = astImport.exportName.split('.')[0];\n          if (collectorEntity.nameForEmit === topExportName) {\n            writer.write(`${importPrefix} { ${topExportName} }`);\n          } else {\n            writer.write(`${importPrefix} { ${topExportName} as ${collectorEntity.nameForEmit} }`);\n          }\n          writer.writeLine(` from '${astImport.modulePath}';`);\n        }\n        break;\n      default:\n        throw new InternalError('Unimplemented AstImportKind');\n    }\n  }\n\n  public static emitNamedExport(\n    writer: IndentedWriter,\n    exportName: string,\n    collectorEntity: CollectorEntity\n  ): void {\n    if (exportName === ts.InternalSymbolName.Default) {\n      writer.writeLine(`export default ${collectorEntity.nameForEmit};`);\n    } else if (collectorEntity.nameForEmit !== exportName) {\n      writer.writeLine(`export { ${collectorEntity.nameForEmit} as ${exportName} }`);\n    } else {\n      writer.writeLine(`export { ${exportName} }`);\n    }\n  }\n\n  public static emitStarExports(writer: IndentedWriter, collector: Collector): void {\n    if (collector.starExportedExternalModulePaths.length > 0) {\n      writer.writeLine();\n      for (const starExportedExternalModulePath of collector.starExportedExternalModulePaths) {\n        writer.writeLine(`export * from \"${starExportedExternalModulePath}\";`);\n      }\n    }\n  }\n\n  public static modifyImportTypeSpan(\n    collector: Collector,\n    span: Span,\n    astDeclaration: AstDeclaration,\n    modifyNestedSpan: (childSpan: Span, childAstDeclaration: AstDeclaration) => void\n  ): void {\n    const node: ts.ImportTypeNode = span.node as ts.ImportTypeNode;\n    const referencedEntity: CollectorEntity | undefined = collector.tryGetEntityForNode(node);\n\n    if (referencedEntity) {\n      if (!referencedEntity.nameForEmit) {\n        // This should never happen\n\n        throw new InternalError('referencedEntry.nameForEmit is undefined');\n      }\n\n      let typeArgumentsText: string = '';\n\n      if (node.typeArguments && node.typeArguments.length > 0) {\n        // Type arguments have to be processed and written to the document\n        const lessThanTokenPos: number = span.children.findIndex(\n          (childSpan) => childSpan.node.kind === ts.SyntaxKind.LessThanToken\n        );\n        const greaterThanTokenPos: number = span.children.findIndex(\n          (childSpan) => childSpan.node.kind === ts.SyntaxKind.GreaterThanToken\n        );\n\n        if (lessThanTokenPos < 0 || greaterThanTokenPos <= lessThanTokenPos) {\n          throw new InternalError(\n            `Invalid type arguments: ${node.getText()}\\n` +\n              SourceFileLocationFormatter.formatDeclaration(node)\n          );\n        }\n\n        const typeArgumentsSpans: Span[] = span.children.slice(lessThanTokenPos + 1, greaterThanTokenPos);\n\n        // Apply modifications to Span elements of typeArguments\n        typeArgumentsSpans.forEach((childSpan) => {\n          const childAstDeclaration: AstDeclaration = AstDeclaration.isSupportedSyntaxKind(childSpan.kind)\n            ? collector.astSymbolTable.getChildAstDeclarationByNode(childSpan.node, astDeclaration)\n            : astDeclaration;\n\n          modifyNestedSpan(childSpan, childAstDeclaration);\n        });\n\n        const typeArgumentsStrings: string[] = typeArgumentsSpans.map((childSpan) =>\n          childSpan.getModifiedText()\n        );\n        typeArgumentsText = `<${typeArgumentsStrings.join(', ')}>`;\n      }\n\n      const separatorAfter: string = /(\\s*)$/.exec(span.getText())?.[1] ?? '';\n\n      if (\n        referencedEntity.astEntity instanceof AstImport &&\n        referencedEntity.astEntity.importKind === AstImportKind.ImportType &&\n        referencedEntity.astEntity.exportName\n      ) {\n        // For an ImportType with a namespace chain, only the top namespace is imported.\n        // Must add the original nested qualifiers to the rolled up import.\n        const qualifiersText: string = node.qualifier?.getText() ?? '';\n        const nestedQualifiersStart: number = qualifiersText.indexOf('.');\n        // Including the leading \".\"\n        const nestedQualifiersText: string =\n          nestedQualifiersStart >= 0 ? qualifiersText.substring(nestedQualifiersStart) : '';\n\n        const replacement: string = `${referencedEntity.nameForEmit}${nestedQualifiersText}${typeArgumentsText}${separatorAfter}`;\n\n        span.modification.skipAll();\n        span.modification.prefix = replacement;\n      } else {\n        // Replace with internal symbol or AstImport\n        span.modification.skipAll();\n        span.modification.prefix = `${referencedEntity.nameForEmit}${typeArgumentsText}${separatorAfter}`;\n      }\n    }\n  }\n}\n"]}
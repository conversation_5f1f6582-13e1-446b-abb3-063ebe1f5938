{"version": 3, "file": "DeclarationReferenceGenerator.js", "sourceRoot": "", "sources": ["../../src/generators/DeclarationReferenceGenerator.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+BAA+B;AAC/B,+CAAiC;AACjC,kGAMiE;AACjE,oEAAoF;AACpF,qEAAkE;AAClE,yEAAsE;AAGtE,uEAAoE;AAEpE,MAAa,6BAA6B;IAKxC,YAAmB,SAAoB;QACrC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,oCAAoC,CAAC,IAAmB;QAC7D,MAAM,MAAM,GAA0B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC5F,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,MAAM,YAAY,GAAY,6BAA6B,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YACzF,OAAO,CACL,IAAI,CAAC,gCAAgC,CACnC,MAAM,EACN,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAC1D;gBACD,IAAI,CAAC,gCAAgC,CACnC,MAAM,EACN,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAC1D;gBACD,IAAI,CAAC,gCAAgC,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CACxE,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,gCAAgC,CACrC,MAAiB,EACjB,OAAuB;QAEvB,OAAO,IAAI,CAAC,6BAA6B,CAAC,MAAM,EAAE,OAAO,EAAE,wBAAwB,CAAC,KAAK,CAAC,CAAC;IAC7F,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,IAAa;QACjD,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC;YAC7B,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB;gBACrC,OAAO,IAAI,CAAC;YACd,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC9B,OAAO,6BAA6B,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3E;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,MAAiB;QACtD,OAAO,CACL,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC;YAC7C,MAAM,CAAC,gBAAgB,KAAK,SAAS;YACrC,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,CAAC,CACzC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,IAA2B,EAAE,KAAgB;QACxE,OAAO,CACL,IAAI,KAAK,KAAK;YACd,CAAC,CAAC,CACA,IAAI;gBACJ,IAAI,CAAC,gBAAgB;gBACrB,KAAK,CAAC,gBAAgB;gBACtB,IAAI,CAAC,gBAAgB,KAAK,KAAK,CAAC,gBAAgB,CACjD,CACF,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,MAAiB;QAC9C,MAAM,WAAW,GAA+B,qCAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC7F,MAAM,UAAU,GAA8B,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,aAAa,EAAE,CAAC;QAC3E,MAAM,MAAM,GAA0B,yCAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAElF,wGAAwG;QACxG,2DAA2D;QAC3D,MAAM,QAAQ,GAAY,CAAC,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAC3E,MAAM,qBAAqB,GACzB,CAAC,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,+BAA+B,CAAC,UAAU,CAAC,CAAC;QACtF,IAAI,QAAQ,IAAI,qBAAqB,EAAE,CAAC;YACtC,IACE,MAAM;gBACN,MAAM,CAAC,OAAO;gBACd,6BAA6B,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,EAC3F,CAAC;gBACD,OAAO,iCAAU,CAAC,OAAO,CAAC;YAC5B,CAAC;YAED,OAAO,iCAAU,CAAC,OAAO,CAAC;QAC5B,CAAC;QAED,8FAA8F;QAC9F,8FAA8F;QAC9F,6FAA6F;QAC7F,yCAAyC;QACzC,MAAM,MAAM,GAAgC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAC1F,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,EAAE,CAAC;YACvB,OAAO,iCAAU,CAAC,OAAO,CAAC;QAC5B,CAAC;QAED,sGAAsG;QACtG,uGAAuG;QACvG,4CAA4C;QAC5C,IAAI,MAAM,IAAI,CAAC,6BAA6B,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7E,IACE,MAAM,CAAC,OAAO;gBACd,6BAA6B,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,EAC3F,CAAC;gBACD,OAAO,iCAAU,CAAC,OAAO,CAAC;YAC5B,CAAC;YAED,OAAO,iCAAU,CAAC,OAAO,CAAC;QAC5B,CAAC;QAED,mFAAmF;QACnF,EAAE;QACF,mFAAmF;QACnF,6DAA6D;QAC7D,OAAO,iCAAU,CAAC,MAAM,CAAC;IAC3B,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,MAAiB,EAAE,OAAuB;QAC3E,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAClD,OAAO,8BAAO,CAAC,KAAK,CAAC;QACvB,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YACjD,OAAO,8BAAO,CAAC,IAAI,CAAC;QACtB,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YACtD,OAAO,8BAAO,CAAC,SAAS,CAAC;QAC3B,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YACtD,OAAO,8BAAO,CAAC,SAAS,CAAC;QAC3B,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YACrD,OAAO,8BAAO,CAAC,QAAQ,CAAC;QAC1B,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YACrD,OAAO,8BAAO,CAAC,QAAQ,CAAC;QAC1B,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACnD,OAAO,8BAAO,CAAC,SAAS,CAAC;QAC3B,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YACxD,OAAO,8BAAO,CAAC,MAAM,CAAC;QACxB,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YACxD,OAAO,8BAAO,CAAC,WAAW,CAAC;QAC7B,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;YACvD,OAAO,8BAAO,CAAC,MAAM,CAAC;QACxB,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YACtD,IAAI,MAAM,CAAC,WAAW,KAAK,EAAE,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBACtD,OAAO,8BAAO,CAAC,aAAa,CAAC;YAC/B,CAAC;YACD,IAAI,MAAM,CAAC,WAAW,KAAK,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC;gBACrD,OAAO,8BAAO,CAAC,kBAAkB,CAAC;YACpC,CAAC;YACD,IAAI,MAAM,CAAC,WAAW,KAAK,EAAE,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;gBACvD,OAAO,8BAAO,CAAC,cAAc,CAAC;YAChC,CAAC;QACH,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;YAC1D,8EAA8E;YAC9E,MAAM,IAAI,iCAAa,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,6BAA6B,CACnC,MAAiB,EACjB,OAAuB,EACvB,oBAA6B;QAE7B,MAAM,WAAW,GAAwB,qCAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACtF,MAAM,UAAU,GAA8B,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,aAAa,EAAE,CAAC;QAE3E,IAAI,cAAc,GAAc,MAAM,CAAC;QACvC,IAAI,cAAc,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YACtD,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QACvF,CAAC;QACD,IAAI,cAAc,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAChD,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAE9E,+FAA+F;YAC/F,yFAAyF;YACzF,IAAI,cAAc,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;gBACtD,cAAc,GAAG,MAAM,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,IAAI,6BAA6B,CAAC,uBAAuB,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1E,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC1B,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,OAAO,IAAI,2CAAoB,CAAC,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9E,CAAC;QAED,gEAAgE;QAChE,IAAI,cAAc,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;YACxD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,SAAS,GAAqC,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;QAC3F,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,SAAS,GAAW,cAAc,CAAC,IAAI,CAAC;QAC5C,MAAM,MAAM,GAAgC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;QAClG,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,EAAE,CAAC;YACxB,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC;QACjC,CAAC;QAED,IAAI,cAAc,CAAC,WAAW,KAAK,EAAE,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;YACrE,SAAS,GAAG,aAAa,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,MAAM,aAAa,GAAuB,qCAAiB,CAAC,4BAA4B,CACtF,cAAc,CAAC,WAAW,CAC3B,CAAC;YACF,IAAI,aAAa,EAAE,CAAC;gBAClB,0FAA0F;gBAC1F,wFAAwF;gBACxF,SAAS,GAAG,aAAa,CAAC;YAC5B,CAAC;iBAAM,IAAI,qCAAiB,CAAC,kBAAkB,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5E,KAAK,MAAM,IAAI,IAAI,cAAc,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;oBACrD,MAAM,QAAQ,GAAmC,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;oBAC/E,IAAI,QAAQ,IAAI,EAAE,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACpD,MAAM,QAAQ,GAAuB,qCAAiB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;wBACrF,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;4BAC3B,SAAS,GAAG,QAAQ,CAAC;4BACrB,MAAM;wBACR,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,UAAU,GAAe,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAE3E,0DAA0D;QAC1D,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC,MAAM,KAAK,mCAAY,CAAC,QAAQ,EAAE,CAAC;YACjG,SAAS,GAAG,IAAI,2CAAoB,CAAC,mCAAY,CAAC,QAAQ,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,SAAS;aACb,iBAAiB,CAAC,UAAU,EAAE,SAAS,CAAC;aACxC,WAAW,CAAC,6BAA6B,CAAC,mBAAmB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;IAC7F,CAAC;IAEO,mBAAmB,CAAC,MAAiB;;QAC3C,MAAM,WAAW,GAAwB,qCAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACtF,MAAM,UAAU,GAA8B,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,aAAa,EAAE,CAAC;QAE3E,iGAAiG;QACjG,kGAAkG;QAClG,kFAAkF;QAClF,MAAM,MAAM,GAAgC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAC1F,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,MAAM,CAAC,sBAAsB,EAAE,CAAC;gBAClC,OAAO,IAAI,2CAAoB,CAAC,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC,CAAC;YAC9E,CAAC;YAED,MAAM,8BAA8B,GAClC,MAAM,CAAC,iCAAiC,EAAE,CAAC;YAC7C,IACE,8BAA8B;gBAC9B,8BAA8B,CAAC,SAAS,YAAY,uCAAkB,EACtE,CAAC;gBACD,MAAM,YAAY,GAA0B,yCAAmB,CAAC,0BAA0B,CACxF,8BAA8B,CAAC,SAAS,CAAC,WAAW,EACpD,IAAI,CAAC,UAAU,CAAC,WAAW,CAC5B,CAAC;gBACF,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO,IAAI,CAAC,6BAA6B,CACvC,YAAY,EACZ,YAAY,CAAC,KAAK;oBAClB,wBAAwB,CAAC,IAAI,CAC9B,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,yDAAyD;QACzD,MAAM,YAAY,GAA0B,yCAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACxF,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,6BAA6B,CACvC,YAAY,EACZ,YAAY,CAAC,KAAK;YAClB,wBAAwB,CAAC,IAAI,CAC9B,CAAC;QACJ,CAAC;QAED,8FAA8F;QAC9F,8EAA8E;QAC9E,EAAE;QACF,MAAM;QACN,uBAAuB;QACvB,4BAA4B;QAC5B,2DAA2D;QAC3D,IAAI;QACJ,MAAM;QACN,EAAE;QACF,+FAA+F;QAC/F,mFAAmF;QACnF,MAAM,WAAW,GAAwB,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,0CAAE,MAAM,CAAC;QACrE,IAAI,WAAW,IAAI,EAAE,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;YACvD,MAAM,iBAAiB,GAA0B,yCAAmB,CAAC,0BAA0B,CAC7F,WAAW,EACX,IAAI,CAAC,UAAU,CAAC,WAAW,CAC5B,CAAC;YACF,IAAI,iBAAiB,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,6BAA6B,CACvC,iBAAiB,EACjB,iBAAiB,CAAC,KAAK;gBACvB,wBAAwB,CAAC,IAAI,CAC9B,CAAC;YACJ,CAAC;QACH,CAAC;QAED,qDAAqD;QACrD,IAAI,UAAU,IAAI,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;YAClD,OAAO,IAAI,2CAAoB,CAAC,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,2CAAoB,CAAC,mCAAY,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,UAAyB;QAC/C,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,+BAA+B,CAAC,UAAU,CAAC,EAAE,CAAC;YACxE,MAAM,WAAW,GACf,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEnF,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;gBACpC,OAAO,WAAW,CAAC,IAAI,CAAC;YAC1B,CAAC;YACD,OAAO,6BAA6B,CAAC,gBAAgB,CAAC;QACxD,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC;IAC7C,CAAC;IAEO,yBAAyB,CAAC,UAAqC;QACrE,IAAI,UAAU,IAAI,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;YAClD,MAAM,WAAW,GAAW,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YAE7D,IAAI,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;gBACzD,gGAAgG;gBAChG,+FAA+F;gBAC/F,uCAAuC;gBACvC,OAAO,IAAI,mCAAY,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,mCAAY,CAAC,WAAW,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QACD,OAAO,mCAAY,CAAC,QAAQ,CAAC;IAC/B,CAAC;;AAvWH,sEAwWC;AAvWwB,8CAAgB,GAAW,GAAG,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\n/* eslint-disable no-bitwise */\nimport * as ts from 'typescript';\nimport {\n  DeclarationReference,\n  ModuleSource,\n  GlobalSource,\n  Navigation,\n  Meaning\n} from '@microsoft/tsdoc/lib-commonjs/beta/DeclarationReference';\nimport { type INodePackageJson, InternalError } from '@rushstack/node-core-library';\nimport { TypeScriptHelpers } from '../analyzer/TypeScriptHelpers';\nimport { TypeScriptInternals } from '../analyzer/TypeScriptInternals';\nimport type { Collector } from '../collector/Collector';\nimport type { CollectorEntity } from '../collector/CollectorEntity';\nimport { AstNamespaceImport } from '../analyzer/AstNamespaceImport';\n\nexport class DeclarationReferenceGenerator {\n  public static readonly unknownReference: string = '?';\n\n  private _collector: Collector;\n\n  public constructor(collector: Collector) {\n    this._collector = collector;\n  }\n\n  /**\n   * Gets the UID for a TypeScript Identifier that references a type.\n   */\n  public getDeclarationReferenceForIdentifier(node: ts.Identifier): DeclarationReference | undefined {\n    const symbol: ts.Symbol | undefined = this._collector.typeChecker.getSymbolAtLocation(node);\n    if (symbol !== undefined) {\n      const isExpression: boolean = DeclarationReferenceGenerator._isInExpressionContext(node);\n      return (\n        this.getDeclarationReferenceForSymbol(\n          symbol,\n          isExpression ? ts.SymbolFlags.Value : ts.SymbolFlags.Type\n        ) ||\n        this.getDeclarationReferenceForSymbol(\n          symbol,\n          isExpression ? ts.SymbolFlags.Type : ts.SymbolFlags.Value\n        ) ||\n        this.getDeclarationReferenceForSymbol(symbol, ts.SymbolFlags.Namespace)\n      );\n    }\n  }\n\n  /**\n   * Gets the DeclarationReference for a TypeScript Symbol for a given meaning.\n   */\n  public getDeclarationReferenceForSymbol(\n    symbol: ts.Symbol,\n    meaning: ts.SymbolFlags\n  ): DeclarationReference | undefined {\n    return this._symbolToDeclarationReference(symbol, meaning, /*includeModuleSymbols*/ false);\n  }\n\n  private static _isInExpressionContext(node: ts.Node): boolean {\n    switch (node.parent.kind) {\n      case ts.SyntaxKind.TypeQuery:\n      case ts.SyntaxKind.ComputedPropertyName:\n        return true;\n      case ts.SyntaxKind.QualifiedName:\n        return DeclarationReferenceGenerator._isInExpressionContext(node.parent);\n      default:\n        return false;\n    }\n  }\n\n  private static _isExternalModuleSymbol(symbol: ts.Symbol): boolean {\n    return (\n      !!(symbol.flags & ts.SymbolFlags.ValueModule) &&\n      symbol.valueDeclaration !== undefined &&\n      ts.isSourceFile(symbol.valueDeclaration)\n    );\n  }\n\n  private static _isSameSymbol(left: ts.Symbol | undefined, right: ts.Symbol): boolean {\n    return (\n      left === right ||\n      !!(\n        left &&\n        left.valueDeclaration &&\n        right.valueDeclaration &&\n        left.valueDeclaration === right.valueDeclaration\n      )\n    );\n  }\n\n  private _getNavigationToSymbol(symbol: ts.Symbol): Navigation {\n    const declaration: ts.Declaration | undefined = TypeScriptHelpers.tryGetADeclaration(symbol);\n    const sourceFile: ts.SourceFile | undefined = declaration?.getSourceFile();\n    const parent: ts.Symbol | undefined = TypeScriptInternals.getSymbolParent(symbol);\n\n    // If it's global or from an external library, then use either Members or Exports. It's not possible for\n    // global symbols or external library symbols to be Locals.\n    const isGlobal: boolean = !!sourceFile && !ts.isExternalModule(sourceFile);\n    const isFromExternalLibrary: boolean =\n      !!sourceFile && this._collector.program.isSourceFileFromExternalLibrary(sourceFile);\n    if (isGlobal || isFromExternalLibrary) {\n      if (\n        parent &&\n        parent.members &&\n        DeclarationReferenceGenerator._isSameSymbol(parent.members.get(symbol.escapedName), symbol)\n      ) {\n        return Navigation.Members;\n      }\n\n      return Navigation.Exports;\n    }\n\n    // Otherwise, this symbol is from the current package. If we've found an associated consumable\n    // `CollectorEntity`, then use Exports. We use `consumable` here instead of `exported` because\n    // if the symbol is exported from a non-consumable `AstNamespaceImport`, we don't want to use\n    // Exports. We should use Locals instead.\n    const entity: CollectorEntity | undefined = this._collector.tryGetEntityForSymbol(symbol);\n    if (entity?.consumable) {\n      return Navigation.Exports;\n    }\n\n    // If its parent symbol is not a source file, then use either Exports or Members. If the parent symbol\n    // is a source file, but it wasn't exported from the package entry point (in the check above), then the\n    // symbol is a local, so fall through below.\n    if (parent && !DeclarationReferenceGenerator._isExternalModuleSymbol(parent)) {\n      if (\n        parent.members &&\n        DeclarationReferenceGenerator._isSameSymbol(parent.members.get(symbol.escapedName), symbol)\n      ) {\n        return Navigation.Members;\n      }\n\n      return Navigation.Exports;\n    }\n\n    // Otherwise, we have a local symbol, so use a Locals navigation. These are either:\n    //\n    // 1. Symbols that are exported from a file module but not the package entry point.\n    // 2. Symbols that are not exported from their parent module.\n    return Navigation.Locals;\n  }\n\n  private static _getMeaningOfSymbol(symbol: ts.Symbol, meaning: ts.SymbolFlags): Meaning | undefined {\n    if (symbol.flags & meaning & ts.SymbolFlags.Class) {\n      return Meaning.Class;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.Enum) {\n      return Meaning.Enum;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.Interface) {\n      return Meaning.Interface;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.TypeAlias) {\n      return Meaning.TypeAlias;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.Function) {\n      return Meaning.Function;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.Variable) {\n      return Meaning.Variable;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.Module) {\n      return Meaning.Namespace;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.ClassMember) {\n      return Meaning.Member;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.Constructor) {\n      return Meaning.Constructor;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.EnumMember) {\n      return Meaning.Member;\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.Signature) {\n      if (symbol.escapedName === ts.InternalSymbolName.Call) {\n        return Meaning.CallSignature;\n      }\n      if (symbol.escapedName === ts.InternalSymbolName.New) {\n        return Meaning.ConstructSignature;\n      }\n      if (symbol.escapedName === ts.InternalSymbolName.Index) {\n        return Meaning.IndexSignature;\n      }\n    }\n    if (symbol.flags & meaning & ts.SymbolFlags.TypeParameter) {\n      // This should have already been handled in `getDeclarationReferenceOfSymbol`.\n      throw new InternalError('Not supported.');\n    }\n    return undefined;\n  }\n\n  private _symbolToDeclarationReference(\n    symbol: ts.Symbol,\n    meaning: ts.SymbolFlags,\n    includeModuleSymbols: boolean\n  ): DeclarationReference | undefined {\n    const declaration: ts.Node | undefined = TypeScriptHelpers.tryGetADeclaration(symbol);\n    const sourceFile: ts.SourceFile | undefined = declaration?.getSourceFile();\n\n    let followedSymbol: ts.Symbol = symbol;\n    if (followedSymbol.flags & ts.SymbolFlags.ExportValue) {\n      followedSymbol = this._collector.typeChecker.getExportSymbolOfSymbol(followedSymbol);\n    }\n    if (followedSymbol.flags & ts.SymbolFlags.Alias) {\n      followedSymbol = this._collector.typeChecker.getAliasedSymbol(followedSymbol);\n\n      // Without this logic, we end up following the symbol `ns` in `import * as ns from './file'` to\n      // the actual file `file.ts`. We don't want to do this, so revert to the original symbol.\n      if (followedSymbol.flags & ts.SymbolFlags.ValueModule) {\n        followedSymbol = symbol;\n      }\n    }\n\n    if (DeclarationReferenceGenerator._isExternalModuleSymbol(followedSymbol)) {\n      if (!includeModuleSymbols) {\n        return undefined;\n      }\n      return new DeclarationReference(this._sourceFileToModuleSource(sourceFile));\n    }\n\n    // Do not generate a declaration reference for a type parameter.\n    if (followedSymbol.flags & ts.SymbolFlags.TypeParameter) {\n      return undefined;\n    }\n\n    let parentRef: DeclarationReference | undefined = this._getParentReference(followedSymbol);\n    if (!parentRef) {\n      return undefined;\n    }\n\n    let localName: string = followedSymbol.name;\n    const entity: CollectorEntity | undefined = this._collector.tryGetEntityForSymbol(followedSymbol);\n    if (entity?.nameForEmit) {\n      localName = entity.nameForEmit;\n    }\n\n    if (followedSymbol.escapedName === ts.InternalSymbolName.Constructor) {\n      localName = 'constructor';\n    } else {\n      const wellKnownName: string | undefined = TypeScriptHelpers.tryDecodeWellKnownSymbolName(\n        followedSymbol.escapedName\n      );\n      if (wellKnownName) {\n        // TypeScript binds well-known ECMAScript symbols like 'Symbol.iterator' as '__@iterator'.\n        // This converts a string like '__@iterator' into the property name '[Symbol.iterator]'.\n        localName = wellKnownName;\n      } else if (TypeScriptHelpers.isUniqueSymbolName(followedSymbol.escapedName)) {\n        for (const decl of followedSymbol.declarations || []) {\n          const declName: ts.DeclarationName | undefined = ts.getNameOfDeclaration(decl);\n          if (declName && ts.isComputedPropertyName(declName)) {\n            const lateName: string | undefined = TypeScriptHelpers.tryGetLateBoundName(declName);\n            if (lateName !== undefined) {\n              localName = lateName;\n              break;\n            }\n          }\n        }\n      }\n    }\n\n    const navigation: Navigation = this._getNavigationToSymbol(followedSymbol);\n\n    // If the symbol is a global, ensure the source is global.\n    if (sourceFile && !ts.isExternalModule(sourceFile) && parentRef.source !== GlobalSource.instance) {\n      parentRef = new DeclarationReference(GlobalSource.instance);\n    }\n\n    return parentRef\n      .addNavigationStep(navigation, localName)\n      .withMeaning(DeclarationReferenceGenerator._getMeaningOfSymbol(followedSymbol, meaning));\n  }\n\n  private _getParentReference(symbol: ts.Symbol): DeclarationReference | undefined {\n    const declaration: ts.Node | undefined = TypeScriptHelpers.tryGetADeclaration(symbol);\n    const sourceFile: ts.SourceFile | undefined = declaration?.getSourceFile();\n\n    // Note that it's possible for a symbol to be exported from an entry point as well as one or more\n    // namespaces. In that case, it's not clear what to choose as its parent. Today's logic is neither\n    // perfect nor particularly stable to API items being renamed and shuffled around.\n    const entity: CollectorEntity | undefined = this._collector.tryGetEntityForSymbol(symbol);\n    if (entity) {\n      if (entity.exportedFromEntryPoint) {\n        return new DeclarationReference(this._sourceFileToModuleSource(sourceFile));\n      }\n\n      const firstExportingConsumableParent: CollectorEntity | undefined =\n        entity.getFirstExportingConsumableParent();\n      if (\n        firstExportingConsumableParent &&\n        firstExportingConsumableParent.astEntity instanceof AstNamespaceImport\n      ) {\n        const parentSymbol: ts.Symbol | undefined = TypeScriptInternals.tryGetSymbolForDeclaration(\n          firstExportingConsumableParent.astEntity.declaration,\n          this._collector.typeChecker\n        );\n        if (parentSymbol) {\n          return this._symbolToDeclarationReference(\n            parentSymbol,\n            parentSymbol.flags,\n            /*includeModuleSymbols*/ true\n          );\n        }\n      }\n    }\n\n    // Next, try to find a parent symbol via the symbol tree.\n    const parentSymbol: ts.Symbol | undefined = TypeScriptInternals.getSymbolParent(symbol);\n    if (parentSymbol) {\n      return this._symbolToDeclarationReference(\n        parentSymbol,\n        parentSymbol.flags,\n        /*includeModuleSymbols*/ true\n      );\n    }\n\n    // If that doesn't work, try to find a parent symbol via the node tree. As far as we can tell,\n    // this logic is only needed for local symbols within namespaces. For example:\n    //\n    // ```\n    // export namespace n {\n    //   type SomeType = number;\n    //   export function someFunction(): SomeType { return 5; }\n    // }\n    // ```\n    //\n    // In the example above, `SomeType` doesn't have a parent symbol per the TS internal API above,\n    // but its reference still needs to be qualified with the parent reference for `n`.\n    const grandParent: ts.Node | undefined = declaration?.parent?.parent;\n    if (grandParent && ts.isModuleDeclaration(grandParent)) {\n      const grandParentSymbol: ts.Symbol | undefined = TypeScriptInternals.tryGetSymbolForDeclaration(\n        grandParent,\n        this._collector.typeChecker\n      );\n      if (grandParentSymbol) {\n        return this._symbolToDeclarationReference(\n          grandParentSymbol,\n          grandParentSymbol.flags,\n          /*includeModuleSymbols*/ true\n        );\n      }\n    }\n\n    // At this point, we have a local symbol in a module.\n    if (sourceFile && ts.isExternalModule(sourceFile)) {\n      return new DeclarationReference(this._sourceFileToModuleSource(sourceFile));\n    } else {\n      return new DeclarationReference(GlobalSource.instance);\n    }\n  }\n\n  private _getPackageName(sourceFile: ts.SourceFile): string {\n    if (this._collector.program.isSourceFileFromExternalLibrary(sourceFile)) {\n      const packageJson: INodePackageJson | undefined =\n        this._collector.packageJsonLookup.tryLoadNodePackageJsonFor(sourceFile.fileName);\n\n      if (packageJson && packageJson.name) {\n        return packageJson.name;\n      }\n      return DeclarationReferenceGenerator.unknownReference;\n    }\n    return this._collector.workingPackage.name;\n  }\n\n  private _sourceFileToModuleSource(sourceFile: ts.SourceFile | undefined): GlobalSource | ModuleSource {\n    if (sourceFile && ts.isExternalModule(sourceFile)) {\n      const packageName: string = this._getPackageName(sourceFile);\n\n      if (this._collector.bundledPackageNames.has(packageName)) {\n        // The api-extractor.json config file has a \"bundledPackages\" setting, which causes imports from\n        // certain NPM packages to be treated as part of the working project.  In this case, we need to\n        // substitute the working package name.\n        return new ModuleSource(this._collector.workingPackage.name);\n      } else {\n        return new ModuleSource(packageName);\n      }\n    }\n    return GlobalSource.instance;\n  }\n}\n"]}
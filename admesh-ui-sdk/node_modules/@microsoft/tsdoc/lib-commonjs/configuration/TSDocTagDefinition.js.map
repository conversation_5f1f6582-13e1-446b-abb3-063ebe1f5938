{"version": 3, "file": "TSDocTagDefinition.js", "sourceRoot": "", "sources": ["../../src/configuration/TSDocTagDefinition.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,uDAAsD;AACtD,8DAA6D;AAE7D;;GAEG;AACH,IAAY,kBAiBX;AAjBD,WAAY,kBAAkB;IAC5B;;OAEG;IACH,qEAAS,CAAA;IAET;;;OAGG;IACH,mEAAQ,CAAA;IAER;;;OAGG;IACH,yEAAW,CAAA;AACb,CAAC,EAjBW,kBAAkB,kCAAlB,kBAAkB,QAiB7B;AAkBD;;GAEG;AACH;IA8BE,4BAAmB,UAAyC;QAC1D,2BAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC7D,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;QACxC,IAAI,CAAC,eAAe;YACjB,UAAoD,CAAC,eAAe,IAAI,iCAAe,CAAC,IAAI,CAAC;QAChG,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC;IAClD,CAAC;IAED;;OAEG;IACW,uCAAoB,GAAlC,UAAmC,OAAe;QAChD,2BAAY,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IACH,yBAAC;AAAD,CAAC,AA9CD,IA8CC;AA9CY,gDAAkB", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { StringChecks } from '../parser/StringChecks';\r\nimport { Standardization } from '../details/Standardization';\r\n\r\n/**\r\n * Determines the type of syntax for a TSDocTagDefinition\r\n */\r\nexport enum TSDocTagSyntaxKind {\r\n  /**\r\n   * The tag is intended to be an inline tag.  For example: `{@link}`.\r\n   */\r\n  InlineTag,\r\n\r\n  /**\r\n   * The tag is intended to be a block tag that starts a new documentation\r\n   * section.  For example: `@remarks`\r\n   */\r\n  BlockTag,\r\n\r\n  /**\r\n   * The tag is intended to be a modifier tag whose presence indicates\r\n   * an aspect of the associated API item.  For example: `@internal`\r\n   */\r\n  ModifierTag\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link TSDocTagDefinition}\r\n */\r\nexport interface ITSDocTagDefinitionParameters {\r\n  tagName: string;\r\n  syntaxKind: TSDocTagSyntaxKind;\r\n  allowMultiple?: boolean;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nexport interface ITSDocTagDefinitionInternalParameters extends ITSDocTagDefinitionParameters {\r\n  standardization: Standardization;\r\n}\r\n\r\n/**\r\n * Defines a TSDoc tag that will be understood by the TSDocParser.\r\n */\r\nexport class TSDocTagDefinition {\r\n  /**\r\n   * The TSDoc tag name.  TSDoc tag names start with an at-sign (`@`) followed\r\n   * by ASCII letters using \"camelCase\" capitalization.\r\n   */\r\n  public readonly tagName: string;\r\n\r\n  /**\r\n   * The TSDoc tag name in all capitals, which is used for performing\r\n   * case-insensitive comparisons or lookups.\r\n   */\r\n  public readonly tagNameWithUpperCase: string;\r\n\r\n  /**\r\n   * Specifies the expected syntax for this tag.\r\n   */\r\n  public readonly syntaxKind: TSDocTagSyntaxKind;\r\n\r\n  /**\r\n   * Indicates the level of support expected from documentation tools that implement\r\n   * the standard.\r\n   */\r\n  public readonly standardization: Standardization;\r\n\r\n  /**\r\n   * If true, then this TSDoc tag may appear multiple times in a doc comment.\r\n   * By default, a tag may only appear once.\r\n   */\r\n  public readonly allowMultiple: boolean;\r\n\r\n  public constructor(parameters: ITSDocTagDefinitionParameters) {\r\n    StringChecks.validateTSDocTagName(parameters.tagName);\r\n    this.tagName = parameters.tagName;\r\n    this.tagNameWithUpperCase = parameters.tagName.toUpperCase();\r\n    this.syntaxKind = parameters.syntaxKind;\r\n    this.standardization =\r\n      (parameters as ITSDocTagDefinitionInternalParameters).standardization || Standardization.None;\r\n    this.allowMultiple = !!parameters.allowMultiple;\r\n  }\r\n\r\n  /**\r\n   * Throws an exception if `tagName` is not a valid TSDoc tag name.\r\n   */\r\n  public static validateTSDocTagName(tagName: string): void {\r\n    StringChecks.validateTSDocTagName(tagName);\r\n  }\r\n}\r\n"]}
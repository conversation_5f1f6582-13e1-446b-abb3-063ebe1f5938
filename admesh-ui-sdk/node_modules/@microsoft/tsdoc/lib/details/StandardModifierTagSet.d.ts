import { ModifierTagSet } from './ModifierTagSet';
/**
 * Extends the ModifierTagSet base class with getters for modifiers that
 * are part of the standardized core tags for TSDoc.
 */
export declare class StandardModifierTagSet extends ModifierTagSet {
    /**
     * Returns true if the `@alpha` modifier tag was specified.
     */
    isAlpha(): boolean;
    /**
     * Returns true if the `@beta` modifier tag was specified.
     */
    isBeta(): boolean;
    /**
     * Returns true if the `@eventProperty` modifier tag was specified.
     */
    isEventProperty(): boolean;
    /**
     * Returns true if the `@experimental` modifier tag was specified.
     */
    isExperimental(): boolean;
    /**
     * Returns true if the `@internal` modifier tag was specified.
     */
    isInternal(): boolean;
    /**
     * Returns true if the `@override` modifier tag was specified.
     */
    isOverride(): boolean;
    /**
     * Returns true if the `@packageDocumentation` modifier tag was specified.
     */
    isPackageDocumentation(): boolean;
    /**
     * Returns true if the `@public` modifier tag was specified.
     */
    isPublic(): boolean;
    /**
     * Returns true if the `@readonly` modifier tag was specified.
     */
    isReadonly(): boolean;
    /**
     * Returns true if the `@sealed` modifier tag was specified.
     */
    isSealed(): boolean;
    /**
     * Returns true if the `@virtual` modifier tag was specified.
     */
    isVirtual(): boolean;
}
//# sourceMappingURL=StandardModifierTagSet.d.ts.map
{"version": 3, "file": "TSDocMessageId.js", "sourceRoot": "", "sources": ["../../src/parser/TSDocMessageId.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D;;;;;;;;;;GAUG;AACH,MAAM,CAAN,IAAY,cAmZX;AAnZD,WAAY,cAAc;IACxB;;;;OAIG;IACH,oEAAkD,CAAA;IAElD;;;;OAIG;IACH,iEAA+C,CAAA;IAE/C;;;;OAIG;IACH,iFAA+D,CAAA;IAE/D;;;;OAIG;IACH,qEAAmD,CAAA;IAEnD;;;;;OAKG;IACH,yEAAuD,CAAA;IAEvD;;;;OAIG;IACH,iFAA+D,CAAA;IAE/D;;;;OAIG;IACH,uEAAqD,CAAA;IAErD;;;;OAIG;IACH,gFAA8D,CAAA;IAE9D;;;;OAIG;IACH,4EAA0D,CAAA;IAE1D;;;OAGG;IACH,6DAA2C,CAAA;IAE3C;;OAEG;IACH,2FAAyE,CAAA;IAEzE;;OAEG;IACH,4FAA0E,CAAA;IAE1E;;OAEG;IACH,mEAAiD,CAAA;IAEjD;;OAEG;IACH,+DAA6C,CAAA;IAE7C;;OAEG;IACH,iEAA+C,CAAA;IAE/C;;OAEG;IACH,iFAA+D,CAAA;IAE/D;;OAEG;IACH,iFAA+D,CAAA;IAE/D;;OAEG;IACH,yFAAuE,CAAA;IAEvE;;OAEG;IACH,4EAA0D,CAAA;IAE1D;;OAEG;IACH,6EAA2D,CAAA;IAE3D;;OAEG;IACH,0DAAwC,CAAA;IAExC;;OAEG;IACH,sDAAoC,CAAA;IAEpC;;OAEG;IACH,+EAA6D,CAAA;IAE7D;;OAEG;IACH,gGAA8E,CAAA;IAE9E;;OAEG;IACH,+EAA6D,CAAA;IAE7D;;OAEG;IACH,0EAAwD,CAAA;IAExD;;;OAGG;IACH,sEAAoD,CAAA;IAEpD;;;OAGG;IACH,kDAAgC,CAAA;IAEhC;;OAEG;IACH,wDAAsC,CAAA;IAEtC;;OAEG;IACH,yEAAuD,CAAA;IAEvD;;;OAGG;IACH,mEAAiD,CAAA;IAEjD;;;OAGG;IACH,8EAA4D,CAAA;IAE5D;;OAEG;IACH,+DAA6C,CAAA;IAE7C;;OAEG;IACH,gFAA8D,CAAA;IAE9D;;OAEG;IACH,qFAAmE,CAAA;IAEnE;;OAEG;IACH,8EAA4D,CAAA;IAE5D;;OAEG;IACH,qEAAmD,CAAA;IAEnD;;OAEG;IACH,uDAAqC,CAAA;IAErC;;OAEG;IACH,wEAAsD,CAAA;IAEtD;;OAEG;IACH,gFAA8D,CAAA;IAE9D;;;;OAIG;IACH,kEAAgD,CAAA;IAEhD;;OAEG;IACH,uEAAqD,CAAA;IAErD;;OAEG;IACH,qEAAmD,CAAA;IAEnD;;;OAGG;IACH,0FAAwE,CAAA;IAExE;;;;OAIG;IACH,wFAAsE,CAAA;IAEtE;;OAEG;IACH,8DAA4C,CAAA;IAE5C;;OAEG;IACH,qEAAmD,CAAA;IAEnD;;OAEG;IACH,4FAA0E,CAAA;IAE1E;;OAEG;IACH,yEAAuD,CAAA;IAEvD;;OAEG;IACH,oFAAkE,CAAA;IAElE;;OAEG;IACH,yEAAuD,CAAA;IAEvD;;OAEG;IACH,wFAAsE,CAAA;IAEtE;;OAEG;IACH,yEAAuD,CAAA;IAEvD;;OAEG;IACH,+EAA6D,CAAA;IAE7D;;OAEG;IACH,mFAAiE,CAAA;IAEjE;;;;;OAKG;IACH,qFAAmE,CAAA;IAEnE;;OAEG;IACH,yEAAuD,CAAA;IAEvD;;;;;;OAMG;IACH,6EAA2D,CAAA;IAE3D;;OAEG;IACH,mFAAiE,CAAA;IAEjE;;OAEG;IACH,wEAAsD,CAAA;IAEtD;;OAEG;IACH,wEAAsD,CAAA;IAEtD;;OAEG;IACH,4EAA0D,CAAA;IAE1D;;OAEG;IACH,sEAAoD,CAAA;IAEpD;;OAEG;IACH,kEAAgD,CAAA;IAEhD;;;;OAIG;IACH,iEAA+C,CAAA;IAE/C;;OAEG;IACH,4EAA0D,CAAA;IAE1D;;OAEG;IACH,4EAA0D,CAAA;IAE1D;;OAEG;IACH,gFAA8D,CAAA;IAE9D;;OAEG;IACH,4EAA0D,CAAA;IAE1D;;OAEG;IACH,kFAAgE,CAAA;IAEhE;;OAEG;IACH,4EAA0D,CAAA;IAE1D;;OAEG;IACH,yDAAuC,CAAA;IAEvC;;OAEG;IACH,gFAA8D,CAAA;AAChE,CAAC,EAnZW,cAAc,KAAd,cAAc,QAmZzB;AAED,sDAAsD;AACtD,MAAM,CAAC,IAAM,kBAAkB,GAAa;IAC1C,4EAA4E;IAC5E,6BAA6B;IAC7B,2BAA2B;IAC3B,iCAAiC;IACjC,2BAA2B;IAC3B,6BAA6B;IAC7B,iCAAiC;IACjC,4BAA4B;IAC5B,iCAAiC;IACjC,+BAA+B;IAE/B,yBAAyB;IACzB,yCAAyC;IACzC,yCAAyC;IACzC,4BAA4B;IAC5B,0BAA0B;IAC1B,2BAA2B;IAC3B,mCAAmC;IACnC,mCAAmC;IACnC,uCAAuC;IACvC,iCAAiC;IACjC,kCAAkC;IAClC,uBAAuB;IACvB,qBAAqB;IACrB,mCAAmC;IACnC,4CAA4C;IAC5C,mCAAmC;IACnC,gCAAgC;IAChC,6BAA6B;IAC7B,mBAAmB;IACnB,uBAAuB;IACvB,gCAAgC;IAChC,4BAA4B;IAC5B,kCAAkC;IAClC,0BAA0B;IAC1B,mCAAmC;IACnC,sCAAsC;IACtC,kCAAkC;IAClC,6BAA6B;IAC7B,sBAAsB;IACtB,+BAA+B;IAC/B,mCAAmC;IACnC,4BAA4B;IAC5B,8BAA8B;IAC9B,6BAA6B;IAC7B,wCAAwC;IACxC,uCAAuC;IACvC,yBAAyB;IACzB,6BAA6B;IAC7B,yCAAyC;IACzC,+BAA+B;IAC/B,qCAAqC;IACrC,+BAA+B;IAC/B,uCAAuC;IACvC,+BAA+B;IAC/B,kCAAkC;IAClC,oCAAoC;IACpC,qCAAqC;IACrC,+BAA+B;IAC/B,iCAAiC;IACjC,qCAAqC;IACrC,+BAA+B;IAC/B,+BAA+B;IAC/B,iCAAiC;IACjC,8BAA8B;IAC9B,4BAA4B;IAC5B,2BAA2B;IAC3B,iCAAiC;IACjC,mCAAmC;IACnC,iCAAiC;IACjC,oCAAoC;IACpC,iCAAiC;IACjC,uBAAuB;IACvB,mCAAmC;CACpC,CAAC;AACF,kBAAkB,CAAC,IAAI,EAAE,CAAC;AAE1B,MAAM,CAAC,IAAM,qBAAqB,GAAwB,IAAI,GAAG,CAAS,kBAAkB,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\r\n\r\n/**\r\n * Unique identifiers for messages reported by the TSDoc parser.\r\n *\r\n * @remarks\r\n *\r\n * These strings are possible values for the {@link ParserMessage.messageId} property.\r\n * These identifiers can be used to suppress or configure the reporting of individual messages.\r\n * They are also useful when searching for help about a particular error.\r\n *\r\n * @public\r\n */\r\nexport enum TSDocMessageId {\r\n  /**\r\n   * File not found\r\n   * @remarks\r\n   * Reported by the `@microsoft/tsdoc-config` package when it failed to find a `tsdoc.json` file.\r\n   */\r\n  ConfigFileNotFound = 'tsdoc-config-file-not-found',\r\n\r\n  /**\r\n   * Error parsing JSON input: ___\r\n   * @remarks\r\n   * Reported by the `@microsoft/tsdoc-config` package when the `tsdoc.json` file has invalid JSON syntax.\r\n   */\r\n  ConfigInvalidJson = 'tsdoc-config-invalid-json',\r\n\r\n  /**\r\n   * Unsupported JSON \"$schema\" value\r\n   * @remarks\r\n   * Reported by the `@microsoft/tsdoc-config` package when the file format is not supported.\r\n   */\r\n  ConfigFileUnsupportedSchema = 'tsdoc-config-unsupported-schema',\r\n\r\n  /**\r\n   * Error loading config file: ___\r\n   * @remarks\r\n   * Reported by the `@microsoft/tsdoc-config` package when the config file doesn't conform to its schema.\r\n   */\r\n  ConfigFileSchemaError = 'tsdoc-config-schema-error',\r\n\r\n  /**\r\n   * Circular reference encountered for \"extends\" field of \"___\"\r\n   * @remarks\r\n   * Reported by the `@microsoft/tsdoc-config` package when the \"extends\" field creates a chain of references\r\n   * that causes a file to indirectly extend itself.\r\n   */\r\n  ConfigFileCyclicExtends = 'tsdoc-config-cyclic-extends',\r\n\r\n  /**\r\n   * Unable to resolve \"extends\" reference to \"___\"\r\n   * @remarks\r\n   * Reported by the `@microsoft/tsdoc-config` package when module resolution fails for the \"extends\" field.\r\n   */\r\n  ConfigFileUnresolvedExtends = 'tsdoc-config-unresolved-extends',\r\n\r\n  /**\r\n   * The \"supportForTags\" field refers to an undefined tag \"___\".\r\n   * @remarks\r\n   * Reported by the `@microsoft/tsdoc-config` package when loading the tsdoc.json config file.\r\n   */\r\n  ConfigFileUndefinedTag = 'tsdoc-config-undefined-tag',\r\n\r\n  /**\r\n   * The \"tagDefinitions\" field specifies more than one tag with the name \"___\".\r\n   * @remarks\r\n   * Reported by the `@microsoft/tsdoc-config` package when loading the tsdoc.json config file.\r\n   */\r\n  ConfigFileDuplicateTagName = 'tsdoc-config-duplicate-tag-name',\r\n\r\n  /**\r\n   * A TSDoc tag name must start with a letter and contain only letters and numbers.\r\n   * @remarks\r\n   * Reported by the `@microsoft/tsdoc-config` package when loading the tsdoc.json config file.\r\n   */\r\n  ConfigFileInvalidTagName = 'tsdoc-config-invalid-tag-name',\r\n\r\n  /**\r\n   * Expecting a `/**` comment.\r\n   * Unexpected end of input.\r\n   */\r\n  CommentNotFound = 'tsdoc-comment-not-found',\r\n\r\n  /**\r\n   * Expecting a leading `/**`\r\n   */\r\n  CommentOpeningDelimiterSyntax = 'tsdoc-comment-missing-opening-delimiter',\r\n\r\n  /**\r\n   * Unexpected end of input.\r\n   */\r\n  CommentMissingClosingDelimiter = 'tsdoc-comment-missing-closing-delimiter',\r\n\r\n  /**\r\n   * A doc comment cannot have more than one `@inheritDoc` tag\r\n   */\r\n  ExtraInheritDocTag = 'tsdoc-extra-inheritdoc-tag',\r\n\r\n  /**\r\n   * The `}` character should be escaped using a backslash to avoid confusion with a TSDoc inline tag.\r\n   */\r\n  EscapeRightBrace = 'tsdoc-escape-right-brace',\r\n\r\n  /**\r\n   * The `>` character should be escaped using a backslash to avoid confusion with an HTML tag.\r\n   */\r\n  EscapeGreaterThan = 'tsdoc-escape-greater-than',\r\n\r\n  /**\r\n   * The ___ block must include a deprecation message, e.g. describing the recommended alternative.\r\n   */\r\n  MissingDeprecationMessage = 'tsdoc-missing-deprecation-message',\r\n\r\n  /**\r\n   * A ___ block must not be used, because that content is provided by the `@inheritDoc` tag.\r\n   */\r\n  InheritDocIncompatibleTag = 'tsdoc-inheritdoc-incompatible-tag',\r\n\r\n  /**\r\n   * The summary section must not have any content, because that content is provided by the `@inheritDoc` tag.\r\n   */\r\n  InheritDocIncompatibleSummary = 'tsdoc-inheritdoc-incompatible-summary',\r\n\r\n  /**\r\n   * The TSDoc tag ___ is an inline tag; it must be enclosed in `{ }` braces.\r\n   */\r\n  InlineTagMissingBraces = 'tsdoc-inline-tag-missing-braces',\r\n\r\n  /**\r\n   * The TSDoc tag ___ is not an inline tag; it must not be enclosed in `{ }` braces.\r\n   */\r\n  TagShouldNotHaveBraces = 'tsdoc-tag-should-not-have-braces',\r\n\r\n  /**\r\n   * The TSDoc tag ___ is not supported by this tool.\r\n   */\r\n  UnsupportedTag = 'tsdoc-unsupported-tag',\r\n\r\n  /**\r\n   * The TSDoc tag ___ is not defined in this configuration.\r\n   */\r\n  UndefinedTag = 'tsdoc-undefined-tag',\r\n\r\n  /**\r\n   * The `@param` block should not include a JSDoc-style `{type}`.\r\n   */\r\n  ParamTagWithInvalidType = 'tsdoc-param-tag-with-invalid-type',\r\n\r\n  /**\r\n   * The `@param` block should not include a JSDoc-style optional name; it must not be enclosed in `[ ]` brackets.\r\n   */\r\n  ParamTagWithInvalidOptionalName = 'tsdoc-param-tag-with-invalid-optional-name',\r\n\r\n  /**\r\n   * The `@param` block should be followed by a parameter name.\r\n   */\r\n  ParamTagWithInvalidName = 'tsdoc-param-tag-with-invalid-name',\r\n\r\n  /**\r\n   * The `@param` block should be followed by a parameter name and then a hyphen.\r\n   */\r\n  ParamTagMissingHyphen = 'tsdoc-param-tag-missing-hyphen',\r\n\r\n  /**\r\n   * A backslash must precede another character that is being escaped.  OR\r\n   * A backslash can only be used to escape a punctuation character.\r\n   */\r\n  UnnecessaryBackslash = 'tsdoc-unnecessary-backslash',\r\n\r\n  /**\r\n   * Expecting a TSDoc tag starting with `@`.  OR\r\n   * Expecting a TSDoc tag starting with `{`.\r\n   */\r\n  MissingTag = 'tsdoc-missing-tag',\r\n\r\n  /**\r\n   * The `@` character looks like part of a TSDoc tag; use a backslash to escape it.\r\n   */\r\n  AtSignInWord = 'tsdoc-at-sign-in-word',\r\n\r\n  /**\r\n   * Expecting a TSDoc tag name after `@`; if it is not a tag, use a backslash to escape this character.\r\n   */\r\n  AtSignWithoutTagName = 'tsdoc-at-sign-without-tag-name',\r\n\r\n  /**\r\n   * Expecting a TSDoc tag starting with `{@`.  OR\r\n   * Expecting a TSDoc inline tag name after the `{@` characters.\r\n   */\r\n  MalformedInlineTag = 'tsdoc-malformed-inline-tag',\r\n\r\n  /**\r\n   * The token ___ looks like a TSDoc tag but contains an invalid character ___; if it is not a tag,\r\n   * use a backslash to escape the `@`.\r\n   */\r\n  CharactersAfterBlockTag = 'tsdoc-characters-after-block-tag',\r\n\r\n  /**\r\n   * A TSDoc tag name must start with a letter and contain only letters and numbers.\r\n   */\r\n  MalformedTagName = 'tsdoc-malformed-tag-name',\r\n\r\n  /**\r\n   * The character ___ cannot appear after the TSDoc tag name; expecting a space.\r\n   */\r\n  CharactersAfterInlineTag = 'tsdoc-characters-after-inline-tag',\r\n\r\n  /**\r\n   * The TSDoc inline tag name is missing its closing `}`.\r\n   */\r\n  InlineTagMissingRightBrace = 'tsdoc-inline-tag-missing-right-brace',\r\n\r\n  /**\r\n   * The `{` character must be escaped with a backslash when used inside a TSDoc inline tag.\r\n   */\r\n  InlineTagUnescapedBrace = 'tsdoc-inline-tag-unescaped-brace',\r\n\r\n  /**\r\n   * Unexpected character after declaration reference.\r\n   */\r\n  InheritDocTagSyntax = 'tsdoc-inheritdoc-tag-syntax',\r\n\r\n  /**\r\n   * The `@link` tag content is missing.\r\n   */\r\n  LinkTagEmpty = 'tsdoc-link-tag-empty',\r\n\r\n  /**\r\n   * The ___ character may not be used in the link text without escaping it.\r\n   */\r\n  LinkTagUnescapedText = 'tsdoc-link-tag-unescaped-text',\r\n\r\n  /**\r\n   * Unexpected character after link destination.\r\n   */\r\n  LinkTagDestinationSyntax = 'tsdoc-link-tag-destination-syntax',\r\n\r\n  /**\r\n   * The URL cannot be empty.  OR\r\n   * An `@link` URL must begin with a scheme comprised only of letters and numbers followed by `://`.  OR\r\n   * An `@link` URL must have at least one character after `://`.\r\n   */\r\n  LinkTagInvalidUrl = 'tsdoc-link-tag-invalid-url',\r\n\r\n  /**\r\n   * The declaration reference appears to contain a package name or import path, but it is missing the `#` delimiter.\r\n   */\r\n  ReferenceMissingHash = 'tsdoc-reference-missing-hash',\r\n\r\n  /**\r\n   * The hash character must be preceded by a package name or import path.\r\n   */\r\n  ReferenceHashSyntax = 'tsdoc-reference-hash-syntax',\r\n\r\n  /**\r\n   * The package name cannot be an empty string.  OR\r\n   * The package name ___ is not a valid package name.\r\n   */\r\n  ReferenceMalformedPackageName = 'tsdoc-reference-malformed-package-name',\r\n\r\n  /**\r\n   * An import path must not contain `//`.  OR\r\n   * An import path must not end with `/`.  OR\r\n   * An import path must not start with `/` unless prefixed by a package name.\r\n   */\r\n  ReferenceMalformedImportPath = 'tsdoc-reference-malformed-import-path',\r\n\r\n  /**\r\n   * Expecting a declaration reference.\r\n   */\r\n  MissingReference = 'tsdoc-missing-reference',\r\n\r\n  /**\r\n   * Expecting a period before the next component of a declaration reference\r\n   */\r\n  ReferenceMissingDot = 'tsdoc-reference-missing-dot',\r\n\r\n  /**\r\n   * Syntax error in declaration reference: the member selector must be enclosed in parentheses.\r\n   */\r\n  ReferenceSelectorMissingParens = 'tsdoc-reference-selector-missing-parens',\r\n\r\n  /**\r\n   * Expecting a colon after the identifier because the expression is in parentheses.\r\n   */\r\n  ReferenceMissingColon = 'tsdoc-reference-missing-colon',\r\n\r\n  /**\r\n   * Expecting a matching right parenthesis.\r\n   */\r\n  ReferenceMissingRightParen = 'tsdoc-reference-missing-right-paren',\r\n\r\n  /**\r\n   * Missing declaration reference in symbol reference\r\n   */\r\n  ReferenceSymbolSyntax = 'tsdoc-reference-symbol-syntax',\r\n\r\n  /**\r\n   * Missing closing square bracket for symbol reference\r\n   */\r\n  ReferenceMissingRightBracket = 'tsdoc-reference-missing-right-bracket',\r\n\r\n  /**\r\n   * Unexpected end of input inside quoted member identifier.\r\n   */\r\n  ReferenceMissingQuote = 'tsdoc-reference-missing-quote',\r\n\r\n  /**\r\n   * The quoted identifier cannot be empty.\r\n   */\r\n  ReferenceEmptyIdentifier = 'tsdoc-reference-empty-identifier',\r\n\r\n  /**\r\n   * Syntax error in declaration reference: expecting a member identifier.\r\n   */\r\n  ReferenceMissingIdentifier = 'tsdoc-reference-missing-identifier',\r\n\r\n  /**\r\n   * The identifier cannot be an empty string. OR\r\n   * The identifier cannot non-word characters. OR\r\n   * The identifier must not start with a number. OR\r\n   * The identifier ___ must be quoted because it is a TSDoc system selector name.\r\n   */\r\n  ReferenceUnquotedIdentifier = 'tsdoc-reference-unquoted-identifier',\r\n\r\n  /**\r\n   * Expecting a selector label after the colon.\r\n   */\r\n  ReferenceMissingLabel = 'tsdoc-reference-missing-label',\r\n\r\n  /**\r\n   * The selector cannot be an empty string.  OR\r\n   * If the selector begins with a number, it must be a positive integer value.  OR\r\n   * A label selector must be comprised of upper case letters, numbers, and underscores\r\n   * and must not start with a number.  OR\r\n   * The selector ___ is not a recognized TSDoc system selector name.\r\n   */\r\n  ReferenceSelectorSyntax = 'tsdoc-reference-selector-syntax',\r\n\r\n  /**\r\n   * Expecting an attribute or `>` or `/>`.\r\n   */\r\n  HtmlTagMissingGreaterThan = 'tsdoc-html-tag-missing-greater-than',\r\n\r\n  /**\r\n   * Expecting `=` after HTML attribute name.\r\n   */\r\n  HtmlTagMissingEquals = 'tsdoc-html-tag-missing-equals',\r\n\r\n  /**\r\n   * Expecting an HTML string starting with a single-quote or double-quote character.\r\n   */\r\n  HtmlTagMissingString = 'tsdoc-html-tag-missing-string',\r\n\r\n  /**\r\n   * The HTML string is missing its closing quote.\r\n   */\r\n  HtmlStringMissingQuote = 'tsdoc-html-string-missing-quote',\r\n\r\n  /**\r\n   * The next character after a closing quote must be spacing or punctuation.\r\n   */\r\n  TextAfterHtmlString = 'tsdoc-text-after-html-string',\r\n\r\n  /**\r\n   * Expecting an HTML tag starting with `</`.\r\n   */\r\n  MissingHtmlEndTag = 'tsdoc-missing-html-end-tag',\r\n\r\n  /**\r\n   * A space is not allowed here.  OR\r\n   * Expecting an HTML name.  OR\r\n   * An HTML name must be a sequence of letters separated by hyphens.\r\n   */\r\n  MalformedHtmlName = 'tsdoc-malformed-html-name',\r\n\r\n  /**\r\n   * This HTML element name is not defined by your TSDoc configuration.\r\n   */\r\n  UnsupportedHtmlElementName = 'tsdoc-unsupported-html-name',\r\n\r\n  /**\r\n   * The opening backtick for a code fence must appear at the start of the line.\r\n   */\r\n  CodeFenceOpeningIndent = 'tsdoc-code-fence-opening-indent',\r\n\r\n  /**\r\n   * The language specifier cannot contain backtick characters.\r\n   */\r\n  CodeFenceSpecifierSyntax = 'tsdoc-code-fence-specifier-syntax',\r\n\r\n  /**\r\n   * The closing delimiter for a code fence must not be indented.\r\n   */\r\n  CodeFenceClosingIndent = 'tsdoc-code-fence-closing-indent',\r\n\r\n  /**\r\n   * Missing closing delimiter.\r\n   */\r\n  CodeFenceMissingDelimiter = 'tsdoc-code-fence-missing-delimiter',\r\n\r\n  /**\r\n   * Unexpected characters after closing delimiter for code fence.\r\n   */\r\n  CodeFenceClosingSyntax = 'tsdoc-code-fence-closing-syntax',\r\n\r\n  /**\r\n   * A code span must contain at least one character between the backticks.\r\n   */\r\n  CodeSpanEmpty = 'tsdoc-code-span-empty',\r\n\r\n  /**\r\n   * The code span is missing its closing backtick.\r\n   */\r\n  CodeSpanMissingDelimiter = 'tsdoc-code-span-missing-delimiter'\r\n}\r\n\r\n// Exposed via TSDocConfiguration.allTsdocMessageIds()\r\nexport const allTsdocMessageIds: string[] = [\r\n  // To make comparisons easy, keep these in the same order as the enum above:\r\n  'tsdoc-config-file-not-found',\r\n  'tsdoc-config-invalid-json',\r\n  'tsdoc-config-unsupported-schema',\r\n  'tsdoc-config-schema-error',\r\n  'tsdoc-config-cyclic-extends',\r\n  'tsdoc-config-unresolved-extends',\r\n  'tsdoc-config-undefined-tag',\r\n  'tsdoc-config-duplicate-tag-name',\r\n  'tsdoc-config-invalid-tag-name',\r\n\r\n  'tsdoc-comment-not-found',\r\n  'tsdoc-comment-missing-opening-delimiter',\r\n  'tsdoc-comment-missing-closing-delimiter',\r\n  'tsdoc-extra-inheritdoc-tag',\r\n  'tsdoc-escape-right-brace',\r\n  'tsdoc-escape-greater-than',\r\n  'tsdoc-missing-deprecation-message',\r\n  'tsdoc-inheritdoc-incompatible-tag',\r\n  'tsdoc-inheritdoc-incompatible-summary',\r\n  'tsdoc-inline-tag-missing-braces',\r\n  'tsdoc-tag-should-not-have-braces',\r\n  'tsdoc-unsupported-tag',\r\n  'tsdoc-undefined-tag',\r\n  'tsdoc-param-tag-with-invalid-type',\r\n  'tsdoc-param-tag-with-invalid-optional-name',\r\n  'tsdoc-param-tag-with-invalid-name',\r\n  'tsdoc-param-tag-missing-hyphen',\r\n  'tsdoc-unnecessary-backslash',\r\n  'tsdoc-missing-tag',\r\n  'tsdoc-at-sign-in-word',\r\n  'tsdoc-at-sign-without-tag-name',\r\n  'tsdoc-malformed-inline-tag',\r\n  'tsdoc-characters-after-block-tag',\r\n  'tsdoc-malformed-tag-name',\r\n  'tsdoc-characters-after-inline-tag',\r\n  'tsdoc-inline-tag-missing-right-brace',\r\n  'tsdoc-inline-tag-unescaped-brace',\r\n  'tsdoc-inheritdoc-tag-syntax',\r\n  'tsdoc-link-tag-empty',\r\n  'tsdoc-link-tag-unescaped-text',\r\n  'tsdoc-link-tag-destination-syntax',\r\n  'tsdoc-link-tag-invalid-url',\r\n  'tsdoc-reference-missing-hash',\r\n  'tsdoc-reference-hash-syntax',\r\n  'tsdoc-reference-malformed-package-name',\r\n  'tsdoc-reference-malformed-import-path',\r\n  'tsdoc-missing-reference',\r\n  'tsdoc-reference-missing-dot',\r\n  'tsdoc-reference-selector-missing-parens',\r\n  'tsdoc-reference-missing-colon',\r\n  'tsdoc-reference-missing-right-paren',\r\n  'tsdoc-reference-symbol-syntax',\r\n  'tsdoc-reference-missing-right-bracket',\r\n  'tsdoc-reference-missing-quote',\r\n  'tsdoc-reference-empty-identifier',\r\n  'tsdoc-reference-missing-identifier',\r\n  'tsdoc-reference-unquoted-identifier',\r\n  'tsdoc-reference-missing-label',\r\n  'tsdoc-reference-selector-syntax',\r\n  'tsdoc-html-tag-missing-greater-than',\r\n  'tsdoc-html-tag-missing-equals',\r\n  'tsdoc-html-tag-missing-string',\r\n  'tsdoc-html-string-missing-quote',\r\n  'tsdoc-text-after-html-string',\r\n  'tsdoc-missing-html-end-tag',\r\n  'tsdoc-malformed-html-name',\r\n  'tsdoc-code-fence-opening-indent',\r\n  'tsdoc-code-fence-specifier-syntax',\r\n  'tsdoc-code-fence-closing-indent',\r\n  'tsdoc-code-fence-missing-delimiter',\r\n  'tsdoc-code-fence-closing-syntax',\r\n  'tsdoc-code-span-empty',\r\n  'tsdoc-code-span-missing-delimiter'\r\n];\r\nallTsdocMessageIds.sort();\r\n\r\nexport const allTsdocMessageIdsSet: ReadonlySet<string> = new Set<string>(allTsdocMessageIds);\r\n"]}
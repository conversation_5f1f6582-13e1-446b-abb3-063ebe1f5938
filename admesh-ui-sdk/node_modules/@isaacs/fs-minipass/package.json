{"name": "@isaacs/fs-minipass", "version": "4.0.1", "main": "./dist/commonjs/index.js", "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/npm/fs-minipass.git"}, "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^7.0.4"}, "devDependencies": {"@types/node": "^20.11.30", "mutate-fs": "^2.1.1", "prettier": "^3.2.5", "tap": "^18.7.1", "tshy": "^1.12.0", "typedoc": "^0.25.12"}, "files": ["dist"], "engines": {"node": ">=18.0.0"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}}
{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,8CAA2B;AAC3B,yCAAyC;AAAhC,8FAAA,MAAM,OAAK;AACpB,+CAA4B;AAC5B,2CAA2C;AAAlC,+FAAA,OAAO,OAAK;AACrB,8CAA2B;AAC3B,4CAAyB;AACzB,qCAAqC;AAA5B,4FAAA,IAAI,OAAK;AAClB,UAAU;AACV,4CAAyB;AACzB,6CAA0B;AAC1B,2CAAwB;AACxB,kDAA+B;AAC/B,+CAA4B;AAC5B,2CAA2C;AAAlC,+FAAA,OAAO,OAAK;AACrB,oDAAmC;AACnC,8CAA2B;AAC3B,8CAA2B;AAC3B,yCAAyC;AAAhC,8FAAA,MAAM,OAAK;AACpB,mDAAgC", "sourcesContent": ["export {\n  type TarOptionsWithAliasesAsync,\n  type TarOptionsWithAliasesAsyncFile,\n  type TarOptionsWithAliasesAsyncNoFile,\n  type TarOptionsWithAliasesSyncNoFile,\n  type TarOptionsWithAliases,\n  type TarOptionsWithAliasesFile,\n  type TarOptionsWithAliasesSync,\n  type TarOptionsWithAliasesSyncFile,\n} from './options.js'\n\nexport * from './create.js'\nexport { create as c } from './create.js'\nexport * from './extract.js'\nexport { extract as x } from './extract.js'\nexport * from './header.js'\nexport * from './list.js'\nexport { list as t } from './list.js'\n// classes\nexport * from './pack.js'\nexport * from './parse.js'\nexport * from './pax.js'\nexport * from './read-entry.js'\nexport * from './replace.js'\nexport { replace as r } from './replace.js'\nexport * as types from './types.js'\nexport * from './unpack.js'\nexport * from './update.js'\nexport { update as u } from './update.js'\nexport * from './write-entry.js'\n"]}
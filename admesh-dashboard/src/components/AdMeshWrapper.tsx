"use client";

import { useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import AdMeshLayout to handle ESM module
const AdMeshLayout = dynamic(() => import('admesh-ui-sdk').then(mod => ({ default: mod.AdMeshLayout })), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-gray-200 rounded-lg h-48"></div>
});

interface AdMeshWrapperProps {
  recommendations: any[];
  intentType?: string;
  theme?: { mode: string };
  maxDisplayed?: number;
  showMatchScores?: boolean;
  showFeatures?: boolean;
  autoLayout?: boolean;
  onProductClick?: (adId: string, admeshLink: string) => void;
  onTrackView?: (data: { adId: string; productId?: string }) => void;
}

export default function AdMeshWrapper(props: AdMeshWrapperProps) {
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Dynamically load AdMesh UI SDK styles only for this component
    const loadStyles = async () => {
      if (wrapperRef.current) {
        // Create a style element for scoped styles
        const styleElement = document.createElement('style');
        styleElement.id = 'admesh-ui-sdk-styles';
        
        // Check if styles are already loaded
        if (!document.getElementById('admesh-ui-sdk-styles')) {
          try {
            // Import the CSS as text and scope it
            const response = await fetch('/node_modules/admesh-ui-sdk/dist/admesh-ui-sdk.css');
            if (response.ok) {
              const cssText = await response.text();
              // Scope all CSS rules to our wrapper
              const scopedCSS = cssText.replace(/([^{}]+){/g, (match, selector) => {
                // Skip @media, @keyframes, etc.
                if (selector.trim().startsWith('@')) return match;
                // Scope the selector to our wrapper
                return `.admesh-ui-wrapper ${selector.trim()} {`;
              });
              styleElement.textContent = scopedCSS;
              document.head.appendChild(styleElement);
            }
          } catch (error) {
            console.warn('Could not load AdMesh UI SDK styles:', error);
          }
        }
      }
    };

    loadStyles();

    // Cleanup function
    return () => {
      const existingStyle = document.getElementById('admesh-ui-sdk-styles');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);

  return (
    <div ref={wrapperRef} className="admesh-ui-wrapper">
      <AdMeshLayout {...props} />
    </div>
  );
}
